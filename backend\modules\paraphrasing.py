import json
import re
from http import HTTPStatus
from typing import Any, Dict, <PERSON><PERSON>
from venv import logger
from modules.text_utils import extract_json, sanitize_paraphrases
from modules.api_calls import call_openrouter_api

def get_paraphrase_instruction(text: str, tone: str = "Standard", synonym_level: int = 2) -> Tuple[str, str]:
    """Generate prompt and system instruction for paraphrasing."""
    synonym_level = min(max(synonym_level, 0), 4)
    system_instruction = (
        f"Break the text into sentences and paraphrase each sentence in a {tone} tone. "
        f"Provide 3 paraphrased versions per sentence, adjusting vocabulary based on a synonym frequency level "
        f"(0 = minimal changes, 4 = maximum synonym use). Synonym level: {synonym_level}. "
        f"For each paraphrased version, include a 'vocabulary' field that is an array of objects, each with 'original' and 'replacement' keys "
        f"indicating the words that were changed. Also, include a 'sentence_structure' field describing any changes to the sentence structure. "
        f"Return the result in this strict JSON format:\n"
        "```json\n"
        "{\n"
        "  \"paraphrases\": [\n"
        "    {\n"
        "      \"sentence\": \"original sentence\",\n"
        "      \"versions\": [\n"
        "        {\n"
        "          \"text\": \"paraphrased sentence\",\n"
        "          \"vocabulary\": [\n"
        "            {\"original\": \"word1\", \"replacement\": \"synonym1\"},\n"
        "            {\"original\": \"word2\", \"replacement\": \"synonym2\"}\n"
        "          ],\n"
        "          \"sentence_structure\": \"description of structural changes\"\n"
        "        }\n"
        "      ]\n"
        "    }\n"
        "  ]\n"
        "}\n"
        "```"
    )
    prompt = f"Paraphrase the following text sentence by sentence:\n\n{text}"
    return prompt, system_instruction
def paraphrase_handler(data: Dict[str, Any]) -> Tuple[Dict[str, Any], int]:
    """Handle paraphrase request and return response with status."""
    if not data or 'text' not in data:
        return {"error": "Text field is required"}, HTTPStatus.BAD_REQUEST

    text = data['text'].strip()
    tone = data.get('tone', 'Standard').capitalize()
    synonym_level = data.get('synonym_level', 2)

    if not text:
        return {"error": "Text cannot be empty"}, HTTPStatus.BAD_REQUEST

    # Validate tone
    valid_tones = ['Standard', 'Fluency', 'Creative', 'Expand', 'Shorten', 'Custom']
    if tone not in valid_tones:
        return {"error": f"Tone must be one of {', '.join(valid_tones)}"}, HTTPStatus.BAD_REQUEST

    # Validate synonym_level
    try:
        synonym_level = int(synonym_level)
        if not 0 <= synonym_level <= 4:
            raise ValueError
    except (ValueError, TypeError):
        return {"error": "Synonym level must be an integer between 0 and 4"}, HTTPStatus.BAD_REQUEST

    prompt, system_instruction = get_paraphrase_instruction(text, tone, synonym_level)
    
    # Replace with actual call_openrouter_api in production
    result, status = call_openrouter_api(prompt, system_instruction)
    
    if status != HTTPStatus.OK:
        return result, status

    try:
        parsed = json.loads(extract_json(json.dumps(result)))
        sanitized = sanitize_paraphrases(parsed)
        return sanitized, HTTPStatus.OK
    except json.JSONDecodeError as e:
        logger.error("JSON parsing error: %s", str(e))
        return {"error": "Invalid response format from paraphrasing service"}, HTTPStatus.INTERNAL_SERVER_ERROR