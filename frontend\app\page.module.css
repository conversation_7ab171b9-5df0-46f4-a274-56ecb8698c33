/* page.module.css */
.container {
  min-height: 100vh;
  padding: 0 2rem;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.3rem 0;
  border-bottom: 1px solid #e0e0e0;
}

.logo img {
  height: 60px;
}

.userIcon {
  min-height: auto;
  max-width: auto;
  border-radius: 50%;
  border: 2px solid #e0e0e0;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem 0;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 1rem;
}

.greeting {
  font-size: 1.25rem;
  color: #4a5568;
  margin-bottom: 2rem;
}

.grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  max-width: 800px;
  width: 100%;
  margin-bottom: 2rem;
}

.card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  background-color: #f7fafc;
  border-radius: 10px;
  text-decoration: none;
  color: #2d3748;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.icon {
  margin-bottom: 0.5rem;
}

.card span {
  font-size: 1.25rem;
  font-weight: 500;
}

.card p {
  font-size: 0.875rem;
  color: #718096;
  margin-top: 0.25rem;
}

.premium {
  text-align: center;
  margin-top: 2rem;
}

.premium h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
}

.premium p {
  font-size: 1rem;
  color: #4a5568;
}

/* Responsive design */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr 1fr;
  }

  .title {
    font-size: 2rem;
  }

  .greeting {
    font-size: 1rem;
  }

  .card {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .header {
    flex-direction: column;
    align-items: center;
  }

  .grid {
    grid-template-columns: 1fr;
  }

  .title {
    font-size: 1.3rem;
  }
}
