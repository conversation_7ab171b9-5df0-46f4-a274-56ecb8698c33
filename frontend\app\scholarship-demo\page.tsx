'use client';

import React, { useState } from 'react';
import CriteriaCheckingComponent from '../components/text-analyzer/CriteriaCheckingComponent';

export default function ScholarshipDemo() {
  const [text, setText] = useState(`I am writing to express my strong interest in pursuing a Master's degree in Computer Science through the Global Korea Scholarship program. My passion for technology and innovation, combined with my academic achievements and leadership experiences, has motivated me to seek this opportunity to advance my education in South Korea.

Throughout my undergraduate studies in Computer Engineering, I have maintained a GPA of 3.8/4.0 while actively participating in various extracurricular activities. I served as the president of the Computer Science Club, where I organized coding workshops for junior students and led a team that developed a mobile application for campus navigation. This experience taught me valuable leadership skills and the importance of collaborative problem-solving.

My research interests lie in artificial intelligence and machine learning, particularly in their applications to healthcare technology. During my final year project, I developed a machine learning model for early detection of diabetic retinopathy, which achieved 92% accuracy in preliminary testing. This project not only enhanced my technical skills but also reinforced my commitment to using technology for social good.

I have also gained practical experience through internships at two technology companies, where I worked on web development and data analysis projects. These experiences exposed me to real-world challenges and helped me develop strong problem-solving abilities and attention to detail.

Beyond academics, I have been actively involved in community service. I volunteered as a computer literacy instructor for elderly citizens in my community, teaching basic computer skills to over 50 participants. This experience highlighted the digital divide in our society and motivated me to pursue research that can make technology more accessible to all demographics.

I am particularly drawn to South Korea's advanced technology sector and its innovative approach to education. The country's leadership in areas such as 5G technology, smart cities, and digital innovation aligns perfectly with my career aspirations. I believe that studying in South Korea will provide me with unique insights into cutting-edge technologies and methodologies that I can apply to address challenges in my home country.

My long-term goal is to establish a technology startup focused on developing AI-powered healthcare solutions for underserved communities. The knowledge and experience I gain through the GKS program will be instrumental in achieving this objective. I am confident that my academic background, leadership experience, and passion for technology make me a strong candidate for this prestigious scholarship.

I am committed to serving as a cultural ambassador, promoting mutual understanding between Korea and my home country. I look forward to contributing to the academic community in Korea while gaining invaluable knowledge and experiences that will shape my future career in technology and innovation.`);

  const handleAnalysisComplete = (result: any) => {
    console.log('Analysis completed:', result);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Scholarship Criteria Checker Demo
          </h1>
          <p className="text-gray-600">
            Test the new scholarship selection and criteria evaluation system
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold mb-4">Sample Essay Text</h2>
            <textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              className="w-full h-96 p-4 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your essay text here..."
            />
            <div className="mt-2 text-sm text-gray-500">
              {text.length} characters
            </div>
          </div>

          {/* Criteria Checking Panel */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <CriteriaCheckingComponent 
              text={text}
              onAnalysisComplete={handleAnalysisComplete}
            />
          </div>
        </div>

        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold mb-4">How to Use</h2>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">1</span>
              <p>Click "Select Scholarship & Writing Type" to open the scholarship selection modal</p>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">2</span>
              <p>Choose from available scholarships (GKS, Fulbright, Chevening, etc.)</p>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">3</span>
              <p>Select the type of writing you want to evaluate (Personal Statement, Research Proposal, etc.)</p>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">4</span>
              <p>Click "Analyze Criteria" to see detailed evaluation with scores for each criterion</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
