import axios from 'axios';

const API = process.env.NEXT_PUBLIC_API_URL;

type ChatMode = 'criteria' | 'detector' | 'grammar' | 'paraphraser';

export const api = {
  chat(prompt: string, mode: ChatMode) {
    const urlMap = {
      criteria: '/generate',
      detector: '/detect-ai',
      grammar: '/grammar-check',
      paraphraser: '/paraphrasing'
    };
    
    const payload = mode === 'detector' || mode === 'grammar' 
      ? { text: prompt } 
      : { prompt };
      
    return axios.post(`${API}${urlMap[mode]}`, payload, { withCredentials: true });
  },
  
  login(username: string, password: string) {
    return axios.post(`${API}/login`, { username, password }, { withCredentials: true });
  },
  
  register(username: string, password: string, email: string) {
    return axios.post(`${API}/register`, { username, password, email }, { withCredentials: true });
  },
  
  checkAuth() {
    return axios.get(`${API}/check-auth`, { withCredentials: true });
  },
  
  logout() {
    return axios.post(`${API}/logout`, {}, { withCredentials: true });
  }
};