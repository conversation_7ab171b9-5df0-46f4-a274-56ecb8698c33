'use client';

import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';

export default function ForgotForm() {
  const { openAuth } = useAuth();
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;
    
    setIsSubmitting(true);
    // Simulate API call
    setTimeout(() => {
      setIsSubmitted(true);
      setIsSubmitting(false);
    }, 1000);
  };

  return (
    <div>
      <h2 className="text-2xl font-bold text-center mb-6">Reset Password</h2>
      
      {!isSubmitted ? (
        <>
          <p className="text-gray-600 mb-6 text-center">
            Enter your email address and we'll send you a link to reset your password.
          </p>
          
          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-blue-400"
            >
              {isSubmitting ? 'Sending...' : 'Send Reset Link'}
            </button>
          </form>
        </>
      ) : (
        <div className="text-center">
          <div className="bg-green-50 text-green-700 p-4 rounded-lg mb-6">
            If an account exists with that email, we've sent a password reset link.
          </div>
          <p className="text-gray-600 mb-4">
            Check your inbox and follow the instructions in the email.
          </p>
        </div>
      )}
      
      <div className="mt-6 text-center">
        <button 
          onClick={() => openAuth('login')}
          className="text-blue-600 hover:underline"
        >
          Back to Sign In
        </button>
      </div>
    </div>
  );
}