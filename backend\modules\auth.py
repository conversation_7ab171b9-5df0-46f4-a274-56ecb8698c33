import os
import uuid
import bcrypt
import datetime
import psycopg2
from dotenv import load_dotenv
import json
load_dotenv()

def get_db_connection():
    """Establish a new connection to the PostgreSQL database."""
    conn = psycopg2.connect(
        host=os.getenv("DB_HOST"),         
        port=os.getenv("DB_PORT"),         
        dbname=os.getenv("DB_DATABASE"),   
        user=os.getenv("DB_USERNAME"),     
        password=os.getenv("DB_PASSWORD")  
    )
    conn.autocommit = True
    return conn

def register_user(username: str, password: str, first_name: str = None, last_name: str = None, role="user"):
    conn = get_db_connection()
    hashed_password = bcrypt.hashpw(password.encode("utf-8"), bcrypt.gensalt()).decode("utf-8")
    created_at = datetime.datetime.now(datetime.UTC)
    
    with conn.cursor() as cur:
        cur.execute("SELECT user_id FROM users WHERE username = %s;", (username,))
        if cur.fetchone():
            conn.close()
            return {"error": "An account with this Gmail address already exists."}, False
        
        # Check if users table has first_name and last_name columns
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name IN ('first_name', 'last_name');
        """)
        existing_columns = [row[0] for row in cur.fetchall()]
        
        # Add columns if they don't exist
        if 'first_name' not in existing_columns:
            cur.execute("ALTER TABLE users ADD COLUMN first_name VARCHAR(255);")
        if 'last_name' not in existing_columns:
            cur.execute("ALTER TABLE users ADD COLUMN last_name VARCHAR(255);")
        
        # Insert user with first and last name
        cur.execute("""
            INSERT INTO users (username, password, role, created_at, first_name, last_name)
            VALUES (%s, %s, %s, %s, %s, %s)
            RETURNING user_id;
        """, (username, hashed_password, role, created_at, first_name, last_name))
        user_id = cur.fetchone()[0]
        conn.commit()
    
    conn.close()
    return {"user_id": str(user_id), "username": username, "role": role, "first_name": first_name, "last_name": last_name}, True

def create_session(user_id, username, role):
    conn = get_db_connection()
    session_id = str(uuid.uuid4())
    expires_at = datetime.datetime.now(datetime.UTC) + datetime.timedelta(days=7)

    session_data = json.dumps({
        "username": username,
        "role": role
    })  # ✅ Serialize dict to JSON

    with conn.cursor() as cur:
        cur.execute("""
            INSERT INTO sessions (session_id, user_id, data, expires_at)
            VALUES (%s, %s, %s, %s);
        """, (session_id, user_id, session_data, expires_at))
        conn.commit()

    conn.close()
    return session_id

def login_user(username: str, password: str):
    """
    Login a user by verifying the password.
    Returns a tuple: (user dict, success flag).
    """
    conn = get_db_connection()
    with conn.cursor() as cur:
        cur.execute("SELECT user_id, username, password, role FROM users WHERE username = %s;", (username,))
        user = cur.fetchone()
    conn.close()
    
    if user and bcrypt.checkpw(password.encode("utf-8"), user[2].encode("utf-8")):
        return {"user_id": user[0], "username": user[1], "role": user[3] or "user"}, True
    return {"error": "Invalid credentials."}, False
