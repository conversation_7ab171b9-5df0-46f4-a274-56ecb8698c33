'use client';

import React from 'react';
import { useChat } from '../../context/ChatContext';
import { FiEdit3, <PERSON>Check, FiAlertTriangle, FiRefreshCw } from 'react-icons/fi';

export default function ToolBar() {
  const { mode, setMode, clearMessages } = useChat();
  
  const handleModeChange = (newMode: 'criteria' | 'detector' | 'grammar' | 'paraphraser') => {
    if (mode !== newMode) {
      setMode(newMode);
      clearMessages();
    }
  };
  
  return (
    <div className="bg-white border-b border-gray-200 py-2 px-4">
      <div className="max-w-3xl mx-auto flex space-x-2 overflow-x-auto">
        <button
          onClick={() => handleModeChange('criteria')}
          className={`px-4 py-2 rounded-lg flex items-center gap-2 whitespace-nowrap ${
            mode === 'criteria' 
              ? 'bg-blue-100 text-blue-700 font-medium' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <FiEdit3 /> Criteria Checking
        </button>
        
        <button
          onClick={() => handleModeChange('detector')}
          className={`px-4 py-2 rounded-lg flex items-center gap-2 whitespace-nowrap ${
            mode === 'detector' 
              ? 'bg-blue-100 text-blue-700 font-medium' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <FiAlertTriangle /> AI Detector
        </button>
        
        <button
          onClick={() => handleModeChange('grammar')}
          className={`px-4 py-2 rounded-lg flex items-center gap-2 whitespace-nowrap ${
            mode === 'grammar' 
              ? 'bg-blue-100 text-blue-700 font-medium' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <FiCheck /> Grammar Check
        </button>
        
        <button
          onClick={() => handleModeChange('paraphraser')}
          className={`px-4 py-2 rounded-lg flex items-center gap-2 whitespace-nowrap ${
            mode === 'paraphraser' 
              ? 'bg-blue-100 text-blue-700 font-medium' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <FiRefreshCw /> Paraphraser
        </button>
      </div>
    </div>
  );
}