'use client';

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { FaSearch, FaClipboardCheck, FaRegCommentDots } from "react-icons/fa";
import { MdOutlineSpellcheck } from "react-icons/md";
import { GiArtificialIntelligence } from "react-icons/gi";

type Role = 'user' | 'admin';

interface HoverSidebarProps {
  role: Role;
}

const USER_MENU_ITEMS = [
  { href: "/ai-detector", label: "AI Detector", icon: GiArtificialIntelligence },
  { href: "/criteria-checking", label: "Criteria Checking", icon: FaSearch },
  { href: "/grammar-checker", label: "Grammar Checker", icon: MdOutlineSpellcheck },
  { href: "/paraphrasing", label: "Paraphrasing", icon: FaRegCommentDots },
];

const ADMIN_MENU_ITEMS = [
  ...USER_MENU_ITEMS,
  { href: "/criteria-input", label: "Criteria Input", icon: FaClipboardCheck },
];

export default function HoverSidebar({ role }: HoverSidebarProps) {
  console.log("HoverSidebar rendered with role:", role);
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  const menuItems = role === 'admin' ? ADMIN_MENU_ITEMS : USER_MENU_ITEMS;

  return (
    <div
      className="fixed top-1/2 right-0 z-[100] transform -translate-y-1/2"
      onMouseEnter={() => setIsOpen(true)}
      onMouseLeave={() => setIsOpen(false)}
    >
      <div
      className={`h-auto bg-gradient-to-b from-gray-100 to-gray-300 shadow-2xl rounded-l-3xl transition-all duration-300 flex flex-col border border-gray-300 ${
        isOpen ? "w-56" : "w-16"
      }`}
      >
      <nav className="flex flex-col space-y-2 p-2">
        {menuItems.map(({ href, label, icon: Icon }) => {
        const active = pathname === href;
        return (
          <Link
          key={href}
          href={href}
          className={`flex items-center gap-4 py-3 px-4 rounded-xl transition-all duration-300 font-medium shadow-sm ${
            active ? "bg-purple-500 text-white" : "text-gray-700 hover:bg-gray-200"
          }`}
          >
          <Icon className="text-2xl flex-shrink-0" />
          <span
            className={`text-md whitespace-nowrap overflow-hidden transition-all duration-300 ${
            isOpen ? "opacity-100 w-auto" : "hidden opacity-0"
            }`}
          >
            {label}
          </span>
          </Link>
        );
        })}
      </nav>
      </div>
    </div>
  );
}