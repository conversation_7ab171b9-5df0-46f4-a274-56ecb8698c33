{"cells": [{"cell_type": "code", "execution_count": 1, "id": "54d97eaf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["$2b$12$akoOkFhDcG1xg9//T4Py..4Y5QO1aAB.kh399.SmmwnoGcsaHaDzG\n"]}], "source": ["import bcrypt\n", "password = \"test123\"\n", "hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())\n", "print(hashed.decode('utf-8'))  # Use this in the users INSERT"]}, {"cell_type": "code", "execution_count": null, "id": "5364eaca", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}