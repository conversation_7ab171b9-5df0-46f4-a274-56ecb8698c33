from http import HTTPStatus
import logging

logger = logging.getLogger(__name__)

def get_grammar_instruction(text, tone="Academic"):
    """
    Prepare the prompt and system instruction for grammar checking.
    """
    system_instruction = (
        "You are an expert grammar checker similar to Grammarly, specializing in precise error detection. "
        "Analyze the text for grammar, spelling, punctuation, style, and clarity issues only. "
        "Do NOT provide paraphrasing, vocabulary enhancements, or unrelated suggestions. "
        "Return your analysis in this exact JSON format:\n"
        "{\n"
        "  \"corrections\": [\n"
        "    {\n"
        "      \"incorrect\": \"string\", // Exact text segment with the error\n"
        "      \"suggestion\": \"string\", // Corrected version\n"
        "      \"start\": integer, // Starting character index in original text\n"
        "      \"end\": integer, // Ending character index in original text\n"
        "      \"priority\": \"high\" | \"medium\" | \"low\", // Error severity\n"
        "      \"category\": \"misspelling\" | \"correctness\" | \"clarity\" | \"engagement\" | \"delivery\", // Error type\n"
        "      \"explanation\": \"string\" // Brief explanation of the error and why the suggestion is better\n"
        "    }\n"
        "  ]\n"
        "}\n"
        "Ensure all fields are present and accurate for each correction. "
        "If no errors are found, return an empty 'corrections' array."
    )
    
    prompt = (
        f"Analyze this text for grammar, spelling, punctuation, style, and clarity issues in a {tone} tone:\n\n"
        f"\"{text}\"\n\n"
        "Provide corrections in the exact JSON format specified above. "
        "Include precise character indices and ensure suggestions maintain the original meaning. "
        "Return only the JSON structure, no additional text."
    )
    
    return prompt, system_instruction
def grammar_check_handler(data):
    """
    Handle grammar checking logic.
    Expects data: { "text": string, "tone": string (optional) }
    """
    if not data or 'text' not in data:
        return {"error": "Text field is required"}, HTTPStatus.BAD_REQUEST
    
    text = data.get('text', '').strip()
    tone = data.get('tone', 'Academic')
    
    if not text:
        return {"error": "Text cannot be empty"}, HTTPStatus.BAD_REQUEST

    prompt, system_instruction = get_grammar_instruction(text, tone)
    from modules.api_calls import call_openrouter_api  # Import here to avoid circular imports
    return call_openrouter_api(prompt, system_instruction)