# modules/scholarship.py
import os
import psycopg2
from psycopg2.extras import RealDictCursor
import logging
from dotenv import load_dotenv
# Use existing logger instead of configuring a new one
logger = logging.getLogger(__name__)
load_dotenv()

# Database connection
def get_db_connection():
    try:
        return psycopg2.connect(
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_DATABASE"),
            user=os.getenv("DB_USERNAME"),
            password=os.getenv("DB_PASSWORD")
        )
    except Exception as e:
        logger.error("Database connection failed: %s", str(e))
        return None

def get_scholarship_categories():
    """Retrieve all scholarship categories from the database."""
    conn = get_db_connection()
    if conn is None:
        return {"error": "Database connection failed"}, False
    try:
        with conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("SELECT category_id, category_name FROM scholarship_categories")
                categories = cur.fetchall()
        logger.info("Scholarship categories retrieved successfully")
        return categories, True
    except Exception as e:
        logger.error("Error retrieving scholarship categories: %s", str(e))
        return {"error": str(e)}, False
    finally:
        conn.close()

def get_prompts(category_id):
    """Retrieve prompts for a given scholarship category from the database."""
    conn = get_db_connection()
    if conn is None:
        return {"error": "Database connection failed"}, False
    try:
        with conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT isp.init_prompt_id AS prompt_id, isp.prompt_text
                    FROM essay_types et
                    JOIN initial_suggestion_prompts isp ON et.essay_type_id = isp.essay_type_id
                    WHERE et.category_id = %s
                """, (category_id,))
                prompts = cur.fetchall()
        logger.info("Prompts retrieved successfully for category_id %s", category_id)
        return prompts, True
    except Exception as e:
        logger.error("Error retrieving prompts for category_id %s: %s", category_id, str(e))
        return {"error": str(e)}, False
    finally:
        conn.close()

# scholarship.py (partial update)
def get_initial_suggestion_prompts():
    conn = get_db_connection()
    if conn is None:
        return {"error": "Database connection failed"}, False
    try:
        with conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT isp.init_prompt_id AS prompt_id, isp.prompt_text, isp.essay_type_id, et.category_id
                    FROM initial_suggestion_prompts isp
                    JOIN essay_types et ON isp.essay_type_id = et.essay_type_id
                """)
                prompts = cur.fetchall()
        logger.info("Initial suggestion prompts retrieved successfully")
        return prompts, True
    except Exception as e:
        logger.error("Error retrieving initial suggestion prompts: %s", str(e))
        return {"error": str(e)}, False
    finally:
        conn.close()

def get_follow_up_suggestions():
    conn = get_db_connection()
    if conn is None:
        return {"error": "Database connection failed"}, False
    try:
        with conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT fus.followup_id, fus.suggestion_text, fus.suggestion_key, fus.essay_type_id, et.category_id
                    FROM follow_up_suggestions fus
                    JOIN essay_types et ON fus.essay_type_id = et.essay_type_id
                """)
                suggestions = cur.fetchall()
        logger.info("Follow-up suggestions retrieved successfully")
        return suggestions, True
    except Exception as e:
        logger.error("Error retrieving follow-up suggestions: %s", str(e))
        return {"error": str(e)}, False
    finally:
        conn.close()

def get_follow_up_examples():
    """Retrieve all follow-up examples from the database."""
    conn = get_db_connection()
    if conn is None:
        return {"error": "Database connection failed"}, False
    try:
        with conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("SELECT * FROM follow_up_examples")
                examples = cur.fetchall()
        logger.info("Follow-up examples retrieved successfully")
        return examples, True
    except Exception as e:
        logger.error("Error retrieving follow-up examples: %s", str(e))
        return {"error": str(e)}, False
    finally:
        conn.close()

def get_essay_types():
    try:
        conn = get_db_connection()
        with conn.cursor() as cur:
            cur.execute("""
                SELECT essay_type_id, category_id, essay_type_name 
                FROM essay_types
            """)
            rows = cur.fetchall()
        conn.close()

        results = [
            {"essay_type_id": str(row[0]), "category_id": str(row[1]), "essay_type_name": row[2]}
            for row in rows
        ]
        return results, True
    except Exception as e:
        logger.error("Error in get_essay_types: %s", str(e))
        return {"error": "Failed to retrieve essay types"}, False
