'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiPlus, FiMenu, FiX } from 'react-icons/fi';
import HistoryEntry, { HistoryEntry as HistoryEntryType } from './HistoryEntry';

interface HistorySidebarProps {
  isVisible: boolean;
  toggleSidebar: () => void;
  historyEntries: HistoryEntryType[];
  activeEntryId: string | null;
  onEntryClick: (entryId: string) => void;
  onNewAnalysis: () => void;
}

const HistorySidebar: React.FC<HistorySidebarProps> = ({
  isVisible,
  toggleSidebar,
  historyEntries,
  activeEntryId,
  onEntryClick,
  onNewAnalysis
}) => {
  // Animation variants
  const sidebarVariants = {
    open: { 
      width: '280px',
      transition: { 
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    closed: { 
      width: '0px',
      transition: { 
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    }
  };

  return (
    <>
      {/* Toggle button - visible when sidebar is closed */}
      {!isVisible && (
        <motion.button
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute left-4 top-4 z-20 p-2 bg-white rounded-full shadow-md hover:bg-gray-100"
          onClick={toggleSidebar}
        >
          <FiMenu size={20} />
        </motion.button>
      )}

      {/* Sidebar */}
      <motion.div
        className="h-full bg-gray-50 border-r border-gray-200 overflow-hidden"
        variants={sidebarVariants}
        initial="closed"
        animate={isVisible ? "open" : "closed"}
      >
        {isVisible && (
          <div className="h-full flex flex-col p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">History</h2>
              <button
                onClick={toggleSidebar}
                className="p-1 rounded-full hover:bg-gray-200"
              >
                <FiX size={20} />
              </button>
            </div>

            <button
              onClick={onNewAnalysis}
              className="w-full bg-blue-600 text-white py-2 rounded-lg mb-4 hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <FiPlus className="mr-2" />
              New Analysis
            </button>

            <div className="flex-1 overflow-y-auto">
              <AnimatePresence>
                {historyEntries.length > 0 ? (
                  historyEntries.map((entry) => (
                    <HistoryEntry
                      key={entry.id}
                      entry={entry}
                      isActive={entry.id === activeEntryId}
                      onClick={() => onEntryClick(entry.id)}
                    />
                  ))
                ) : (
                  <div className="text-center text-gray-500 mt-8">
                    <p>No history yet</p>
                    <p className="text-sm mt-2">
                      Analyze text to see your history here
                    </p>
                  </div>
                )}
              </AnimatePresence>
            </div>
          </div>
        )}
      </motion.div>
    </>
  );
};

export default HistorySidebar;
