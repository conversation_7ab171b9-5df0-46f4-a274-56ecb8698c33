import re
import json
import logging
from typing import Any, Dict

logger = logging.getLogger(__name__)

def extract_json(content: str) -> str:
    """Extract JSON from the AI response, handling both ```json``` blocks and raw JSON."""
    match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
    if match:
        return match.group(1)
    start = content.find('{')
    end = content.rfind('}')
    if start != -1 and end != -1 and end > start:
        return content[start:end + 1]
    return content

def sanitize_paraphrases(parsed: Dict[str, Any]) -> Dict[str, Any]:
    """Ensure the parsed JSON matches the expected structure, fixing invalid parts."""
    if not isinstance(parsed, dict) or "paraphrases" not in parsed:
        logger.warning("Invalid JSON structure detected, returning empty paraphrases")
        return {"paraphrases": []}

    for paraphrase in parsed["paraphrases"]:
        if not isinstance(paraphrase, dict) or "sentence" not in paraphrase or "versions" not in paraphrase:
            paraphrase.update({"sentence": "", "versions": []})
        if not isinstance(paraphrase["versions"], list):
            paraphrase["versions"] = []
        for version in paraphrase["versions"]:
            if not isinstance(version, dict):
                version = {"text": "", "vocabulary": [], "sentence_structure": ""}
            version.setdefault("text", "")
            version.setdefault("vocabulary", [])
            version.setdefault("sentence_structure", "")
            if not isinstance(version["vocabulary"], list):
                version["vocabulary"] = []
            else:
                version["vocabulary"] = [
                    item for item in version["vocabulary"]
                    if isinstance(item, dict) and "original" in item and "replacement" in item
                ]
    return parsed