import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import Header from './components/header';
import ClientAuth from './ClientAuth';
import AuthProvider from './context/AuthContext';
import Canvas from './components/auth-canvas/Canvas';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Scholarar - AI-Powered Scholarship Writing Assistant',
  description: 'Enhance your scholarship applications with AI-powered tools',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <ClientAuth>
            <Header />
            {children}
          </ClientAuth>
          <Canvas />
        </AuthProvider>
      </body>
    </html>
  );
}

