# config.py
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
    EDENAI_API_KEY = os.getenv("EDENAI_API_KEY")
    ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000")
    PORT = int(os.getenv("PORT", 5000))
    API_TIMEOUT = int(os.getenv("API_TIMEOUT", 15))
    DEBUG = os.getenv("FLASK_DEBUG", "False").lower() in ("true", "1", "t")
    LOG_DIR = os.path.join(os.path.dirname(__file__), "logs")
    LOG_FILE = os.path.join(LOG_DIR, "app.log")