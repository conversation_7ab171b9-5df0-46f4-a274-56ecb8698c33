'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiPlus,
  FiMenu,
  FiX,
  FiFolder,
  FiFile,
  FiFileText,
  FiEdit3,
  FiCheck,
  FiSearch,
  FiClock
} from 'react-icons/fi';
import { format } from 'date-fns';
import { HistoryEntry, GroupedHistoryEntry } from './HistoryEntry';

interface HierarchicalHistorySidebarProps {
  isVisible: boolean;
  toggleSidebar: () => void;
  groupedEntries: GroupedHistoryEntry[];
  activeInputId: string | null;
  activeFeatureId: string | null;
  onInputClick: (inputId: string) => void;
  onFeatureClick: (featureId: string) => void;
  onNewAnalysis: () => void;
  onShowAllHistory: () => void;
}

const HierarchicalHistorySidebar: React.FC<HierarchicalHistorySidebarProps> = ({
  isVisible,
  toggleSidebar,
  groupedEntries,
  activeInputId,
  activeFeatureId,
  onInputClick,
  onFeatureClick,
  onNewAnalysis
}) => {
  const [expandedInputs, setExpandedInputs] = useState<Set<string>>(new Set());

  // Toggle expansion of an input group
  const toggleInputExpansion = (inputId: string) => {
    setExpandedInputs(prev => {
      const newSet = new Set(prev);
      if (newSet.has(inputId)) {
        newSet.delete(inputId);
      } else {
        newSet.add(inputId);
      }
      return newSet;
    });
  };

  // Get feature icon
  const getFeatureIcon = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {
    switch (featureType) {
      case 'ai-detector':
        return <FiSearch className="text-purple-500" size={14} />;
      case 'grammar-check':
        return <FiCheck className="text-green-500" size={14} />;
      case 'criteria-check':
        return <FiFileText className="text-blue-500" size={14} />;
      case 'paraphraser':
        return <FiEdit3 className="text-orange-500" size={14} />;
      default:
        return <FiFile className="text-gray-500" size={14} />;
    }
  };

  // Get feature name
  const getFeatureName = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {
    switch (featureType) {
      case 'ai-detector':
        return 'AI Detector';
      case 'grammar-check':
        return 'Grammar Check';
      case 'criteria-check':
        return 'Criteria Check';
      case 'paraphraser':
        return 'Paraphraser';
      default:
        return 'Unknown Feature';
    }
  };

  // Get truncated text preview
  const getTextPreview = (text: string) => {
    if (!text) return 'No input text';
    return text.length > 40
      ? `${text.substring(0, 40)}...`
      : text;
  };

  // Animation variants
  const sidebarVariants = {
    open: {
      width: '320px',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      width: '0px',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    }
  };

  return (
    <>
      {/* Toggle button - visible when sidebar is closed */}
      {!isVisible && (
        <motion.button
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute left-4 top-4 z-20 p-2 bg-white rounded-full shadow-md hover:bg-gray-100"
          onClick={toggleSidebar}
        >
          <FiMenu size={20} />
        </motion.button>
      )}

      {/* Sidebar */}
      <motion.div
        className="h-full bg-gray-50 border-r border-gray-200 overflow-hidden"
        variants={sidebarVariants}
        initial="closed"
        animate={isVisible ? "open" : "closed"}
      >
        {isVisible && (
          <div className="h-full flex flex-col p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">History</h2>
              <button
                onClick={toggleSidebar}
                className="p-1 rounded-full hover:bg-gray-200"
              >
                <FiX size={20} />
              </button>
            </div>

            <button
              onClick={onNewAnalysis}
              className="w-full bg-blue-600 text-white py-2 rounded-lg mb-4 hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <FiPlus className="mr-2" />
              New Analysis
            </button>

            <div className="flex-1 overflow-y-auto">
              <AnimatePresence>
                {groupedEntries.length > 0 ? (
                  groupedEntries.map((group) => (
                    <motion.div
                      key={group.inputId}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="mb-2"
                    >
                      {/* Input Group Header */}
                      <div
                        className={`flex items-center p-2 rounded-lg cursor-pointer transition-all ${
                          activeInputId === group.inputId
                            ? 'bg-blue-100 border border-blue-300'
                            : 'bg-white hover:bg-gray-50 border border-gray-200'
                        }`}
                        onClick={() => {
                          onInputClick(group.inputId);
                          toggleInputExpansion(group.inputId);
                        }}
                      >
                        <div className="mr-2">
                          {expandedInputs.has(group.inputId) ? (
                            <FiFolder className="text-blue-500" size={16} />
                          ) : (
                            <FiFolder className="text-blue-500" size={16} />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium truncate">
                            {getTextPreview(group.inputText)}
                          </div>
                          <div className="flex items-center text-xs text-gray-500">
                            <FiClock className="mr-1" size={10} />
                            {format(group.timestamp, 'MMM d, h:mm a')}
                            <span className="ml-2 bg-gray-200 px-1 rounded text-xs">
                              {group.features.length}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Feature List */}
                      <AnimatePresence>
                        {expandedInputs.has(group.inputId) && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="ml-4 mt-1 space-y-1"
                          >
                            {group.features.map((feature) => (
                              <div key={feature.id}>
                                <motion.div
                                  initial={{ opacity: 0, x: -10 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  exit={{ opacity: 0, x: -10 }}
                                  className={`flex items-center p-2 rounded cursor-pointer transition-all ${
                                    activeFeatureId === feature.id
                                      ? 'bg-blue-50 border-l-2 border-blue-500'
                                      : 'hover:bg-gray-50'
                                  }`}
                                  onClick={() => onFeatureClick(feature.id)}
                                >
                                  <div className="mr-2">
                                    {getFeatureIcon(feature.featureType)}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="text-xs font-medium">
                                      {getFeatureName(feature.featureType)}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      {format(feature.timestamp, 'h:mm a')}
                                    </div>
                                  </div>
                                </motion.div>
                              </div>
                            ))}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))
                ) : (
                  <div className="text-center text-gray-500 mt-8">
                    <p>No history yet</p>
                    <p className="text-sm mt-2">
                      Analyze text to see your history here
                    </p>
                  </div>
                )}
              </AnimatePresence>
            </div>
          </div>
        )}
      </motion.div>
    </>
  );
};

export default HierarchicalHistorySidebar;
