

-- Create users table
CREATE TABLE users (
    user_id SERIAL PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    role VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create chat_histories table
CREATE TABLE chat_histories (
    chat_history_id SERIAL PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_chat_histories_user FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Create messages table
CREATE TABLE messages (
    message_id SERIAL PRIMARY KEY,
    chat_history_id INT NOT NULL,
    role VARCHAR(50),
    content TEXT,
    CONSTRAINT fk_messages_chat_history FOREIGN KEY (chat_history_id) REFERENCES chat_histories(chat_history_id)
);

-- Create scholarship_categories table with UNIQUE constraint
CREATE TABLE scholarship_categories (
    category_id SERIAL PRIMARY KEY,
    category_name VARCHAR(255) NOT NULL UNIQUE
);

-- Create essay_types table to store essay types for each scholarship category
CREATE TABLE essay_types (
    essay_type_id SERIAL PRIMARY KEY,
    category_id INT NOT NULL,
    essay_type_name VARCHAR(255) NOT NULL,
    CONSTRAINT fk_essay_types_category FOREIGN KEY (category_id) REFERENCES scholarship_categories(category_id),
    CONSTRAINT unique_category_essay_type UNIQUE (category_id, essay_type_name)
);

-- Create initial_suggestion_prompts table
CREATE TABLE initial_suggestion_prompts (
    init_prompt_id SERIAL PRIMARY KEY,
    essay_type_id INT NOT NULL,
    prompt_text TEXT NOT NULL,
    CONSTRAINT fk_initial_prompts_essay_type FOREIGN KEY (essay_type_id) REFERENCES essay_types(essay_type_id)
);

-- Create follow_up_suggestions table
CREATE TABLE follow_up_suggestions (
    followup_id SERIAL PRIMARY KEY,
    essay_type_id INT NOT NULL,
    suggestion_key VARCHAR(255) NOT NULL,
    suggestion_text TEXT NOT NULL,
    CONSTRAINT fk_followup_suggestions_essay_type FOREIGN KEY (essay_type_id) REFERENCES essay_types(essay_type_id)
);

-- Create follow_up_examples table
CREATE TABLE follow_up_examples (
    example_id SERIAL PRIMARY KEY,
    essay_type_id INT NOT NULL,
    example_key VARCHAR(255) NOT NULL,
    example_text TEXT NOT NULL,
    CONSTRAINT fk_followup_examples_essay_type FOREIGN KEY (essay_type_id) REFERENCES essay_types(essay_type_id)
);

-- Create prompts table to link essay types with their prompts, suggestions, and examples
CREATE TABLE prompts (
    prompt_id SERIAL PRIMARY KEY,
    essay_type_id INT NOT NULL,
    init_prompt_id INT NOT NULL,
    followup_id INT NOT NULL,
    example_id INT NOT NULL,
    CONSTRAINT fk_prompts_essay_type FOREIGN KEY (essay_type_id) REFERENCES essay_types(essay_type_id),
    CONSTRAINT fk_prompts_init_prompt FOREIGN KEY (init_prompt_id) REFERENCES initial_suggestion_prompts(init_prompt_id),
    CONSTRAINT fk_prompts_followup FOREIGN KEY (followup_id) REFERENCES follow_up_suggestions(followup_id),
    CONSTRAINT fk_prompts_example FOREIGN KEY (example_id) REFERENCES follow_up_examples(example_id)
);

CREATE TABLE sessions (
    session_id VARCHAR PRIMARY KEY,  -- Unique session identifier
    user_id INTEGER REFERENCES users(user_id),  -- Link to users table
    data JSONB,  -- Store session data (e.g., {"username": "...", "role": "..."})
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL  -- When the session expires
);

-- Recommended if you're using Postgres:
ALTER TABLE sessions
  ALTER COLUMN data SET DATA TYPE JSONB;

