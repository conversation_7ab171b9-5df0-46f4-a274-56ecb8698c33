import os
import pytest
import psycopg2
from modules.auth import register_user, login_user, get_db_connection
from dotenv import load_dotenv

load_dotenv()

# Fixture to set up and tear down a test database
@pytest.fixture(scope="function")
def test_db():
    # Connect to the database
    conn = psycopg2.connect(
        host=os.getenv("DB_HOST"),
        port=os.getenv("DB_PORT"),
        dbname=os.getenv("DB_DATABASE"),
        user=os.getenv("DB_USERNAME"),
        password=os.getenv("DB_PASSWORD")
    )
    conn.autocommit = True
    cur = conn.cursor()

    # Clean up any existing test user
    cur.execute("DELETE FROM users WHERE username = %s;", ("<EMAIL>",))
    
    yield conn  # Provide the connection to the test
    
    # Cleanup after test
    cur.execute("DELETE FROM users WHERE username = %s;", ("<EMAIL>",))
    cur.close()
    conn.close()

def test_register_and_login(test_db):
    # Register a user
    response, success = register_user("<EMAIL>", "password123", role="admin")
    assert success
    assert response["message"] == "User registered successfully with role 'admin'!"

    # Login with the registered user
    user, success = login_user("<EMAIL>", "password123")
    assert success
    assert user["username"] == "<EMAIL>"
    assert user["role"] == "admin"

def test_register_duplicate_user(test_db):
    # Register a user
    register_user("<EMAIL>", "password123")
    
    # Try registering the same user again
    response, success = register_user("<EMAIL>", "password123")
    assert not success
    assert response["error"] == "An account with this Gmail address already exists. Please log in instead."

def test_login_invalid_credentials(test_db):
    # Try logging in with a non-existent user
    user, success = login_user("<EMAIL>", "password123")
    assert not success
    assert user["error"] == "Invalid credentials."