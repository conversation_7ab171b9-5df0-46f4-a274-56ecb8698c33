'use client';

import { usePathname } from 'next/navigation';
import ClientAuth from './ClientAuth';

export default function ClientLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  // Add '/' to public routes to make homepage publicly accessible
  const isPublicRoute = pathname === '/login' || pathname === '/register' || pathname === '/';

  return isPublicRoute ? <>{children}</> : <ClientAuth>{children}</ClientAuth>;
}
