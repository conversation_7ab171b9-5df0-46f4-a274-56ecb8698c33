'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiFileText, FiEdit3, FiCheck, FiSearch } from 'react-icons/fi';

interface FeatureSelectorProps {
  onSelect: (feature: string) => void;
  selectedFeature: string | null;
}

const FeatureSelector: React.FC<FeatureSelectorProps> = ({ onSelect, selectedFeature }) => {
  const features = [
    {
      id: 'criteria-check',
      name: 'Criteria Check',
      description: 'Check against criteria',
      icon: <FiFileText className="text-blue-500" />
    },
    {
      id: 'ai-detector',
      name: 'AI Detector',
      description: 'Detect AI content',
      icon: <FiSearch className="text-purple-500" />
    },
    {
      id: 'grammar-check',
      name: 'Grammar Check',
      description: 'Fix grammar issues',
      icon: <FiCheck className="text-green-500" />
    },
    {
      id: 'paraphraser',
      name: 'Paraphraser',
      description: 'Rewrite text',
      icon: <FiEdit3 className="text-orange-500" />
    }
  ];

  // Handle feature selection with animation
  const handleFeatureSelect = (featureId: string) => {
    onSelect(featureId);
  };

  // Animation variants for the container
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    },
    exit: {
      opacity: 0,
      transition: {
        staggerChildren: 0.05,
        staggerDirection: -1
      }
    }
  };

  // Animation variants for each feature button
  const featureVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.2
      }
    },
    selected: {
      scale: 1.05,
      y: -5,
      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 15
      }
    }
  };

  return (
    <motion.div
      className="flex flex-wrap gap-4 justify-center"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      <AnimatePresence>
        {features.map((feature) => (
          <motion.button
            key={feature.id}
            onClick={() => handleFeatureSelect(feature.id)}
            whileHover={{ y: -3, boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)' }}
            whileTap={{ scale: 0.95 }}
            variants={featureVariants}
            animate={selectedFeature === feature.id ? "selected" : "visible"}
            className={`flex flex-col items-center p-4 rounded-xl transition-all ${
              selectedFeature === feature.id
                ? 'bg-blue-100 border-2 border-blue-500 shadow-md'
                : 'bg-white border border-gray-200 hover:bg-gray-50 hover:border-blue-200'
            }`}
            style={{ width: '140px', height: '130px' }}
          >
            <div className={`w-12 h-12 flex items-center justify-center rounded-full mb-3 ${
              selectedFeature === feature.id ? 'bg-blue-500' : 'bg-gray-100'
            }`}>
              <div className={`text-xl ${selectedFeature === feature.id ? 'text-white' : ''}`}>
                {feature.icon}
              </div>
            </div>
            <div className="font-medium text-sm text-center">
              {feature.name}
            </div>
            <div className="text-xs text-gray-500 text-center mt-1">
              {feature.description}
            </div>
            {selectedFeature === feature.id && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                className="absolute top-0 right-0 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center transform translate-x-1 -translate-y-1"
              >
                <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                </svg>
              </motion.div>
            )}
          </motion.button>
        ))}
      </AnimatePresence>
    </motion.div>
  );
};

export default FeatureSelector;
