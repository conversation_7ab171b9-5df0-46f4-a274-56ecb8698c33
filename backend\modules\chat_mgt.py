# chat_mgt.py
import os
import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

load_dotenv()

# Establish connection using env variables
conn = psycopg2.connect(
    host=os.getenv("DB_HOST"),
    port=os.getenv("DB_PORT"),
    dbname=os.getenv("DB_DATABASE"),
    user=os.getenv("DB_USERNAME"),
    password=os.getenv("DB_PASSWORD")
)
conn.autocommit = True

def list_chat_histories(user_id, limit=10, offset=0):
    with conn.cursor(cursor_factory=RealDictCursor) as cur:
        cur.execute("""
            SELECT chat_history_id, title, created_at, scholarship, essay_type 
            FROM chat_histories 
            WHERE user_id = %s 
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        """, (user_id, limit, offset))
        sessions = cur.fetchall()

    chat_list = [
        {
            "chat_id": str(s["chat_history_id"]),
            "title": s.get("title") or f"Chat {str(s['chat_history_id'])[-5:]}",
            "scholarship": s.get("scholarship"),
            "essay_type": s.get("essay_type")
        }
        for s in sessions
    ]
    return chat_list

def load_chat_history(chat_id, user_id):
    with conn.cursor(cursor_factory=RealDictCursor) as cur:
        cur.execute("""
            SELECT role, content 
            FROM messages 
            WHERE chat_history_id = %s 
            AND chat_history_id IN (
                SELECT chat_history_id FROM chat_histories WHERE user_id = %s
            )
            ORDER BY message_id
        """, (chat_id, user_id))
        rows = cur.fetchall()
        return rows if rows else []

def create_new_chat(user_id, title="Untitled Chat"):
    with conn.cursor() as cur:
        cur.execute("""
            INSERT INTO chat_histories (user_id, title, created_at)
            VALUES (%s, %s, %s)
            RETURNING chat_history_id
        """, (user_id, title, datetime.datetime.utcnow()))
        new_id = cur.fetchone()[0]
        conn.commit()
    return str(new_id)

def save_chat_history(chat_id, messages, user_id, scholarship=None, essay_type=None):
    with conn.cursor() as cur:
        cur.execute("""
            SELECT COUNT(*) FROM chat_histories 
            WHERE chat_history_id = %s AND user_id = %s
        """, (chat_id, user_id))
        if cur.fetchone()[0] == 0:
            raise ValueError("Chat ID does not belong to this user")
        
        # Update scholarship and essay_type if provided
        if scholarship and essay_type:
            cur.execute("""
                UPDATE chat_histories 
                SET scholarship = %s, essay_type = %s 
                WHERE chat_history_id = %s
            """, (scholarship, essay_type, chat_id))
        
        cur.execute("DELETE FROM messages WHERE chat_history_id = %s", (chat_id,))
        for message in messages:
            cur.execute("""
                INSERT INTO messages (chat_history_id, role, content)
                VALUES (%s, %s, %s)
            """, (chat_id, message["role"], message["content"]))
        conn.commit()