'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiFileText,
  FiEdit3,
  FiCheck,
  FiSearch,
  FiClock,
  FiAlertTriangle
} from 'react-icons/fi';
import { format } from 'date-fns';
import { HistoryEntry } from './HistoryEntry';

interface ResultCardsPanelProps {
  entries: HistoryEntry[];
  isLoading?: boolean;
  error?: string | null;
}

const ResultCardsPanel: React.FC<ResultCardsPanelProps> = ({
  entries,
  isLoading = false,
  error = null
}) => {
  // Get feature icon
  const getFeatureIcon = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {
    switch (featureType) {
      case 'ai-detector':
        return <FiSearch className="text-purple-500" size={18} />;
      case 'grammar-check':
        return <FiCheck className="text-green-500" size={18} />;
      case 'criteria-check':
        return <FiFileText className="text-blue-500" size={18} />;
      case 'paraphraser':
        return <FiEdit3 className="text-orange-500" size={18} />;
      default:
        return <FiFileText className="text-gray-500" size={18} />;
    }
  };

  // Get feature name
  const getFeatureName = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {
    switch (featureType) {
      case 'ai-detector':
        return 'AI Detector';
      case 'grammar-check':
        return 'Grammar Check';
      case 'criteria-check':
        return 'Criteria Check';
      case 'paraphraser':
        return 'Paraphraser';
      default:
        return 'Unknown Feature';
    }
  };

  // Get badge color
  const getBadgeColor = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {
    switch (featureType) {
      case 'ai-detector':
        return 'bg-purple-100 text-purple-700 border-purple-200';
      case 'grammar-check':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'criteria-check':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'paraphraser':
        return 'bg-orange-100 text-orange-700 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  // Render AI Detection result
  const renderAIDetectionResult = (resultContent: any) => {
    if (!resultContent) return <p className="text-gray-500">No AI detection results available.</p>;

    const overallScore = resultContent.ai_score * 100;

    return (
      <div>
        <div className="mb-4 flex items-center justify-between">
          <h4 className="text-sm font-medium">AI Content Detection</h4>
          <div className="text-lg font-bold">
            <span className={`${overallScore > 70 ? 'text-red-500' : overallScore > 30 ? 'text-yellow-500' : 'text-green-500'}`}>
              {overallScore.toFixed(1)}% AI
            </span>
          </div>
        </div>

        <div className="mt-2 p-3 bg-yellow-50 rounded-md text-sm">
          {resultContent.sentences?.map((sentence: any, index: number) => {
            const isAI = sentence.prediction === 'ai-generated' && sentence.ai_score > 0.5;
            return (
              <span key={index} className={isAI ? 'bg-yellow-200 px-1 rounded' : ''}>
                {sentence.text}{' '}
              </span>
            );
          })}
        </div>

        <div className="mt-4">
          <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
            <div
              className={`h-full ${overallScore > 70 ? 'bg-red-500' : overallScore > 30 ? 'bg-yellow-500' : 'bg-green-500'}`}
              style={{ width: `${overallScore}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs mt-1">
            <span>Human Content</span>
            <span>AI Content</span>
          </div>
        </div>
      </div>
    );
  };

  // Render Grammar Check result
  const renderGrammarResult = (resultContent: any) => {
    if (!resultContent) {
      return <p className="text-gray-500">No grammar check result.</p>;
    }

    // Show the corrected text if available, otherwise show original
    const correctedText = resultContent.corrected_text || resultContent.originalText || 'No corrected text available';

    return (
      <div>
        <div className="mb-3">
          <h4 className="text-sm font-medium mb-2">Grammar Check Result</h4>
          {resultContent.corrections && (
            <div className="text-xs text-gray-600">
              {resultContent.corrections.length} {resultContent.corrections.length === 1 ? 'correction' : 'corrections'} applied
            </div>
          )}
        </div>

        <div className="p-4 bg-green-50 rounded-md border border-green-200">
          <p className="text-sm text-gray-800 leading-relaxed">{correctedText}</p>
        </div>
      </div>
    );
  };

  // Render Paraphrase result
  const renderParaphraseResult = (resultContent: any) => {
    if (!resultContent || !resultContent.paraphrased_text) {
      return <p className="text-gray-500">No paraphrased text available.</p>;
    }

    return (
      <div>
        <h4 className="text-sm font-medium mb-3">Paraphrased Text</h4>
        <div className="p-3 bg-blue-50 rounded-md">
          <p className="text-sm leading-relaxed">{resultContent.paraphrased_text}</p>
        </div>
        {resultContent.options && resultContent.options.length > 0 && (
          <div className="mt-3">
            <h5 className="text-xs font-medium text-gray-600 mb-2">Alternative Options:</h5>
            <div className="space-y-2">
              {resultContent.options.slice(0, 2).map((option: string, index: number) => (
                <div key={index} className="p-2 bg-gray-50 rounded text-xs">
                  {option}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render Criteria Check result
  const renderCriteriaResult = (resultContent: any) => {
    if (!resultContent || !resultContent.matches) {
      return <p className="text-gray-500">No criteria analysis available.</p>;
    }

    return (
      <div>
        <h4 className="text-sm font-medium mb-3">Criteria Analysis</h4>
        <div className="space-y-3">
          {resultContent.matches.map((match: any, index: number) => (
            <div key={index} className="p-3 bg-gray-50 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">{match.criteria}</span>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  match.match_level === 'high'
                    ? 'bg-green-100 text-green-700'
                    : match.match_level === 'medium'
                    ? 'bg-yellow-100 text-yellow-700'
                    : 'bg-red-100 text-red-700'
                }`}>
                  {match.match_level}
                </span>
              </div>
              <p className="text-xs text-gray-600">{match.explanation}</p>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render result content based on feature type
  const renderResultContent = (entry: HistoryEntry) => {
    switch (entry.featureType) {
      case 'ai-detector':
        return renderAIDetectionResult(entry.resultContent);
      case 'grammar-check':
        return renderGrammarResult(entry.resultContent);
      case 'paraphraser':
        return renderParaphraseResult(entry.resultContent);
      case 'criteria-check':
        return renderCriteriaResult(entry.resultContent);
      default:
        return <p className="text-gray-500">Unknown feature type.</p>;
    }
  };

  if (error) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-red-600">
          <FiAlertTriangle className="mx-auto mb-2" size={24} />
          <p className="font-medium">Error</p>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className="text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  if (entries.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-gray-500">
          <FiFileText className="mx-auto mb-2" size={24} />
          <p>No results to display</p>
          <p className="text-sm mt-2">
            Select an input or feature from the history to view results
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto p-4 space-y-4">
      <AnimatePresence>
        {entries.map((entry) => (
          <motion.div
            key={entry.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          >
            {/* Card Header */}
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-3">
                    {getFeatureIcon(entry.featureType)}
                  </div>
                  <div>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getBadgeColor(entry.featureType)}`}>
                      {getFeatureName(entry.featureType)}
                    </span>
                  </div>
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <FiClock className="mr-1" size={12} />
                  {format(entry.timestamp, 'MMM d, h:mm a')}
                </div>
              </div>
            </div>

            {/* Card Content */}
            <div className="p-4">
              {renderResultContent(entry)}
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default ResultCardsPanel;
