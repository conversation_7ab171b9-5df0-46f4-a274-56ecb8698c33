# Python cache
__pycache__/
*.py[cod]

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# Environment files
.env

# Logs
logs/
*.log

# Workspace settings (optional, IDE-specific)
*.code-workspace

# Virtual environments (if created in project dir)
venv/
env/
ENV/

# Bytecode
*.pyc
*.pyo
*.pyd

# OS-specific files
.DS_Store
Thumbs.db

# Test output/cache
*.pytest_cache/
.pytest_cache/

# Backup files
*.bak
*.swp
*.swo
*~

# VS Code
.vscode/

# Ignore generated dependency files
*.egg-info/
dist/
build/
