'use client';

import React, { useState, useEffect, useRef, JSX } from 'react';
import axios from 'axios';
import { <PERSON><PERSON><PERSON>ck, FiMaximize2, FiMinimize2, FiRefreshCw } from 'react-icons/fi';

interface GrammarCheckComponentProps {
  text: string;
  onCorrectionsApplied?: (correctedText: string) => void;
  onSuggestionsFound?: (corrections: Correction[]) => void;
  onShowSuggestions?: (show: boolean) => void;
  showSuggestions?: boolean;
}

interface Correction {
  incorrect: string;
  suggestion: string;
  start: number;
  end: number;
  type: string;
  explanation: string;
  category?: 'misspelling' | 'correctness' | 'clarity' | 'engagement' | 'delivery';
  priority?: 'high' | 'medium' | 'low';
  id?: string;
}

interface GrammarCheckResult {
  corrections: Correction[];
  corrected_text?: string;
}

// Extend Window interface to include our custom functions
declare global {
  interface Window {
    applyGrammarCorrection?: (id: string) => void;
    applyAllGrammarCorrections?: () => void;
    dismissGrammarCorrection?: (id: string) => void;
  }
}

// Helper function to escape regex special characters (used in cleanupCorrections)
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Load and save handled corrections from/to localStorage
const loadHandledCorrections = (): Set<string> => {
  if (typeof window !== 'undefined' && window.localStorage) {
    const stored = localStorage.getItem('handledCorrections');
    return stored ? new Set(JSON.parse(stored)) : new Set();
  }
  return new Set();
};

const saveHandledCorrections = (handledCorrections: Set<string>) => {
  if (typeof window !== 'undefined' && window.localStorage) {
    localStorage.setItem('handledCorrections', JSON.stringify([...handledCorrections]));
  }
};

const GrammarCheckComponent: React.FC<GrammarCheckComponentProps> = ({
  text,
  onCorrectionsApplied,
  onSuggestionsFound,
  onShowSuggestions,
  showSuggestions = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<GrammarCheckResult | null>(null);
  const [correctedText, setCorrectedText] = useState<string>(text);
  const [selectedCorrection, setSelectedCorrection] = useState<Correction | null>(null);
  const [handledCorrections, setHandledCorrections] = useState<Set<string>>(new Set());
  const [hasOverflow, setHasOverflow] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const textContainerRef = useRef<HTMLDivElement>(null);
  const [originalText, setOriginalText] = useState<string>(text);
  const [correctionMap, setCorrectionMap] = useState<Map<string, Correction>>(new Map());

  // Load handledCorrections from localStorage on mount
  useEffect(() => {
    setHandledCorrections(loadHandledCorrections());
  }, []);

  // Update correctedText when text prop changes
  useEffect(() => {
    setCorrectedText(text);
  }, [text]);

  // Check for overflow when text changes
  useEffect(() => {
    const checkForOverflow = () => {
      if (textContainerRef.current) {
        const hasVerticalOverflow = textContainerRef.current.scrollHeight > textContainerRef.current.clientHeight;
        setHasOverflow(hasVerticalOverflow);
      }
    };

    // Use a small delay to ensure the DOM has updated
    const timer = setTimeout(() => {
      checkForOverflow();
    }, 100);

    // Add resize listener to recheck on window resize
    window.addEventListener('resize', checkForOverflow);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', checkForOverflow);
    };
  }, [correctedText, result]);

  // Expose correction functions to window object for external access
  useEffect(() => {
    // Add the correction functions to the window object
    window.applyGrammarCorrection = (id: string) => {
      const correction = result?.corrections.find(c => c.id === id);
      if (correction) {
        applyCorrection(correction);
      }
    };

    window.applyAllGrammarCorrections = () => {
      if (result) {
        applyAllCorrections();
      }
    };

    window.dismissGrammarCorrection = (id: string) => {
      const correction = result?.corrections.find(c => c.id === id);
      if (correction) {
        dismissCorrection(correction);
      }
    };

    // Clean up when component unmounts
    return () => {
      window.applyGrammarCorrection = undefined;
      window.applyAllGrammarCorrections = undefined;
      window.dismissGrammarCorrection = undefined;
    };
  }, [result]);

  // Simulate loading progress
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isLoading) {
      setLoadingProgress(0);
      interval = setInterval(() => {
        setLoadingProgress((prev) => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 200);
    }
    return () => clearInterval(interval);
  }, [isLoading]);

  // Function to check grammar
  const checkGrammar = async () => {
    if (!text.trim()) {
      setError('Please enter some text to check');
      return;
    }

    setIsLoading(true);
    setError(null);

    // Store the original text
    setOriginalText(text);

    // Reset corrected text to original text
    setCorrectedText(text);

    try {
      // Check cache first
      if (typeof window !== 'undefined' && window.localStorage) {
        const cacheKey = `grammarCache_${text}`;
        const cachedResult = localStorage.getItem(cacheKey);
        if (cachedResult) {
          const data: GrammarCheckResult = JSON.parse(cachedResult);
          processGrammarResults(data);
          setIsLoading(false);
          setLoadingProgress(100);
          return;
        }
      }

      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
      const response = await axios.post(`${API_URL}/grammar-check`, { text });

      if (response.data) {
        processGrammarResults(response.data);

        // Cache the result
        if (typeof window !== 'undefined' && window.localStorage) {
          localStorage.setItem(`grammarCache_${text}`, JSON.stringify(response.data));
        }
      }
    } catch (err: any) {
      console.error('Grammar check error:', err);
      setError(err.message || 'An error occurred during grammar checking');
    } finally {
      setIsLoading(false);
      setLoadingProgress(100);
    }
  };

  // Process grammar check results
  const processGrammarResults = (data: GrammarCheckResult) => {
    // First, ensure we have valid corrections
    if (!data.corrections || !Array.isArray(data.corrections)) {
      console.error('Invalid corrections data:', data);
      setError('Invalid grammar check results received');
      return;
    }

    // Clean up the corrections to ensure they don't overlap
    const cleanedCorrections = cleanupCorrections(data.corrections);

    // Create a map of corrections for easier access
    const newCorrectionMap = new Map<string, Correction>();

    // Assign categories and priorities to corrections
    const enhancedCorrections = cleanedCorrections.map(correction => {
      // Generate a unique ID for each correction
      const id = `${correction.incorrect}-${correction.start}-${correction.end}`;

      // Assign a category based on the type
      let category: 'misspelling' | 'correctness' | 'clarity' | 'engagement' | 'delivery' = 'correctness';
      if (correction.type === 'spelling') {
        category = 'misspelling';
      } else if (correction.type === 'grammar') {
        category = 'correctness';
      } else if (correction.type === 'style') {
        category = 'clarity';
      } else if (correction.type === 'punctuation') {
        category = 'delivery';
      }

      // Assign a priority
      let priority: 'high' | 'medium' | 'low' = 'medium';
      if (category === 'misspelling' || category === 'correctness') {
        priority = 'high';
      } else if (category === 'clarity') {
        priority = 'medium';
      } else {
        priority = 'low';
      }

      // Ensure suggestion is clean
      const suggestion = correction.suggestion?.trim() || correction.incorrect;

      const enhancedCorrection = {
        ...correction,
        suggestion,
        category,
        priority,
        id
      };

      // Add to map
      newCorrectionMap.set(id, enhancedCorrection);

      return enhancedCorrection;
    });

    // Set the correction map
    setCorrectionMap(newCorrectionMap);

    // Filter out handled corrections
    const filteredCorrections = enhancedCorrections.filter(
      correction => !handledCorrections.has(correction.id!)
    );

    // Sort by position
    const sortedCorrections = [...filteredCorrections].sort((a, b) => a.start - b.start);

    // Set the result
    setResult({
      ...data,
      corrections: sortedCorrections
    });

    // Notify parent component about the suggestions
    if (onSuggestionsFound && sortedCorrections.length > 0) {
      onSuggestionsFound(sortedCorrections);
    }
  };

  // Clean up corrections to ensure they don't overlap
  const cleanupCorrections = (corrections: Correction[]): Correction[] => {
    if (!corrections.length) return [];

    // Sort by position
    const sorted = [...corrections].sort((a, b) => a.start - b.start);
    const result: Correction[] = [];

    // Track the last end position
    let lastEnd = -1;

    for (const correction of sorted) {
      // Skip invalid corrections
      if (correction.start >= correction.end ||
          correction.start < 0 ||
          correction.end > text.length ||
          !correction.incorrect ||
          correction.incorrect.trim() === '') {
        continue;
      }

      // Skip overlapping corrections
      if (correction.start < lastEnd) {
        continue;
      }

      // Verify the incorrect text matches the actual text at that position
      const actualText = text.substring(correction.start, correction.end);
      if (actualText !== correction.incorrect) {
        // Try to find the correct position using regex to handle special characters
        const escapedIncorrect = escapeRegExp(correction.incorrect);
        const regex = new RegExp(escapedIncorrect, 'g');
        const match = regex.exec(text);

        if (match) {
          // Update the position
          correction.start = match.index;
          correction.end = match.index + correction.incorrect.length;
        } else {
          // Try a simple indexOf as fallback
          const index = text.indexOf(correction.incorrect);
          if (index >= 0) {
            // Update the position
            correction.start = index;
            correction.end = index + correction.incorrect.length;
          } else {
            // Skip this correction if we can't find it
            continue;
          }
        }
      }

      // Add to result and update lastEnd
      result.push(correction);
      lastEnd = correction.end;
    }

    return result;
  };

  // Function to adjust correction positions after a correction is applied
  const adjustCorrectionPositions = (
    corrections: Correction[],
    start: number,
    end: number,
    newLength: number
  ): Correction[] => {
    const lengthDiff = newLength - (end - start);

    return corrections.map(correction => {
      // If the correction is after the changed text, adjust its position
      if (correction.start >= end) {
        return {
          ...correction,
          start: correction.start + lengthDiff,
          end: correction.end + lengthDiff
        };
      }
      // If the correction overlaps with the changed text, skip it
      // This shouldn't happen with our cleanup logic, but just in case
      if (correction.start < end && correction.end > start) {
        return correction;
      }
      // Otherwise, leave it unchanged
      return correction;
    });
  };

  // Apply a single correction
  const applyCorrection = (correction: Correction) => {
    // Create a new value with the suggestion applied
    const before = correctedText.substring(0, correction.start);
    const after = correctedText.substring(correction.end);
    const newText = before + correction.suggestion + after;

    // Set the new output text
    setCorrectedText(newText);

    // Add to handled corrections
    if (correction.id) {
      setHandledCorrections(prev => {
        const newSet = new Set(prev);
        newSet.add(correction.id!);
        saveHandledCorrections(newSet);
        return newSet;
      });
    }

    // Update the result by removing the applied correction
    if (result) {
      // First remove the applied correction
      const updatedCorrections = result.corrections.filter(
        c => c.id !== correction.id
      );

      // Calculate the length difference
      const lengthDiff = correction.suggestion.length - (correction.end - correction.start);

      // Adjust positions for corrections that come after the applied one
      const adjustedCorrections = updatedCorrections.map(c => {
        if (c.start > correction.end) {
          return {
            ...c,
            start: c.start + lengthDiff,
            end: c.end + lengthDiff
          };
        }
        return c;
      });

      setResult({
        ...result,
        corrections: adjustedCorrections
      });

      // Notify parent component about the updated suggestions
      if (onSuggestionsFound) {
        onSuggestionsFound(adjustedCorrections);
      }
    }

    // Clear selected correction
    setSelectedCorrection(null);

    // We're NOT calling onCorrectionsApplied here to keep input text unchanged
    // But we need to update the UI to show the corrected text
    // This is handled by the correctedText state
  };

  // Dismiss a correction without applying it - used by the suggestion panel
  const dismissCorrection = (correction: Correction) => {
    // Add to handled corrections
    if (correction.id) {
      setHandledCorrections(prev => {
        const newSet = new Set(prev);
        newSet.add(correction.id!);
        saveHandledCorrections(newSet);
        return newSet;
      });
    }

    // Update the result by removing the dismissed correction
    if (result) {
      const updatedCorrections = result.corrections.filter(
        c => c.id !== correction.id
      );

      setResult({
        ...result,
        corrections: updatedCorrections
      });

      // Notify parent component about the updated suggestions
      if (onSuggestionsFound) {
        onSuggestionsFound(updatedCorrections);
      }
    }

    // Clear selected correction
    setSelectedCorrection(null);
  };

  // Apply all corrections at once
  const applyAllCorrections = () => {
    if (!result) return;

    // Sort corrections by position in reverse order to avoid position shifts
    const sortedCorrections = [...result.corrections].sort((a, b) => b.start - a.start);

    // Start with the current corrected text
    let newText = correctedText;

    // Apply all corrections
    for (const correction of sortedCorrections) {
      // Verify the position is valid
      if (correction.start < 0 || correction.end > newText.length || correction.start >= correction.end) {
        continue;
      }

      const before = newText.substring(0, correction.start);
      const after = newText.substring(correction.end);
      newText = before + correction.suggestion + after;

      // Add to handled corrections
      if (correction.id) {
        setHandledCorrections(prev => {
          const newSet = new Set(prev);
          newSet.add(correction.id!);
          saveHandledCorrections(newSet);
          return newSet;
        });
      }
    }

    // Set the new output text
    setCorrectedText(newText);

    // Clear all corrections
    setResult({
      ...result,
      corrections: []
    });

    // Clear selected correction
    setSelectedCorrection(null);

    // Notify parent component that all suggestions have been applied
    if (onSuggestionsFound) {
      onSuggestionsFound([]);
    }

    // We're NOT calling onCorrectionsApplied here to keep input text unchanged
  };

  // Reset handled corrections
  const resetHandledCorrections = () => {
    setHandledCorrections(new Set());
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.removeItem('handledCorrections');
    }
    // Re-check grammar to show all corrections again
    checkGrammar();
  };

  // Render the text with highlighted corrections
  const renderHighlightedText = () => {
    if (!result || result.corrections.length === 0) {
      return <p className="whitespace-pre-line">{correctedText}</p>;
    }

    // Create segments of text with highlighted corrections
    const segments: JSX.Element[] = [];
    let lastIndex = 0;

    // Make a copy and ensure they're sorted by position
    const sortedCorrections = [...result.corrections].sort((a, b) => a.start - b.start);

    // If the text is very long, limit the number of corrections to improve performance
    const maxCorrections = 100;
    const hasTooManyCorrections = sortedCorrections.length > maxCorrections;

    if (hasTooManyCorrections) {
      console.warn(`Text has ${sortedCorrections.length} corrections, limiting to ${maxCorrections} for performance.`);
    }

    // Use a limited set of corrections if there are too many
    const displayCorrections = hasTooManyCorrections
      ? sortedCorrections.slice(0, maxCorrections)
      : sortedCorrections;

    displayCorrections.forEach((correction, index) => {
      // Add text before the correction
      if (correction.start > lastIndex) {
        segments.push(
          <span key={`text-${index}`}>
            {correctedText.substring(lastIndex, correction.start)}
          </span>
        );
      }

      // Determine the style based on the category
      let underlineStyle = 'border-b-2 border-red-500';
      if (correction.category === 'misspelling' || correction.category === 'correctness') {
        underlineStyle = 'border-b-2 border-red-500';
      } else if (correction.category === 'clarity') {
        underlineStyle = 'border-b-2 border-blue-500';
      } else if (correction.category === 'engagement') {
        underlineStyle = 'border-b-2 border-purple-500';
      } else if (correction.category === 'delivery') {
        underlineStyle = 'border-b-2 border-green-500';
      }

      // Add the highlighted correction
      segments.push(
        <span
          key={`correction-${index}`}
          className={`${underlineStyle} cursor-pointer`}
          title={`Suggestion: ${correction.suggestion}`}
          onClick={() => {
            setSelectedCorrection(correction);
            // Notify parent component about the selected correction
            if (onSuggestionsFound) {
              onSuggestionsFound([...result.corrections]);
            }
          }}
        >
          {correction.incorrect}
        </span>
      );

      lastIndex = correction.end;
    });

    // Add any remaining text
    if (lastIndex < correctedText.length) {
      segments.push(
        <span key="text-end">
          {correctedText.substring(lastIndex)}
        </span>
      );
    }

    return <div className="leading-relaxed whitespace-pre-line">{segments}</div>;
  };

  return (
    <div className="w-full h-full flex flex-col">
      {/* Grammar check results */}
      <div className="flex-1 flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <h3 className="text-lg font-medium text-blue-700">Grammar Check</h3>
            {result && result.corrections.length > 0 && (
              <div className="ml-3 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                {result.corrections.length} {result.corrections.length === 1 ? 'issue' : 'issues'}
              </div>
            )}
            {!showSuggestions && result && result.corrections.length > 0 && (
              <button
                onClick={() => onShowSuggestions && onShowSuggestions(true)}
                className="ml-3 text-blue-600 hover:text-blue-800 text-sm flex items-center"
              >
                <span className="mr-1">Show suggestions</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </button>
            )}
          </div>

          <div className="flex space-x-2">
            {result && result.corrections.length > 0 && (
              <button
                onClick={resetHandledCorrections}
                className="text-gray-600 hover:text-gray-800 text-sm"
              >
                Reset ignored
              </button>
            )}
            <button
              onClick={checkGrammar}
              disabled={isLoading || !text.trim()}
              className={`px-4 py-1.5 rounded-md ${
                isLoading || !text.trim()
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700 transition-colors'
              }`}
            >
              {isLoading ? "Checking..." : "Check Grammar"}
            </button>

            <button
              onClick={() => setIsFullScreen(!isFullScreen)}
              className="ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
              title={isFullScreen ? "Exit full screen" : "Full screen"}
            >
              {isFullScreen ? <FiMinimize2 size={16} /> : <FiMaximize2 size={16} />}
            </button>
          </div>
        </div>

        {error && (
          <div className="p-3 bg-red-50 text-red-700 rounded-md mb-4">
            {error}
          </div>
        )}

        {/* Category legend */}
        {result && result.corrections.length > 0 && (
          <div className="flex flex-wrap gap-3 mb-3">
            <div className="flex items-center">
              <span className="w-3 h-3 rounded-full bg-red-500 mr-1"></span>
              <span className="text-xs text-gray-600">Correctness</span>
            </div>
            <div className="flex items-center">
              <span className="w-3 h-3 rounded-full bg-blue-500 mr-1"></span>
              <span className="text-xs text-gray-600">Clarity</span>
            </div>
            <div className="flex items-center">
              <span className="w-3 h-3 rounded-full bg-purple-500 mr-1"></span>
              <span className="text-xs text-gray-600">Engagement</span>
            </div>
            <div className="flex items-center">
              <span className="w-3 h-3 rounded-full bg-green-500 mr-1"></span>
              <span className="text-xs text-gray-600">Delivery</span>
            </div>
          </div>
        )}

        {/* Text with highlighted corrections */}
        <div className={`${isFullScreen ? 'fixed inset-0 z-50 bg-white p-6' : 'flex-1 w-full'} font-sans flex flex-col overflow-hidden`}>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 relative flex-1 overflow-hidden">
            {hasOverflow && (
              <div className="absolute top-2 right-2 z-10 text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded-md flex items-center">
                <span className="mr-1">Scroll for more</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 5v14M5 12l7 7 7-7"/>
                </svg>
              </div>
            )}
            <div
              ref={textContainerRef}
              className="text-gray-900 text-base overflow-auto h-full max-h-[calc(100vh-370px)] min-h-auto relative"
              style={{ height: 'calc(100vh - 180px)' }}
            >
              {hasOverflow && (
                <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none" />
              )}

              {/* Warning for very long text */}
              {(() => {
                // Check if text is very long (more than 5000 characters)
                if (correctedText.length > 5000) {
                  return (
                    <div className="absolute top-0 left-0 right-0 bg-yellow-50 text-yellow-800 text-xs p-2 border-b border-yellow-200">
                      This text is very long ({correctedText.length} characters). Scroll to see all content.
                    </div>
                  );
                }
                return null;
              })()}

              {/* Warning for too many corrections */}
              {(() => {
                if (result && result.corrections.length > 100) {
                  return (
                    <div className="absolute top-0 left-0 right-0 bg-yellow-50 text-yellow-800 text-xs p-2 border-b border-yellow-200 mt-8">
                      Found {result.corrections.length} issues. Only showing the first 100 for performance.
                    </div>
                  );
                }
                return null;
              })()}

              <div className={`${correctedText.length > 5000 || (result && result.corrections.length > 100) ? 'pt-16' : ''}`}>
                {isLoading ? (
                  <div className="flex flex-col justify-center items-center h-40">
                    <div className="w-full max-w-md h-2 bg-gray-200 rounded-full overflow-hidden mb-3">
                      <div
                        className="h-full bg-blue-600 transition-all duration-300"
                        style={{ width: `${loadingProgress}%` }}
                      ></div>
                    </div>
                    <span className="text-gray-600">Checking grammar... {Math.round(loadingProgress)}%</span>
                  </div>
                ) : (
                  <div className="h-full overflow-auto">
                    {renderHighlightedText()}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>



        {/* We've removed the selected correction details from here as they'll be shown in the suggestion panel */}

        {/* Word/sentence count footer */}
        <div className="mt-2 flex justify-between items-center text-sm text-gray-500 pt-2">
          <div className="flex items-center space-x-3">
            <span>
              {correctedText.split(/\s+/).filter(Boolean).length} Words
            </span>
            <span>
              {correctedText.length} Characters
            </span>
            <span>
              {correctedText.split(/[.!?]+/).filter(Boolean).length} Sentences
            </span>
            {result && result.corrections.length > 0 && (
              <span className="text-yellow-600 font-medium">
                {result.corrections.length} Issues
              </span>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <button className="p-1 hover:bg-gray-100 rounded" onClick={() => checkGrammar()}>
              <FiRefreshCw size={16} />
            </button>
          </div>
        </div>

        {/* Legend */}
        {result && result.corrections.length > 0 && (
          <div className="mt-1 flex items-center justify-center space-x-6 text-xs text-gray-600">
            <div className="flex items-center">
              <span className="w-2 h-2 border-b-2 border-red-500 mr-1"></span>
              <span>Spelling/Grammar</span>
            </div>
            <div className="flex items-center">
              <span className="w-2 h-2 border-b-2 border-blue-500 mr-1"></span>
              <span>Clarity</span>
            </div>
            <div className="flex items-center">
              <span className="w-2 h-2 border-b-2 border-purple-500 mr-1"></span>
              <span>Engagement</span>
            </div>
            <div className="flex items-center">
              <span className="w-2 h-2 border-b-2 border-green-500 mr-1"></span>
              <span>Delivery</span>
            </div>
          </div>
        )}

        {/* Apply all corrections button */}
        {result && result.corrections.length > 0 && !selectedCorrection && (
          <div className="mt-2 flex justify-end">
            <button
              onClick={applyAllCorrections}
              className="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm flex items-center"
            >
              <FiCheck className="mr-1" /> Apply All Corrections
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default GrammarCheckComponent;










