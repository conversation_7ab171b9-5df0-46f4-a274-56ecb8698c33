'use client';

import React, { useState, useEffect, useRef } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { FiCheck, FiX, FiAlertTriangle, FiEdit3, FiRefreshCw, FiSearch, FiFileText } from 'react-icons/fi';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

// Import our custom components
import GrammarCheckComponent from '../components/text-analyzer/GrammarCheckComponent';
import CriteriaCheckingComponent from '../components/text-analyzer/CriteriaCheckingComponent';
import HierarchicalHistorySidebar from '../components/text-analyzer/HierarchicalHistorySidebar';
import ResultCardsPanel from '../components/text-analyzer/ResultCardsPanel';
import AiDetectorResultPanel from '../components/text-analyzer/AiDetectorResultPanel';
import { HistoryEntry, GroupedHistoryEntry } from '../components/text-analyzer/HistoryEntry';
import Paraphraser from '../components/text-analyzer/Paraphraser';

// Types for our analysis results
type IssueType = 'grammar' | 'spelling' | 'ai-content' | 'criteria';

interface Issue {
  id: string;
  type: IssueType;
  original: string;
  suggestion: string;
  position: { start: number; end: number };
  explanation?: string;
  category?: 'misspelling' | 'correctness' | 'clarity' | 'engagement' | 'delivery';
}

interface AIAnalysisResult {
  ai_score: number;
  classification: string;
  human_score: number;
  sentences: {
    text: string;
    ai_score: number;
    prediction: 'original' | 'ai-generated';
  }[];
}

interface GrammarCheckResult {
  corrections: {
    incorrect: string;
    suggestion: string;
    start: number;
    end: number;
    type: string;
    explanation: string;
  }[];
  corrected_text?: string;
}

interface ParaphraseResult {
  paraphrased_text: string;
  options?: string[];
}

interface CriteriaCheckResult {
  content: string;
  matches: {
    criteria: string;
    match_level: 'high' | 'medium' | 'low';
    explanation: string;
  }[];
}

const TextAnalyzer = () => {
  // State for analysis results
  const [activeTab, setActiveTab] = useState<'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser'>('ai-detector');
  const [text, setText] = useState<string>('I have went to the store yesterday and buyed some grocerys. Their was alot of people their.');
  const [issues, setIssues] = useState<Issue[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(true);
  // Column width states
  const [inputColumnWidth, setInputColumnWidth] = useState(40); // Default width in percentage
  const [resultColumnWidth, setResultColumnWidth] = useState(60); // Default width in percentage
  const [suggestionColumnWidth, setSuggestionColumnWidth] = useState(30); // Default width in percentage

  // Results for different analysis types
  const [aiDetectionResult, setAiDetectionResult] = useState<AIAnalysisResult | null>(null);
  const [grammarResult, setGrammarResult] = useState<GrammarCheckResult | null>(null);
  const [paraphraseResult, setParaphraseResult] = useState<ParaphraseResult | null>(null);
  const [criteriaResult, setCriteriaResult] = useState<CriteriaCheckResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // History tracking
  const [historyEntries, setHistoryEntries] = useState<HistoryEntry[]>([]);
  const [currentInputId, setCurrentInputId] = useState<string>(uuidv4());
  const [showHistorySidebar, setShowHistorySidebar] = useState<boolean>(false);
  const [activeInputId, setActiveInputId] = useState<string | null>(null);
  const [activeFeatureId, setActiveFeatureId] = useState<string | null>(null);
  const [selectedEntries, setSelectedEntries] = useState<HistoryEntry[]>([]);

  // Paraphraser options
  const [paraphraseStyle, setParaphraseStyle] = useState<'standard' | 'formal' | 'simple' | 'creative'>('standard');

  // Criteria check options
  const [scholarshipType, setScholarshipType] = useState<string>('academic');
  const [essayType, setEssayType] = useState<string>('personal_statement');

  // Calculate word count
  const wordCount = text.split(/\s+/).filter(Boolean).length;

  // Group history entries by input text
  const groupedHistoryEntries: GroupedHistoryEntry[] = React.useMemo(() => {
    const groups: { [key: string]: HistoryEntry[] } = {};

    // Group entries by exact input text match
    historyEntries.forEach(entry => {
      const key = entry.inputText.trim();
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(entry);
    });

    // Convert to GroupedHistoryEntry array and sort by most recent
    return Object.entries(groups)
      .map(([inputText, features]) => {
        // Sort features by timestamp (most recent first)
        const sortedFeatures = features.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

        return {
          inputText,
          inputId: `input-${inputText.substring(0, 20).replace(/\s+/g, '-')}`, // Create a stable ID
          timestamp: sortedFeatures[0].timestamp, // Use most recent feature timestamp
          features: sortedFeatures
        };
      })
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()); // Sort groups by most recent
  }, [historyEntries]);

  // Load text from localStorage and handle URL parameters
  useEffect(() => {
    // Check if we're in the browser
    if (typeof window !== 'undefined') {
      // Get the tab from URL query parameters
      const params = new URLSearchParams(window.location.search);
      const tabParam = params.get('tab');

      // Set the active tab if it's valid
      if (tabParam && ['ai-detector', 'grammar-check', 'criteria-check', 'paraphraser'].includes(tabParam as any)) {
        setActiveTab(tabParam as 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser');
      }

      // Load saved text from localStorage
      const savedText = localStorage.getItem('scholarar_input_text');
      if (savedText) {
        setText(savedText);
        // Clear the localStorage after loading to avoid reusing the same text unintentionally
        localStorage.removeItem('scholarar_input_text');
      }
    }
  }, []);

  // Function to analyze text based on active tab
  const analyzeText = async () => {
    if (!text.trim()) return;

    setIsAnalyzing(true);
    setError(null);

    // Clear selected entries to show the normal interface
    setSelectedEntries([]);
    setActiveFeatureId(null);
    setActiveInputId(null);

    try {
      switch (activeTab) {
        case 'ai-detector':
          await analyzeAIContent();
          break;
        case 'grammar-check':
          await checkGrammar();
          break;
        case 'paraphraser':
          await paraphraseText();
          break;
        case 'criteria-check':
          await checkCriteria();
          break;
      }
    } catch (err: any) {
      console.error('Analysis error:', err);
      setError(err.message || 'An error occurred during analysis');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // AI Detection analysis
  const analyzeAIContent = async () => {
    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
      const effectiveText = getEffectiveInputText();
      const response = await axios.post(`${API_URL}/detect-ai`, { text: effectiveText });

      if (response.data) {
        setAiDetectionResult(response.data);

        // Convert AI detection results to issues
        const aiIssues: Issue[] = response.data.sentences
          .filter((sentence: any) => sentence.prediction === 'ai-generated' && sentence.ai_score > 0.7)
          .map((sentence: any, index: number) => {
            const startPos = effectiveText.indexOf(sentence.text);
            return {
              id: `ai-${index}`,
              type: 'ai-content',
              original: sentence.text,
              suggestion: sentence.text, // No suggestion for AI content, just highlighting
              position: {
                start: startPos,
                end: startPos + sentence.text.length
              },
              explanation: `AI probability: ${(sentence.ai_score * 100).toFixed(1)}%`
            };
          });

        setIssues(aiIssues);

        // Add to history (use original input text for grouping, but store the effective text used)
        addToHistory('ai-detector', response.data);
      }
    } catch (err) {
      console.error('AI detection error:', err);
      throw err;
    }
  };

  // Grammar check analysis - now fully handled by the GrammarCheckComponent
  const checkGrammar = async () => {
    try {
      // We don't need to do anything here anymore
      // The GrammarCheckComponent handles everything internally
    } catch (err) {
      console.error('Grammar check error:', err);
      throw err;
    }
  };

  // Paraphrasing analysis - now handled by the ParaphrasingComponent
  const paraphraseText = async () => {
    try {
      // We'll just set issues to empty since the component handles the API call
      setIssues([]);
    } catch (err) {
      console.error('Paraphrasing error:', err);
      throw err;
    }
  };

  // Criteria check analysis - now handled by the CriteriaCheckingComponent
  const checkCriteria = async () => {
    try {
      // We'll just set issues to empty since the component handles the API call
      setIssues([]);
    } catch (err) {
      console.error('Criteria check error:', err);
      throw err;
    }
  };

  // Apply a suggestion to the text (only updates the output, not the input)
  const applySuggestion = (issue: Issue) => {
    // We don't modify the input text, only update the output in the grammar component
    // This is handled by the GrammarCheckComponent internally

    // Remove the issue from the list
    setIssues(issues.filter(i => i.id !== issue.id));

    // If we're in grammar check mode, update the analysis after applying the suggestion
    if (activeTab === 'grammar-check' && grammarResult) {
      const updatedCorrections = grammarResult.corrections.filter(
        c => !(c.start === issue.position.start && c.end === issue.position.end)
      );
      setGrammarResult({
        ...grammarResult,
        corrections: updatedCorrections
      });

      // Call the apply correction function in the grammar component
      if (issue.id && typeof window !== 'undefined' && 'applyGrammarCorrection' in window) {
        (window as any).applyGrammarCorrection(issue.id);
      }
    }
  };

  // We no longer need this function as the GrammarCheckComponent handles everything internally

  // State to track which column is being resized
  const [isResizing, setIsResizing] = useState<'input' | 'result' | null>(null);

  // Handle resizing of the input column
  const startInputResize = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing('input');
    const startX = e.clientX;
    const startInputWidth = inputColumnWidth;
    const startResultWidth = resultColumnWidth;

    const handleMouseMove = (e: MouseEvent) => {
      const containerWidth = document.querySelector('main')?.clientWidth || 1000;
      const delta = ((e.clientX - startX) / containerWidth) * 100;

      // Calculate new widths ensuring they stay within reasonable bounds
      const newInputWidth = Math.max(20, Math.min(70, startInputWidth + delta));
      const newResultWidth = Math.max(30, Math.min(80, startResultWidth - delta));

      // Update column widths
      setInputColumnWidth(newInputWidth);
      setResultColumnWidth(newResultWidth);
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      setIsResizing(null);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle resizing of the result column when suggestion panel is visible
  const startResultResize = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing('result');
    const startX = e.clientX;
    const startResultWidth = resultColumnWidth;
    const startSuggestionWidth = suggestionColumnWidth;

    const handleMouseMove = (e: MouseEvent) => {
      const containerWidth = document.querySelector('main')?.clientWidth || 1000;
      const delta = ((e.clientX - startX) / containerWidth) * 100;

      // Calculate new widths ensuring they stay within reasonable bounds
      const newResultWidth = Math.max(20, Math.min(60, startResultWidth + delta));
      const newSuggestionWidth = Math.max(20, Math.min(60, startSuggestionWidth - delta));

      // Update column widths
      setResultColumnWidth(newResultWidth);
      setSuggestionColumnWidth(newSuggestionWidth);
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      setIsResizing(null);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Accept all suggestions at once (only updates the output, not the input)
  const acceptAllSuggestions = () => {
    // We don't modify the input text, only update the output in the grammar component
    // This is handled by the GrammarCheckComponent internally

    // Clear the issues list
    setIssues([]);

    // Clear the specific result based on active tab
    if (activeTab === 'grammar-check') {
      setGrammarResult(prev => prev ? {...prev, corrections: []} : null);

      // Call the apply all corrections function in the grammar component
      if (typeof window !== 'undefined' && 'applyAllGrammarCorrections' in window) {
        (window as any).applyAllGrammarCorrections();
      }
    }
  };

  // Clear the editor and start a new analysis
  const clearEditor = () => {
    setText('');
    setIssues([]);
    setAiDetectionResult(null);
    setGrammarResult(null);
    setParaphraseResult(null);
    setCriteriaResult(null);
    setError(null);
    setCurrentInputId(uuidv4());
    setActiveInputId(null);
    setActiveFeatureId(null);
    setSelectedEntries([]);
  };

  // Handle tab change - input text never changes automatically
  const handleTabChange = (tab: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {
    setActiveTab(tab);
    setIssues([]);

    // Clear current results when switching tabs
    setAiDetectionResult(null);
    setGrammarResult(null);
    setParaphraseResult(null);
    setCriteriaResult(null);
    setError(null);

    // Clear history selection to show normal interface
    setSelectedEntries([]);
    setActiveFeatureId(null);
    setActiveInputId(null);
  };

  // Add entry to history - create a new entry only when switching features or when input text changes
  const addToHistory = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser', resultContent: any) => {
    const outputText = getOutputText(featureType, resultContent);

    // Check if this is the same feature as the most recent entry with the same input text
    const mostRecentEntry = historyEntries[0];
    const isSameFeatureAndInput = mostRecentEntry &&
      mostRecentEntry.featureType === featureType &&
      mostRecentEntry.inputText.trim() === text.trim();

    if (isSameFeatureAndInput) {
      // Update the existing entry instead of creating a new one
      const updatedEntry: HistoryEntry = {
        ...mostRecentEntry,
        timestamp: new Date(),
        resultContent,
        outputText
      };

      setHistoryEntries(prev => [updatedEntry, ...prev.slice(1)]);
    } else {
      // Create a new entry for different feature or different input
      const newEntry: HistoryEntry = {
        id: uuidv4(),
        timestamp: new Date(),
        featureType,
        inputText: text, // Always use the original input text for grouping
        resultContent,
        outputText
      };

      // Add to history (most recent first)
      setHistoryEntries(prev => [newEntry, ...prev]);
    }

    // Clear selected entries to show the normal interface with the new result
    setSelectedEntries([]);
    setActiveFeatureId(null);
    setActiveInputId(null);
  };

  // Toggle history sidebar
  const toggleHistorySidebar = () => {
    setShowHistorySidebar(prev => !prev);
  };

  // Check if there are current results for the active tab
  const hasCurrentResults = () => {
    switch (activeTab) {
      case 'ai-detector':
        return aiDetectionResult !== null;
      case 'grammar-check':
        return grammarResult !== null;
      case 'paraphraser':
        return paraphraseResult !== null;
      case 'criteria-check':
        return criteriaResult !== null;
      default:
        return false;
    }
  };

  // Get output text from a feature result
  const getOutputText = (featureType: string, resultContent: any): string => {
    switch (featureType) {
      case 'grammar-check':
        return resultContent?.corrected_text || text;
      case 'paraphraser':
        return resultContent?.paraphrased_text || text;
      case 'criteria-check':
        return resultContent?.content || text;
      case 'ai-detector':
        return text; // AI detector doesn't modify text
      default:
        return text;
    }
  };



  // Get the effective input text for analysis (uses output from the most recent feature)
  const getEffectiveInputText = (): string => {
    // Use the output from the most recent history entry if available
    if (historyEntries.length > 0) {
      const mostRecentEntry = historyEntries[0]; // Most recent is first
      if (mostRecentEntry.outputText && mostRecentEntry.outputText !== text) {
        return mostRecentEntry.outputText;
      }
    }

    // Default to the original input text
    return text;
  };

  // Handle input click - show all features for this input
  const handleInputClick = (inputId: string) => {
    const group = groupedHistoryEntries.find(g => g.inputId === inputId);
    if (group) {
      setText(group.inputText);
      setActiveInputId(inputId);
      setActiveFeatureId(null);
      setSelectedEntries(group.features);

      // Clear current results to show the cards panel
      setAiDetectionResult(null);
      setGrammarResult(null);
      setParaphraseResult(null);
      setCriteriaResult(null);
      setIssues([]);
    }
  };



  // Handle feature click - show only this specific feature result
  const handleFeatureClick = (featureId: string) => {
    const entry = historyEntries.find(e => e.id === featureId);
    if (entry) {
      setText(entry.inputText);
      setActiveFeatureId(featureId);
      setSelectedEntries([entry]);

      // Find the input group for this entry
      const inputGroup = groupedHistoryEntries.find(group =>
        group.inputText.trim() === entry.inputText.trim()
      );
      if (inputGroup) {
        setActiveInputId(inputGroup.inputId);
      }

      // Set the active tab to match the feature
      setActiveTab(entry.featureType);

      // Restore the result based on feature type for the main interface
      switch (entry.featureType) {
        case 'ai-detector':
          setAiDetectionResult(entry.resultContent);
          break;
        case 'grammar-check':
          setGrammarResult(entry.resultContent);
          if (entry.resultContent?.corrections) {
            const newIssues = entry.resultContent.corrections.map((correction: any) => ({
              id: correction.id || `grammar-${Math.random()}`,
              type: 'grammar' as IssueType,
              original: correction.incorrect,
              suggestion: correction.suggestion,
              position: {
                start: correction.start,
                end: correction.end,
              },
              explanation: correction.explanation,
              category: correction.category,
            }));
            setIssues(newIssues);
          }
          break;
        case 'paraphraser':
          setParaphraseResult(entry.resultContent);
          break;
        case 'criteria-check':
          setCriteriaResult(entry.resultContent);
          break;
      }
    }
  };

  // Get the appropriate icon for the active tab
  const getTabIcon = (tab: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {
    switch (tab) {
      case 'ai-detector':
        return <FiFileText className="mr-1" />;
      case 'grammar-check':
        return <FiEdit3 className="mr-1" />;
      case 'paraphraser':
        return <FiRefreshCw className="mr-1" />;
      case 'criteria-check':
        return <FiSearch className="mr-1" />;
    }
  };

  // Helper function to get message based on tab type
  const getTabMessage = (tab: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser'): string => {
    switch (tab) {
      case 'paraphraser':
        return 'Click "Analyze Text" to paraphrase your content.';
      case 'criteria-check':
        return 'Click "Analyze Text" to check against scholarship criteria.';
      case 'grammar-check':
        return 'Click "Analyze Text" to check grammar.';
      default:
        return 'Click "Analyze Text" to check for issues.';
    }
  };

  // Format the result display based on active tab
  const renderResultContent = () => {
    switch (activeTab) {
      case 'ai-detector':
        return renderAIDetectionResult();
      case 'grammar-check':
        return renderGrammarResult();
      case 'paraphraser':
        return renderParaphraseResult();
      case 'criteria-check':
        return renderCriteriaResult();
      default:
        return <p>Select an analysis type and click "Analyze Text"</p>;
    }
  };

  // Render AI Detection result
  const renderAIDetectionResult = () => {
    if (!aiDetectionResult) return <p>No AI detection results yet. Click "Analyze Text" to check.</p>;

    const overallScore = aiDetectionResult.ai_score * 100;

    return (
      <div>
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-md font-medium">AI Content Detection</h3>
          <div className="text-lg font-bold">
            <span className={`${overallScore > 70 ? 'text-red-500' : overallScore > 30 ? 'text-yellow-500' : 'text-green-500'}`}>
              {overallScore.toFixed(1)}% AI
            </span>
          </div>
        </div>

        <div className="mt-2 p-3 bg-yellow-50 rounded-md">
          {aiDetectionResult.sentences.map((sentence, index) => {
            const isAI = sentence.prediction === 'ai-generated' && sentence.ai_score > 0.5;
            return (
              <span key={index} className={isAI ? 'bg-yellow-200 px-1 rounded' : ''}>
                {sentence.text}{' '}
              </span>
            );
          })}
        </div>

        <div className="mt-4">
          <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
            <div
              className={`h-full ${overallScore > 70 ? 'bg-red-500' : overallScore > 30 ? 'bg-yellow-500' : 'bg-green-500'}`}
              style={{ width: `${overallScore}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs mt-1">
            <span>Human Content</span>
            <span>AI Content</span>
          </div>
        </div>
      </div>
    );
  };

  // Render Grammar Check result
  const renderGrammarResult = () => {
    const effectiveText = getEffectiveInputText();
    return (
      <GrammarCheckComponent
        text={effectiveText}
        showSuggestions={showSuggestions}
        onShowSuggestions={(show) => setShowSuggestions(show)}
        onCorrectionsApplied={(_) => {
          // We're NOT updating the input text here
          // Clear the issues since the correction was applied
          setIssues([]);
        }}
        onSuggestionsFound={(corrections) => {
          // Map corrections to issues
          const newIssues = corrections.map((correction) => ({
            id: correction.id || `grammar-${Math.random()}`,
            type: 'grammar' as IssueType,
            original: correction.incorrect,
            suggestion: correction.suggestion,
            position: {
              start: correction.start,
              end: correction.end,
            },
            explanation: correction.explanation,
            category: correction.category,
          }));

          const result = {
            corrections: corrections,
            corrected_text: effectiveText, // Use the effective text that was actually processed
          };

          setIssues(newIssues);
          setGrammarResult(result);

          // Add to history
          addToHistory('grammar-check', result);
        }}
      />
    );
  };

  // Update the suggestion panel visibility based on active tab
  useEffect(() => {
    // Show suggestions for grammar check
    setShowSuggestions(activeTab === 'grammar-check');
  }, [activeTab]);

  // Store previous column widths when toggling suggestions
  const [prevColumnWidths, setPrevColumnWidths] = useState({
    input: 30,
    result: 40,
    suggestion: 30
  });

  // Ref to track if we're in the middle of a layout change to prevent infinite loops
  const isChangingLayout = useRef(false);

  // Adjust column widths when suggestion panel is shown/hidden or tab changes
  useEffect(() => {
    if (isChangingLayout.current) return;
    isChangingLayout.current = true;

    try {
      if (showSuggestions && activeTab === 'grammar-check') {
        // When suggestions are shown, restore from previous 3-column layout if available
        if (prevColumnWidths.input + prevColumnWidths.result + prevColumnWidths.suggestion === 100) {
          setInputColumnWidth(prevColumnWidths.input);
          setResultColumnWidth(prevColumnWidths.result);
          setSuggestionColumnWidth(prevColumnWidths.suggestion);
        } else {
          // Use default 30/40/30 split if previous values are invalid
          setInputColumnWidth(30);
          setResultColumnWidth(40);
          setSuggestionColumnWidth(30);
        }
      } else if ((activeTab === 'grammar-check' && !showSuggestions) ||
                 activeTab === 'ai-detector' ||
                 activeTab === 'paraphraser' ||
                 activeTab === 'criteria-check') {
        // When hiding suggestions or switching to other tabs, save the current 3-column layout if needed
        if (showSuggestions && activeTab === 'grammar-check') {
          setPrevColumnWidths({
            input: inputColumnWidth,
            result: resultColumnWidth,
            suggestion: suggestionColumnWidth
          });
        }

        // Reset to 40/60 split for 2-column layout
        setInputColumnWidth(40);
        setResultColumnWidth(60);
      }
    } finally {
      isChangingLayout.current = false;
    }
  }, [showSuggestions, activeTab]);

  // Render Paraphrase result
  const renderParaphraseResult = () => {
    const effectiveText = getEffectiveInputText();
    return (
      <Paraphraser
        text={effectiveText}
        onParaphrased={(paraphrasedText: string) => {
          const result = {
            paraphrased_text: paraphrasedText
          };

          if (paraphraseResult) {
            setParaphraseResult({
              ...paraphraseResult,
              paraphrased_text: paraphrasedText
            });
          } else {
            setParaphraseResult(result);
          }

          // Add to history
          addToHistory('paraphraser', result);
        }}
      />
    );
  };

  // Render Criteria Check result
  const renderCriteriaResult = () => {
    const effectiveText = getEffectiveInputText();
    return (
      <CriteriaCheckingComponent
        text={effectiveText}
        onAnalysisComplete={(result) => {
          setCriteriaResult(result);

          // Add to history
          addToHistory('criteria-check', result);
        }}
      />
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.5,
        type: "spring",
        stiffness: 100,
        damping: 15
      }}
      className={`h-[calc(100vh-64px)] bg-gray-50 flex flex-col overflow-hidden max-h-[calc(100vh-64px)] ${isResizing ? 'cursor-ew-resize' : ''}`}
    >
      {/* Main content area */}
      <main className="flex-1 flex overflow-hidden">
        {/* History Sidebar */}
        <HierarchicalHistorySidebar
          isVisible={showHistorySidebar}
          toggleSidebar={toggleHistorySidebar}
          groupedEntries={groupedHistoryEntries}
          activeInputId={activeInputId}
          activeFeatureId={activeFeatureId}
          onInputClick={handleInputClick}
          onFeatureClick={handleFeatureClick}
          onNewAnalysis={clearEditor}
        />
        {/* Input column */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className={`relative p-3 border-r border-gray-200 ${
            isResizing === 'input' ? 'bg-blue-50 transition-colors' : ''
          }`}
          style={{
            width: `${inputColumnWidth}%`
          }}
        >
          {/* Resize handle - show for all modes */}
          <div
            className="absolute right-0 top-0 bottom-0 w-1 cursor-ew-resize bg-gray-200 hover:bg-blue-500 hover:w-1.5 transition-all z-10"
            onMouseDown={startInputResize}
          ></div>
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-medium">Input</h2>
            {isResizing === 'input' && (
              <span className="text-xs text-gray-600 font-mono bg-blue-100 px-2 py-1 rounded">
                {Math.round(inputColumnWidth)}%
              </span>
            )}
          </div>
          <div className="bg-white rounded-md shadow-sm border border-gray-200 flex flex-col" style={{ height: "calc(100vh - 180px)" }}>
            <textarea
              className="flex-1 p-4 focus:outline-none resize-none overflow-auto"
              placeholder="I have went to the store yesterday and buyed some grocerys. Their was alot of people their."
              value={text}
              onChange={(e) => setText(e.target.value)}
            />
            <div className="border-t border-gray-200 p-2 flex justify-between items-center">
              <button
                onClick={clearEditor}
                className="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded"
              >
                Clear
              </button>
              <div className="text-sm text-gray-500">
                {wordCount} words
              </div>
            </div>
          </div>

          {/* Tool selection tabs */}
          <div className="mt-4 flex space-x-2">
            <button
              onClick={() => handleTabChange('ai-detector')}
              className={`px-3 py-1 text-sm rounded flex items-center ${activeTab === 'ai-detector' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}
            >
              {getTabIcon('ai-detector')} AI detector
            </button>
            <button
              onClick={() => handleTabChange('grammar-check')}
              className={`px-3 py-1 text-sm rounded flex items-center ${activeTab === 'grammar-check' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}
            >
              {getTabIcon('grammar-check')} Grammar Check
            </button>
            <button
              onClick={() => handleTabChange('paraphraser')}
              className={`px-3 py-1 text-sm rounded flex items-center ${activeTab === 'paraphraser' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}
            >
              {getTabIcon('paraphraser')} Paraphraser
            </button>
            <button
              onClick={() => handleTabChange('criteria-check')}
              className={`px-3 py-1 text-sm rounded flex items-center ${activeTab === 'criteria-check' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}
            >
              {getTabIcon('criteria-check')} Criteria check
            </button>
          </div>
        </motion.div>

        {/* Result column */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className={`relative p-3 ${
            activeTab === 'ai-detector' || activeTab === 'paraphraser' ? '' : 'border-r border-gray-200'
          } ${
            isResizing === 'result' ? 'bg-blue-50 transition-colors' : ''
          }`}
          style={{
            width: `${resultColumnWidth}%`
          }}
        >
          {/* Resize handle - only shown when suggestions are visible */}
          {showSuggestions && activeTab === 'grammar-check' && (
            <div
              className="absolute right-0 top-0 bottom-0 w-1 cursor-ew-resize bg-gray-200 hover:bg-blue-500 hover:w-1.5 transition-all z-10"
              onMouseDown={startResultResize}
            ></div>
          )}
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-medium">Result</h2>
            <div className="flex items-center space-x-2">
              {isResizing === 'result' && (
                <span className="text-xs text-blue-600 font-mono bg-blue-100 px-2 py-1 rounded">
                  {Math.round(resultColumnWidth)}%
                </span>
              )}
              {activeTab === 'grammar-check' && !showSuggestions && (
                <button
                  onClick={() => setShowSuggestions(true)}
                  className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                >
                  <span className="mr-1">Show suggestions</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </button>
              )}
            </div>
          </div>
          <div className="bg-white rounded-md shadow-sm border border-gray-200 flex flex-col" style={{ height: "calc(100vh - 180px)" }}>
            {/* Show cards panel when viewing history AND not currently analyzing, otherwise show normal result content */}
            {selectedEntries.length > 0 && !isAnalyzing && !hasCurrentResults() ? (
              <ResultCardsPanel
                entries={selectedEntries}
                isLoading={isAnalyzing}
                error={error}
              />
            ) : activeTab === 'ai-detector' ? (
              <AiDetectorResultPanel
                activeTab={activeTab}
                isAnalyzing={isAnalyzing}
                text={text}
                onAnalyze={analyzeText}
                aiDetectionResult={aiDetectionResult}
                error={error}
              />
            ) : (
              <div className="p-4 flex-1 flex flex-col overflow-hidden">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="h-full flex flex-col overflow-hidden"
                >
                  {error ? (
                    <div className="p-4 bg-red-50 text-red-700 rounded-md">
                      <h3 className="font-medium mb-2">Error</h3>
                      <p>{error}</p>
                    </div>
                  ) : (
                    <div className="overflow-y-auto flex-1">
                      {renderResultContent()}
                    </div>
                  )}
                </motion.div>
              </div>
            )}
          </div>


        </motion.div>

        {/* Suggestion column with fixed width - only shown for grammar check */}
        <AnimatePresence>
          {showSuggestions && activeTab === 'grammar-check' && (
            <motion.div
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: `${suggestionColumnWidth}%` }}
              exit={{ opacity: 0, width: 0 }}
              transition={{ duration: 0.4, type: "spring", stiffness: 100 }}
              className={`relative ${isResizing === 'result' ? 'bg-blue-50 transition-colors' : ''}`}
            >
            {/* No resize handle needed here - the result column already has one */}

            <div className="h-full p-3 border-r border-gray-200">
              <div className="flex justify-between items-center mb-3">
                <h2 className="text-lg font-medium">Suggestion</h2>
                <div className="flex items-center space-x-2">
                  {isResizing === 'result' && (
                    <span className="text-xs text-blue-600 font-mono bg-blue-100 px-2 py-1 rounded">
                      {Math.round(suggestionColumnWidth)}%
                    </span>
                  )}
                  <button
                    onClick={() => setShowSuggestions(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    Hide
                  </button>
                </div>
              </div>

              <AnimatePresence>
                {issues.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="flex items-center mb-4 bg-yellow-50 p-3 rounded-md"
                  >
                    <div className="flex-shrink-0 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-white mr-3">
                      <FiAlertTriangle />
                    </div>
                    <div>
                      <span className="font-medium">Found {issues.length} {issues.length === 1 ? 'issue' : 'issues'}</span>
                    </div>
                    <button
                      onClick={acceptAllSuggestions}
                      className="ml-auto bg-blue-600 text-white px-3 py-1 rounded text-sm"
                    >
                      Accept all
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Fixed height scrollable container for suggestions */}
              <div className="overflow-y-auto border border-gray-200 rounded-lg" style={{ height: "calc(100vh - 180px)" }}>
                <AnimatePresence>
                  {issues.map((issue) => (
                    <motion.div
                      key={issue.id}
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                      className="mb-4 bg-white p-3 rounded-md border border-gray-200"
                    >
                      {issue.category ? (
                        <>
                          <div className="flex items-center mb-2">
                            <span className={`w-3 h-3 rounded-full mr-2 ${
                              issue.category === 'misspelling' || issue.category === 'correctness'
                                ? 'bg-red-500'
                                : issue.category === 'clarity'
                                ? 'bg-blue-500'
                                : issue.category === 'engagement'
                                ? 'bg-purple-500'
                                : 'bg-green-500'
                            }`}></span>
                            <span className="text-sm font-medium text-gray-700">
                              {issue.category === 'misspelling'
                                ? 'Misspelling'
                                : issue.category === 'correctness'
                                ? 'Correctness'
                                : issue.category === 'clarity'
                                ? 'Clarity'
                                : issue.category === 'engagement'
                                ? 'Engagement'
                                : 'Delivery'}
                            </span>
                          </div>
                          <div className="mb-2">
                            <p className="text-sm text-gray-600 mb-1">Original:</p>
                            <p className="px-2 py-1 bg-white rounded border border-gray-200 line-through">
                              {issue.original}
                            </p>
                          </div>
                          <div className="mb-2">
                            <p className="text-sm text-gray-600 mb-1">Suggestion:</p>
                            <p className="px-2 py-1 bg-white rounded border border-gray-200 font-medium text-green-600">
                              {issue.suggestion}
                            </p>
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="mb-2">
                            <span className="text-sm font-medium text-gray-700">
                              {issue.original}
                            </span>
                          </div>
                          <div className="flex items-center mb-2">
                            <span className="text-green-600 font-medium">{issue.suggestion}</span>
                          </div>
                        </>
                      )}
                      {issue.explanation && issue.type !== 'ai-content' && (
                        <div className="mb-2">
                          <p className="text-sm text-gray-600 mb-1">Explanation:</p>
                          <p className="px-2 py-1 bg-white rounded border border-gray-200 text-sm">
                            {issue.explanation}
                          </p>
                        </div>
                      )}
                      <div className="flex space-x-2 mt-3">
                        {issue.type !== 'ai-content' && (
                          <button
                            onClick={() => applySuggestion(issue)}
                            className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm flex items-center justify-center"
                          >
                            <FiCheck className="mr-1" /> Accept
                          </button>
                        )}
                        <button
                          onClick={() => setIssues(issues.filter(i => i.id !== issue.id))}
                          className="flex-1 bg-white border border-gray-300 text-gray-700 px-3 py-2 rounded-md text-sm flex items-center justify-center"
                        >
                          <FiX className="mr-1" /> {issue.type === 'ai-content' ? 'Dismiss' : 'Ignore'}
                        </button>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>

                {issues.length === 0 && !isAnalyzing && (
                  <div className="text-center text-gray-500 mt-8">
                    {activeTab === 'grammar-check' && grammarResult ? (
                      <div className="p-4 bg-green-50 text-green-700 rounded-md">
                        <div className="flex items-center justify-center">
                          <FiCheck className="mr-2" />
                          <span className="font-medium">No grammar issues found. Your text looks good!</span>
                        </div>
                      </div>
                    ) : (
                      <>
                        <p>No issues found or all issues resolved.</p>
                        <p className="text-sm mt-2">
                          {getTabMessage(activeTab)}
                        </p>
                      </>
                    )}
                  </div>
                )}

                {isAnalyzing && (
                  <div className="flex flex-col items-center justify-center h-40">
                    <svg className="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p className="mt-3 text-gray-600">Analyzing your text...</p>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
        </AnimatePresence>
      </main>
    </motion.div>
  );
};

export default TextAnalyzer;

