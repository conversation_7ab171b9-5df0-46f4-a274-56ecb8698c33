{"name": "chettra", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"axios": "^1.9.0", "date-fns": "^4.1.0", "diff": "^7.0.0", "fi": "^1.0.16", "framer-motion": "^12.12.1", "lucide-react": "^0.479.0", "next": "15.2.2", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "slate": "^0.112.0", "slate-history": "^0.110.3", "slate-react": "^0.112.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.0.17", "@types/diff": "^7.0.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.14", "typescript": "^5"}}