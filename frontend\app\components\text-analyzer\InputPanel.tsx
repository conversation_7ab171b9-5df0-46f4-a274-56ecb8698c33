'use client';

import React, { JSX } from 'react';
import { Slate, Editable } from 'slate-react';
import { Descendant } from 'slate';

interface InputPanelProps {
  editor: any;
  value: Descendant[];
  onChange: (value: Descendant[]) => void;
  renderLeaf: (props: any) => JSX.Element;
  wordCount: number;
  onClear: () => void;
  activeTab: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser';
  setActiveTab: (tab: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => void;
}

const InputPanel: React.FC<InputPanelProps> = ({
  editor,
  value,
  onChange,
  renderLeaf,
  wordCount,
  onClear,
  activeTab,
  setActiveTab,
}) => {
  return (
    <div className="w-1/3 p-4 border-r border-gray-200">
      <h2 className="text-lg font-medium mb-3">Input</h2>
      <div className="bg-white rounded-md shadow-sm border border-gray-200 min-h-[500px] flex flex-col">
        <Slate
          editor={editor}
          initialValue={value}
          onChange={onChange}
        >
          <Editable
            className="flex-1 p-4 focus:outline-none"
            placeholder="I have went to the store yesterday and buyed some grocerys. Their was alot of people their."
            renderLeaf={renderLeaf}
          />
        </Slate>
        <div className="border-t border-gray-200 p-2 flex justify-between items-center">
          <button 
            onClick={onClear}
            className="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded"
          >
            Clear
          </button>
          <div className="text-sm text-gray-500">
            {wordCount} words
          </div>
        </div>
      </div>
      
      {/* Tool selection tabs */}
      <div className="mt-4 flex space-x-2">
        <button 
          onClick={() => setActiveTab('criteria-check')}
          className={`px-3 py-1 text-sm rounded ${activeTab === 'criteria-check' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}
        >
          Criteria check
        </button>
        <button 
          onClick={() => setActiveTab('ai-detector')}
          className={`px-3 py-1 text-sm rounded ${activeTab === 'ai-detector' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}
        >
          AI detector
        </button>
        <button 
          onClick={() => setActiveTab('grammar-check')}
          className={`px-3 py-1 text-sm rounded ${activeTab === 'grammar-check' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}
        >
          Grammar Check
        </button>
        <button 
          onClick={() => setActiveTab('paraphraser')}
          className={`px-3 py-1 text-sm rounded ${activeTab === 'paraphraser' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}
        >
          Paraphraser
        </button>
      </div>
    </div>
  );
};

export default InputPanel;
