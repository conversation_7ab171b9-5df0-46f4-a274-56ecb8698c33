'use client';

import React from 'react';
import { useChat } from '../../context/ChatContext';
import { FiPlus } from 'react-icons/fi';

export default function ChatHeader() {
  const { clearMessages } = useChat();
  
  const handleNewChat = () => {
    clearMessages();
  };

  return (
    <div className="border-b border-gray-200 p-4 flex justify-between items-center bg-white">
      <h1 className="text-xl font-semibold text-gray-800">Scholarar Assistant</h1>
      <button 
        onClick={handleNewChat}
        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        <FiPlus /> New Chat
      </button>
    </div>
  );
}