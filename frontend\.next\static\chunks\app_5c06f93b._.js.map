{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Scholarar/V2_mentor/frontend/app/components/text-analyzer/GrammarCheckComponent.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef, JSX } from 'react';\nimport axios from 'axios';\nimport { <PERSON><PERSON><PERSON>ck, FiMaximize2, FiMinimize2, FiRefreshCw } from 'react-icons/fi';\n\ninterface GrammarCheckComponentProps {\n  text: string;\n  onCorrectionsApplied?: (correctedText: string) => void;\n  onSuggestionsFound?: (corrections: Correction[]) => void;\n  onShowSuggestions?: (show: boolean) => void;\n  showSuggestions?: boolean;\n}\n\ninterface Correction {\n  incorrect: string;\n  suggestion: string;\n  start: number;\n  end: number;\n  type: string;\n  explanation: string;\n  category?: 'misspelling' | 'correctness' | 'clarity' | 'engagement' | 'delivery';\n  priority?: 'high' | 'medium' | 'low';\n  id?: string;\n}\n\ninterface GrammarCheckResult {\n  corrections: Correction[];\n  corrected_text?: string;\n}\n\n// Extend Window interface to include our custom functions\ndeclare global {\n  interface Window {\n    applyGrammarCorrection?: (id: string) => void;\n    applyAllGrammarCorrections?: () => void;\n    dismissGrammarCorrection?: (id: string) => void;\n  }\n}\n\n// Helper function to escape regex special characters (used in cleanupCorrections)\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\n// Load and save handled corrections from/to localStorage\nconst loadHandledCorrections = (): Set<string> => {\n  if (typeof window !== 'undefined' && window.localStorage) {\n    const stored = localStorage.getItem('handledCorrections');\n    return stored ? new Set(JSON.parse(stored)) : new Set();\n  }\n  return new Set();\n};\n\nconst saveHandledCorrections = (handledCorrections: Set<string>) => {\n  if (typeof window !== 'undefined' && window.localStorage) {\n    localStorage.setItem('handledCorrections', JSON.stringify([...handledCorrections]));\n  }\n};\n\nconst GrammarCheckComponent: React.FC<GrammarCheckComponentProps> = ({\n  text,\n  onCorrectionsApplied,\n  onSuggestionsFound,\n  onShowSuggestions,\n  showSuggestions = false\n}) => {\n  const [isLoading, setIsLoading] = useState(false);\n  const [loadingProgress, setLoadingProgress] = useState(0);\n  const [error, setError] = useState<string | null>(null);\n  const [result, setResult] = useState<GrammarCheckResult | null>(null);\n  const [correctedText, setCorrectedText] = useState<string>(text);\n  const [selectedCorrection, setSelectedCorrection] = useState<Correction | null>(null);\n  const [handledCorrections, setHandledCorrections] = useState<Set<string>>(new Set());\n  const [hasOverflow, setHasOverflow] = useState(false);\n  const [isFullScreen, setIsFullScreen] = useState(false);\n  const textContainerRef = useRef<HTMLDivElement>(null);\n  const [originalText, setOriginalText] = useState<string>(text);\n  const [correctionMap, setCorrectionMap] = useState<Map<string, Correction>>(new Map());\n\n  // Load handledCorrections from localStorage on mount\n  useEffect(() => {\n    setHandledCorrections(loadHandledCorrections());\n  }, []);\n\n  // Update correctedText when text prop changes\n  useEffect(() => {\n    setCorrectedText(text);\n  }, [text]);\n\n  // Check for overflow when text changes\n  useEffect(() => {\n    const checkForOverflow = () => {\n      if (textContainerRef.current) {\n        const hasVerticalOverflow = textContainerRef.current.scrollHeight > textContainerRef.current.clientHeight;\n        setHasOverflow(hasVerticalOverflow);\n      }\n    };\n\n    // Use a small delay to ensure the DOM has updated\n    const timer = setTimeout(() => {\n      checkForOverflow();\n    }, 100);\n\n    // Add resize listener to recheck on window resize\n    window.addEventListener('resize', checkForOverflow);\n\n    return () => {\n      clearTimeout(timer);\n      window.removeEventListener('resize', checkForOverflow);\n    };\n  }, [correctedText, result]);\n\n  // Expose correction functions to window object for external access\n  useEffect(() => {\n    // Add the correction functions to the window object\n    window.applyGrammarCorrection = (id: string) => {\n      const correction = result?.corrections.find(c => c.id === id);\n      if (correction) {\n        applyCorrection(correction);\n      }\n    };\n\n    window.applyAllGrammarCorrections = () => {\n      if (result) {\n        applyAllCorrections();\n      }\n    };\n\n    window.dismissGrammarCorrection = (id: string) => {\n      const correction = result?.corrections.find(c => c.id === id);\n      if (correction) {\n        dismissCorrection(correction);\n      }\n    };\n\n    // Clean up when component unmounts\n    return () => {\n      window.applyGrammarCorrection = undefined;\n      window.applyAllGrammarCorrections = undefined;\n      window.dismissGrammarCorrection = undefined;\n    };\n  }, [result]);\n\n  // Simulate loading progress\n  useEffect(() => {\n    let interval: NodeJS.Timeout;\n    if (isLoading) {\n      setLoadingProgress(0);\n      interval = setInterval(() => {\n        setLoadingProgress((prev) => {\n          if (prev >= 90) return prev;\n          return prev + Math.random() * 10;\n        });\n      }, 200);\n    }\n    return () => clearInterval(interval);\n  }, [isLoading]);\n\n  // Function to check grammar\n  const checkGrammar = async () => {\n    if (!text.trim()) {\n      setError('Please enter some text to check');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    // Store the original text\n    setOriginalText(text);\n\n    // Reset corrected text to original text\n    setCorrectedText(text);\n\n    try {\n      // Check cache first\n      if (typeof window !== 'undefined' && window.localStorage) {\n        const cacheKey = `grammarCache_${text}`;\n        const cachedResult = localStorage.getItem(cacheKey);\n        if (cachedResult) {\n          const data: GrammarCheckResult = JSON.parse(cachedResult);\n          processGrammarResults(data);\n          setIsLoading(false);\n          setLoadingProgress(100);\n          return;\n        }\n      }\n\n      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\n      const response = await axios.post(`${API_URL}/grammar-check`, { text });\n\n      if (response.data) {\n        processGrammarResults(response.data);\n\n        // Cache the result\n        if (typeof window !== 'undefined' && window.localStorage) {\n          localStorage.setItem(`grammarCache_${text}`, JSON.stringify(response.data));\n        }\n      }\n    } catch (err: any) {\n      console.error('Grammar check error:', err);\n      setError(err.message || 'An error occurred during grammar checking');\n    } finally {\n      setIsLoading(false);\n      setLoadingProgress(100);\n    }\n  };\n\n  // Process grammar check results\n  const processGrammarResults = (data: GrammarCheckResult) => {\n    // First, ensure we have valid corrections\n    if (!data.corrections || !Array.isArray(data.corrections)) {\n      console.error('Invalid corrections data:', data);\n      setError('Invalid grammar check results received');\n      return;\n    }\n\n    // Clean up the corrections to ensure they don't overlap\n    const cleanedCorrections = cleanupCorrections(data.corrections);\n\n    // Create a map of corrections for easier access\n    const newCorrectionMap = new Map<string, Correction>();\n\n    // Assign categories and priorities to corrections\n    const enhancedCorrections = cleanedCorrections.map(correction => {\n      // Generate a unique ID for each correction\n      const id = `${correction.incorrect}-${correction.start}-${correction.end}`;\n\n      // Assign a category based on the type\n      let category: 'misspelling' | 'correctness' | 'clarity' | 'engagement' | 'delivery' = 'correctness';\n      if (correction.type === 'spelling') {\n        category = 'misspelling';\n      } else if (correction.type === 'grammar') {\n        category = 'correctness';\n      } else if (correction.type === 'style') {\n        category = 'clarity';\n      } else if (correction.type === 'punctuation') {\n        category = 'delivery';\n      }\n\n      // Assign a priority\n      let priority: 'high' | 'medium' | 'low' = 'medium';\n      if (category === 'misspelling' || category === 'correctness') {\n        priority = 'high';\n      } else if (category === 'clarity') {\n        priority = 'medium';\n      } else {\n        priority = 'low';\n      }\n\n      // Ensure suggestion is clean\n      const suggestion = correction.suggestion?.trim() || correction.incorrect;\n\n      const enhancedCorrection = {\n        ...correction,\n        suggestion,\n        category,\n        priority,\n        id\n      };\n\n      // Add to map\n      newCorrectionMap.set(id, enhancedCorrection);\n\n      return enhancedCorrection;\n    });\n\n    // Set the correction map\n    setCorrectionMap(newCorrectionMap);\n\n    // Filter out handled corrections\n    const filteredCorrections = enhancedCorrections.filter(\n      correction => !handledCorrections.has(correction.id!)\n    );\n\n    // Sort by position\n    const sortedCorrections = [...filteredCorrections].sort((a, b) => a.start - b.start);\n\n    // Set the result\n    setResult({\n      ...data,\n      corrections: sortedCorrections\n    });\n\n    // Notify parent component about the suggestions\n    if (onSuggestionsFound && sortedCorrections.length > 0) {\n      onSuggestionsFound(sortedCorrections);\n    }\n  };\n\n  // Clean up corrections to ensure they don't overlap\n  const cleanupCorrections = (corrections: Correction[]): Correction[] => {\n    if (!corrections.length) return [];\n\n    // Sort by position\n    const sorted = [...corrections].sort((a, b) => a.start - b.start);\n    const result: Correction[] = [];\n\n    // Track the last end position\n    let lastEnd = -1;\n\n    for (const correction of sorted) {\n      // Skip invalid corrections\n      if (correction.start >= correction.end ||\n          correction.start < 0 ||\n          correction.end > text.length ||\n          !correction.incorrect ||\n          correction.incorrect.trim() === '') {\n        continue;\n      }\n\n      // Skip overlapping corrections\n      if (correction.start < lastEnd) {\n        continue;\n      }\n\n      // Verify the incorrect text matches the actual text at that position\n      const actualText = text.substring(correction.start, correction.end);\n      if (actualText !== correction.incorrect) {\n        // Try to find the correct position using regex to handle special characters\n        const escapedIncorrect = escapeRegExp(correction.incorrect);\n        const regex = new RegExp(escapedIncorrect, 'g');\n        const match = regex.exec(text);\n\n        if (match) {\n          // Update the position\n          correction.start = match.index;\n          correction.end = match.index + correction.incorrect.length;\n        } else {\n          // Try a simple indexOf as fallback\n          const index = text.indexOf(correction.incorrect);\n          if (index >= 0) {\n            // Update the position\n            correction.start = index;\n            correction.end = index + correction.incorrect.length;\n          } else {\n            // Skip this correction if we can't find it\n            continue;\n          }\n        }\n      }\n\n      // Add to result and update lastEnd\n      result.push(correction);\n      lastEnd = correction.end;\n    }\n\n    return result;\n  };\n\n  // Function to adjust correction positions after a correction is applied\n  const adjustCorrectionPositions = (\n    corrections: Correction[],\n    start: number,\n    end: number,\n    newLength: number\n  ): Correction[] => {\n    const lengthDiff = newLength - (end - start);\n\n    return corrections.map(correction => {\n      // If the correction is after the changed text, adjust its position\n      if (correction.start >= end) {\n        return {\n          ...correction,\n          start: correction.start + lengthDiff,\n          end: correction.end + lengthDiff\n        };\n      }\n      // If the correction overlaps with the changed text, skip it\n      // This shouldn't happen with our cleanup logic, but just in case\n      if (correction.start < end && correction.end > start) {\n        return correction;\n      }\n      // Otherwise, leave it unchanged\n      return correction;\n    });\n  };\n\n  // Apply a single correction\n  const applyCorrection = (correction: Correction) => {\n    // Create a new value with the suggestion applied\n    const before = correctedText.substring(0, correction.start);\n    const after = correctedText.substring(correction.end);\n    const newText = before + correction.suggestion + after;\n\n    // Set the new output text\n    setCorrectedText(newText);\n\n    // Add to handled corrections\n    if (correction.id) {\n      setHandledCorrections(prev => {\n        const newSet = new Set(prev);\n        newSet.add(correction.id!);\n        saveHandledCorrections(newSet);\n        return newSet;\n      });\n    }\n\n    // Update the result by removing the applied correction\n    if (result) {\n      // First remove the applied correction\n      const updatedCorrections = result.corrections.filter(\n        c => c.id !== correction.id\n      );\n\n      // Calculate the length difference\n      const lengthDiff = correction.suggestion.length - (correction.end - correction.start);\n\n      // Adjust positions for corrections that come after the applied one\n      const adjustedCorrections = updatedCorrections.map(c => {\n        if (c.start > correction.end) {\n          return {\n            ...c,\n            start: c.start + lengthDiff,\n            end: c.end + lengthDiff\n          };\n        }\n        return c;\n      });\n\n      setResult({\n        ...result,\n        corrections: adjustedCorrections\n      });\n\n      // Notify parent component about the updated suggestions\n      if (onSuggestionsFound) {\n        onSuggestionsFound(adjustedCorrections);\n      }\n    }\n\n    // Clear selected correction\n    setSelectedCorrection(null);\n\n    // We're NOT calling onCorrectionsApplied here to keep input text unchanged\n    // But we need to update the UI to show the corrected text\n    // This is handled by the correctedText state\n  };\n\n  // Dismiss a correction without applying it - used by the suggestion panel\n  const dismissCorrection = (correction: Correction) => {\n    // Add to handled corrections\n    if (correction.id) {\n      setHandledCorrections(prev => {\n        const newSet = new Set(prev);\n        newSet.add(correction.id!);\n        saveHandledCorrections(newSet);\n        return newSet;\n      });\n    }\n\n    // Update the result by removing the dismissed correction\n    if (result) {\n      const updatedCorrections = result.corrections.filter(\n        c => c.id !== correction.id\n      );\n\n      setResult({\n        ...result,\n        corrections: updatedCorrections\n      });\n\n      // Notify parent component about the updated suggestions\n      if (onSuggestionsFound) {\n        onSuggestionsFound(updatedCorrections);\n      }\n    }\n\n    // Clear selected correction\n    setSelectedCorrection(null);\n  };\n\n  // Apply all corrections at once\n  const applyAllCorrections = () => {\n    if (!result) return;\n\n    // Sort corrections by position in reverse order to avoid position shifts\n    const sortedCorrections = [...result.corrections].sort((a, b) => b.start - a.start);\n\n    // Start with the current corrected text\n    let newText = correctedText;\n\n    // Apply all corrections\n    for (const correction of sortedCorrections) {\n      // Verify the position is valid\n      if (correction.start < 0 || correction.end > newText.length || correction.start >= correction.end) {\n        continue;\n      }\n\n      const before = newText.substring(0, correction.start);\n      const after = newText.substring(correction.end);\n      newText = before + correction.suggestion + after;\n\n      // Add to handled corrections\n      if (correction.id) {\n        setHandledCorrections(prev => {\n          const newSet = new Set(prev);\n          newSet.add(correction.id!);\n          saveHandledCorrections(newSet);\n          return newSet;\n        });\n      }\n    }\n\n    // Set the new output text\n    setCorrectedText(newText);\n\n    // Clear all corrections\n    setResult({\n      ...result,\n      corrections: []\n    });\n\n    // Clear selected correction\n    setSelectedCorrection(null);\n\n    // Notify parent component that all suggestions have been applied\n    if (onSuggestionsFound) {\n      onSuggestionsFound([]);\n    }\n\n    // We're NOT calling onCorrectionsApplied here to keep input text unchanged\n  };\n\n  // Reset handled corrections\n  const resetHandledCorrections = () => {\n    setHandledCorrections(new Set());\n    if (typeof window !== 'undefined' && window.localStorage) {\n      localStorage.removeItem('handledCorrections');\n    }\n    // Re-check grammar to show all corrections again\n    checkGrammar();\n  };\n\n  // Render the text with highlighted corrections\n  const renderHighlightedText = () => {\n    if (!result || result.corrections.length === 0) {\n      return <p className=\"whitespace-pre-line\">{correctedText}</p>;\n    }\n\n    // Create segments of text with highlighted corrections\n    const segments: JSX.Element[] = [];\n    let lastIndex = 0;\n\n    // Make a copy and ensure they're sorted by position\n    const sortedCorrections = [...result.corrections].sort((a, b) => a.start - b.start);\n\n    // If the text is very long, limit the number of corrections to improve performance\n    const maxCorrections = 100;\n    const hasTooManyCorrections = sortedCorrections.length > maxCorrections;\n\n    if (hasTooManyCorrections) {\n      console.warn(`Text has ${sortedCorrections.length} corrections, limiting to ${maxCorrections} for performance.`);\n    }\n\n    // Use a limited set of corrections if there are too many\n    const displayCorrections = hasTooManyCorrections\n      ? sortedCorrections.slice(0, maxCorrections)\n      : sortedCorrections;\n\n    displayCorrections.forEach((correction, index) => {\n      // Add text before the correction\n      if (correction.start > lastIndex) {\n        segments.push(\n          <span key={`text-${index}`}>\n            {correctedText.substring(lastIndex, correction.start)}\n          </span>\n        );\n      }\n\n      // Determine the style based on the category\n      let underlineStyle = 'border-b-2 border-red-500';\n      if (correction.category === 'misspelling' || correction.category === 'correctness') {\n        underlineStyle = 'border-b-2 border-red-500';\n      } else if (correction.category === 'clarity') {\n        underlineStyle = 'border-b-2 border-blue-500';\n      } else if (correction.category === 'engagement') {\n        underlineStyle = 'border-b-2 border-purple-500';\n      } else if (correction.category === 'delivery') {\n        underlineStyle = 'border-b-2 border-green-500';\n      }\n\n      // Add the highlighted correction\n      segments.push(\n        <span\n          key={`correction-${index}`}\n          className={`${underlineStyle} cursor-pointer`}\n          title={`Suggestion: ${correction.suggestion}`}\n          onClick={() => {\n            setSelectedCorrection(correction);\n            // Notify parent component about the selected correction\n            if (onSuggestionsFound) {\n              onSuggestionsFound([...result.corrections]);\n            }\n          }}\n        >\n          {correction.incorrect}\n        </span>\n      );\n\n      lastIndex = correction.end;\n    });\n\n    // Add any remaining text\n    if (lastIndex < correctedText.length) {\n      segments.push(\n        <span key=\"text-end\">\n          {correctedText.substring(lastIndex)}\n        </span>\n      );\n    }\n\n    return <div className=\"leading-relaxed whitespace-pre-line\">{segments}</div>;\n  };\n\n  return (\n    <div className=\"w-full h-full flex flex-col\">\n      {/* Grammar check results */}\n      <div className=\"flex-1 flex flex-col\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <div className=\"flex items-center\">\n            <h3 className=\"text-lg font-medium text-blue-700\">Grammar Check</h3>\n            {result && result.corrections.length > 0 && (\n              <div className=\"ml-3 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium\">\n                {result.corrections.length} {result.corrections.length === 1 ? 'issue' : 'issues'}\n              </div>\n            )}\n            {!showSuggestions && result && result.corrections.length > 0 && (\n              <button\n                onClick={() => onShowSuggestions && onShowSuggestions(true)}\n                className=\"ml-3 text-blue-600 hover:text-blue-800 text-sm flex items-center\"\n              >\n                <span className=\"mr-1\">Show suggestions</span>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <polyline points=\"9 18 15 12 9 6\"></polyline>\n                </svg>\n              </button>\n            )}\n          </div>\n\n          <div className=\"flex space-x-2\">\n            {result && result.corrections.length > 0 && (\n              <button\n                onClick={resetHandledCorrections}\n                className=\"text-gray-600 hover:text-gray-800 text-sm\"\n              >\n                Reset ignored\n              </button>\n            )}\n            <button\n              onClick={checkGrammar}\n              disabled={isLoading || !text.trim()}\n              className={`px-4 py-1.5 rounded-md ${\n                isLoading || !text.trim()\n                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                  : 'bg-blue-600 text-white hover:bg-blue-700 transition-colors'\n              }`}\n            >\n              {isLoading ? \"Checking...\" : \"Check Grammar\"}\n            </button>\n\n            <button\n              onClick={() => setIsFullScreen(!isFullScreen)}\n              className=\"ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded\"\n              title={isFullScreen ? \"Exit full screen\" : \"Full screen\"}\n            >\n              {isFullScreen ? <FiMinimize2 size={16} /> : <FiMaximize2 size={16} />}\n            </button>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"p-3 bg-red-50 text-red-700 rounded-md mb-4\">\n            {error}\n          </div>\n        )}\n\n        {/* Category legend */}\n        {result && result.corrections.length > 0 && (\n          <div className=\"flex flex-wrap gap-3 mb-3\">\n            <div className=\"flex items-center\">\n              <span className=\"w-3 h-3 rounded-full bg-red-500 mr-1\"></span>\n              <span className=\"text-xs text-gray-600\">Correctness</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"w-3 h-3 rounded-full bg-blue-500 mr-1\"></span>\n              <span className=\"text-xs text-gray-600\">Clarity</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"w-3 h-3 rounded-full bg-purple-500 mr-1\"></span>\n              <span className=\"text-xs text-gray-600\">Engagement</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"w-3 h-3 rounded-full bg-green-500 mr-1\"></span>\n              <span className=\"text-xs text-gray-600\">Delivery</span>\n            </div>\n          </div>\n        )}\n\n        {/* Text with highlighted corrections */}\n        <div className={`${isFullScreen ? 'fixed inset-0 z-50 bg-white p-6' : 'flex-1 w-full'} font-sans flex flex-col overflow-hidden`}>\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4 relative flex-1 overflow-hidden\">\n            {hasOverflow && (\n              <div className=\"absolute top-2 right-2 z-10 text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded-md flex items-center\">\n                <span className=\"mr-1\">Scroll for more</span>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <path d=\"M12 5v14M5 12l7 7 7-7\"/>\n                </svg>\n              </div>\n            )}\n            <div\n              ref={textContainerRef}\n              className=\"text-gray-900 text-base overflow-auto h-full max-h-[calc(100vh-370px)] min-h-auto relative\"\n              style={{ height: 'calc(100vh - 180px)' }}\n            >\n              {hasOverflow && (\n                <div className=\"absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none\" />\n              )}\n\n              {/* Warning for very long text */}\n              {(() => {\n                // Check if text is very long (more than 5000 characters)\n                if (correctedText.length > 5000) {\n                  return (\n                    <div className=\"absolute top-0 left-0 right-0 bg-yellow-50 text-yellow-800 text-xs p-2 border-b border-yellow-200\">\n                      This text is very long ({correctedText.length} characters). Scroll to see all content.\n                    </div>\n                  );\n                }\n                return null;\n              })()}\n\n              {/* Warning for too many corrections */}\n              {(() => {\n                if (result && result.corrections.length > 100) {\n                  return (\n                    <div className=\"absolute top-0 left-0 right-0 bg-yellow-50 text-yellow-800 text-xs p-2 border-b border-yellow-200 mt-8\">\n                      Found {result.corrections.length} issues. Only showing the first 100 for performance.\n                    </div>\n                  );\n                }\n                return null;\n              })()}\n\n              <div className={`${correctedText.length > 5000 || (result && result.corrections.length > 100) ? 'pt-16' : ''}`}>\n                {isLoading ? (\n                  <div className=\"flex flex-col justify-center items-center h-40\">\n                    <div className=\"w-full max-w-md h-2 bg-gray-200 rounded-full overflow-hidden mb-3\">\n                      <div\n                        className=\"h-full bg-blue-600 transition-all duration-300\"\n                        style={{ width: `${loadingProgress}%` }}\n                      ></div>\n                    </div>\n                    <span className=\"text-gray-600\">Checking grammar... {Math.round(loadingProgress)}%</span>\n                  </div>\n                ) : (\n                  <div className=\"h-full overflow-auto\">\n                    {renderHighlightedText()}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n\n        {/* We've removed the selected correction details from here as they'll be shown in the suggestion panel */}\n\n        {/* Word/sentence count footer */}\n        <div className=\"mt-2 flex justify-between items-center text-sm text-gray-500 pt-2\">\n          <div className=\"flex items-center space-x-3\">\n            <span>\n              {correctedText.split(/\\s+/).filter(Boolean).length} Words\n            </span>\n            <span>\n              {correctedText.length} Characters\n            </span>\n            <span>\n              {correctedText.split(/[.!?]+/).filter(Boolean).length} Sentences\n            </span>\n            {result && result.corrections.length > 0 && (\n              <span className=\"text-yellow-600 font-medium\">\n                {result.corrections.length} Issues\n              </span>\n            )}\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <button className=\"p-1 hover:bg-gray-100 rounded\" onClick={() => checkGrammar()}>\n              <FiRefreshCw size={16} />\n            </button>\n          </div>\n        </div>\n\n        {/* Legend */}\n        {result && result.corrections.length > 0 && (\n          <div className=\"mt-1 flex items-center justify-center space-x-6 text-xs text-gray-600\">\n            <div className=\"flex items-center\">\n              <span className=\"w-2 h-2 border-b-2 border-red-500 mr-1\"></span>\n              <span>Spelling/Grammar</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"w-2 h-2 border-b-2 border-blue-500 mr-1\"></span>\n              <span>Clarity</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"w-2 h-2 border-b-2 border-purple-500 mr-1\"></span>\n              <span>Engagement</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"w-2 h-2 border-b-2 border-green-500 mr-1\"></span>\n              <span>Delivery</span>\n            </div>\n          </div>\n        )}\n\n        {/* Apply all corrections button */}\n        {result && result.corrections.length > 0 && !selectedCorrection && (\n          <div className=\"mt-2 flex justify-end\">\n            <button\n              onClick={applyAllCorrections}\n              className=\"bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm flex items-center\"\n            >\n              <FiCheck className=\"mr-1\" /> Apply All Corrections\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default GrammarCheckComponent;\n\n\n\n\n\n\n\n\n\n\n"], "names": [], "mappings": ";;;AA6LsB;;AA3LtB;AACA;AACA;;;AAJA;;;;AAwCA,kFAAkF;AAClF,SAAS,aAAa,MAAc;IAClC,OAAO,OAAO,OAAO,CAAC,uBAAuB;AAC/C;AAEA,yDAAyD;AACzD,MAAM,yBAAyB;IAC7B,IAAI,aAAkB,eAAe,OAAO,YAAY,EAAE;QACxD,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,OAAO,SAAS,IAAI,IAAI,KAAK,KAAK,CAAC,WAAW,IAAI;IACpD;IACA,OAAO,IAAI;AACb;AAEA,MAAM,yBAAyB,CAAC;IAC9B,IAAI,aAAkB,eAAe,OAAO,YAAY,EAAE;QACxD,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;eAAI;SAAmB;IACnF;AACF;AAEA,MAAM,wBAA8D,CAAC,EACnE,IAAI,EACJ,oBAAoB,EACpB,kBAAkB,EAClB,iBAAiB,EACjB,kBAAkB,KAAK,EACxB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,IAAI;IAEhF,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,sBAAsB;QACxB;0CAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,iBAAiB;QACnB;0CAAG;QAAC;KAAK;IAET,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM;oEAAmB;oBACvB,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,MAAM,sBAAsB,iBAAiB,OAAO,CAAC,YAAY,GAAG,iBAAiB,OAAO,CAAC,YAAY;wBACzG,eAAe;oBACjB;gBACF;;YAEA,kDAAkD;YAClD,MAAM,QAAQ;yDAAW;oBACvB;gBACF;wDAAG;YAEH,kDAAkD;YAClD,OAAO,gBAAgB,CAAC,UAAU;YAElC;mDAAO;oBACL,aAAa;oBACb,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;0CAAG;QAAC;QAAe;KAAO;IAE1B,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,oDAAoD;YACpD,OAAO,sBAAsB;mDAAG,CAAC;oBAC/B,MAAM,aAAa,QAAQ,YAAY;2DAAK,CAAA,IAAK,EAAE,EAAE,KAAK;;oBAC1D,IAAI,YAAY;wBACd,gBAAgB;oBAClB;gBACF;;YAEA,OAAO,0BAA0B;mDAAG;oBAClC,IAAI,QAAQ;wBACV;oBACF;gBACF;;YAEA,OAAO,wBAAwB;mDAAG,CAAC;oBACjC,MAAM,aAAa,QAAQ,YAAY;2DAAK,CAAA,IAAK,EAAE,EAAE,KAAK;;oBAC1D,IAAI,YAAY;wBACd,kBAAkB;oBACpB;gBACF;;YAEA,mCAAmC;YACnC;mDAAO;oBACL,OAAO,sBAAsB,GAAG;oBAChC,OAAO,0BAA0B,GAAG;oBACpC,OAAO,wBAAwB,GAAG;gBACpC;;QACF;0CAAG;QAAC;KAAO;IAEX,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI;YACJ,IAAI,WAAW;gBACb,mBAAmB;gBACnB,WAAW;uDAAY;wBACrB;+DAAmB,CAAC;gCAClB,IAAI,QAAQ,IAAI,OAAO;gCACvB,OAAO,OAAO,KAAK,MAAM,KAAK;4BAChC;;oBACF;sDAAG;YACL;YACA;mDAAO,IAAM,cAAc;;QAC7B;0CAAG;QAAC;KAAU;IAEd,4BAA4B;IAC5B,MAAM,eAAe;QACnB,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,0BAA0B;QAC1B,gBAAgB;QAEhB,wCAAwC;QACxC,iBAAiB;QAEjB,IAAI;YACF,oBAAoB;YACpB,IAAI,aAAkB,eAAe,OAAO,YAAY,EAAE;gBACxD,MAAM,WAAW,CAAC,aAAa,EAAE,MAAM;gBACvC,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,IAAI,cAAc;oBAChB,MAAM,OAA2B,KAAK,KAAK,CAAC;oBAC5C,sBAAsB;oBACtB,aAAa;oBACb,mBAAmB;oBACnB;gBACF;YACF;YAEA,MAAM,UAAU,6DAAmC;YACnD,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,cAAc,CAAC,EAAE;gBAAE;YAAK;YAErE,IAAI,SAAS,IAAI,EAAE;gBACjB,sBAAsB,SAAS,IAAI;gBAEnC,mBAAmB;gBACnB,IAAI,aAAkB,eAAe,OAAO,YAAY,EAAE;oBACxD,aAAa,OAAO,CAAC,CAAC,aAAa,EAAE,MAAM,EAAE,KAAK,SAAS,CAAC,SAAS,IAAI;gBAC3E;YACF;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;YACb,mBAAmB;QACrB;IACF;IAEA,gCAAgC;IAChC,MAAM,wBAAwB,CAAC;QAC7B,0CAA0C;QAC1C,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,WAAW,GAAG;YACzD,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;YACT;QACF;QAEA,wDAAwD;QACxD,MAAM,qBAAqB,mBAAmB,KAAK,WAAW;QAE9D,gDAAgD;QAChD,MAAM,mBAAmB,IAAI;QAE7B,kDAAkD;QAClD,MAAM,sBAAsB,mBAAmB,GAAG,CAAC,CAAA;YACjD,2CAA2C;YAC3C,MAAM,KAAK,GAAG,WAAW,SAAS,CAAC,CAAC,EAAE,WAAW,KAAK,CAAC,CAAC,EAAE,WAAW,GAAG,EAAE;YAE1E,sCAAsC;YACtC,IAAI,WAAkF;YACtF,IAAI,WAAW,IAAI,KAAK,YAAY;gBAClC,WAAW;YACb,OAAO,IAAI,WAAW,IAAI,KAAK,WAAW;gBACxC,WAAW;YACb,OAAO,IAAI,WAAW,IAAI,KAAK,SAAS;gBACtC,WAAW;YACb,OAAO,IAAI,WAAW,IAAI,KAAK,eAAe;gBAC5C,WAAW;YACb;YAEA,oBAAoB;YACpB,IAAI,WAAsC;YAC1C,IAAI,aAAa,iBAAiB,aAAa,eAAe;gBAC5D,WAAW;YACb,OAAO,IAAI,aAAa,WAAW;gBACjC,WAAW;YACb,OAAO;gBACL,WAAW;YACb;YAEA,6BAA6B;YAC7B,MAAM,aAAa,WAAW,UAAU,EAAE,UAAU,WAAW,SAAS;YAExE,MAAM,qBAAqB;gBACzB,GAAG,UAAU;gBACb;gBACA;gBACA;gBACA;YACF;YAEA,aAAa;YACb,iBAAiB,GAAG,CAAC,IAAI;YAEzB,OAAO;QACT;QAEA,yBAAyB;QACzB,iBAAiB;QAEjB,iCAAiC;QACjC,MAAM,sBAAsB,oBAAoB,MAAM,CACpD,CAAA,aAAc,CAAC,mBAAmB,GAAG,CAAC,WAAW,EAAE;QAGrD,mBAAmB;QACnB,MAAM,oBAAoB;eAAI;SAAoB,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;QAEnF,iBAAiB;QACjB,UAAU;YACR,GAAG,IAAI;YACP,aAAa;QACf;QAEA,gDAAgD;QAChD,IAAI,sBAAsB,kBAAkB,MAAM,GAAG,GAAG;YACtD,mBAAmB;QACrB;IACF;IAEA,oDAAoD;IACpD,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,YAAY,MAAM,EAAE,OAAO,EAAE;QAElC,mBAAmB;QACnB,MAAM,SAAS;eAAI;SAAY,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;QAChE,MAAM,SAAuB,EAAE;QAE/B,8BAA8B;QAC9B,IAAI,UAAU,CAAC;QAEf,KAAK,MAAM,cAAc,OAAQ;YAC/B,2BAA2B;YAC3B,IAAI,WAAW,KAAK,IAAI,WAAW,GAAG,IAClC,WAAW,KAAK,GAAG,KACnB,WAAW,GAAG,GAAG,KAAK,MAAM,IAC5B,CAAC,WAAW,SAAS,IACrB,WAAW,SAAS,CAAC,IAAI,OAAO,IAAI;gBACtC;YACF;YAEA,+BAA+B;YAC/B,IAAI,WAAW,KAAK,GAAG,SAAS;gBAC9B;YACF;YAEA,qEAAqE;YACrE,MAAM,aAAa,KAAK,SAAS,CAAC,WAAW,KAAK,EAAE,WAAW,GAAG;YAClE,IAAI,eAAe,WAAW,SAAS,EAAE;gBACvC,4EAA4E;gBAC5E,MAAM,mBAAmB,aAAa,WAAW,SAAS;gBAC1D,MAAM,QAAQ,IAAI,OAAO,kBAAkB;gBAC3C,MAAM,QAAQ,MAAM,IAAI,CAAC;gBAEzB,IAAI,OAAO;oBACT,sBAAsB;oBACtB,WAAW,KAAK,GAAG,MAAM,KAAK;oBAC9B,WAAW,GAAG,GAAG,MAAM,KAAK,GAAG,WAAW,SAAS,CAAC,MAAM;gBAC5D,OAAO;oBACL,mCAAmC;oBACnC,MAAM,QAAQ,KAAK,OAAO,CAAC,WAAW,SAAS;oBAC/C,IAAI,SAAS,GAAG;wBACd,sBAAsB;wBACtB,WAAW,KAAK,GAAG;wBACnB,WAAW,GAAG,GAAG,QAAQ,WAAW,SAAS,CAAC,MAAM;oBACtD,OAAO;wBAEL;oBACF;gBACF;YACF;YAEA,mCAAmC;YACnC,OAAO,IAAI,CAAC;YACZ,UAAU,WAAW,GAAG;QAC1B;QAEA,OAAO;IACT;IAEA,wEAAwE;IACxE,MAAM,4BAA4B,CAChC,aACA,OACA,KACA;QAEA,MAAM,aAAa,YAAY,CAAC,MAAM,KAAK;QAE3C,OAAO,YAAY,GAAG,CAAC,CAAA;YACrB,mEAAmE;YACnE,IAAI,WAAW,KAAK,IAAI,KAAK;gBAC3B,OAAO;oBACL,GAAG,UAAU;oBACb,OAAO,WAAW,KAAK,GAAG;oBAC1B,KAAK,WAAW,GAAG,GAAG;gBACxB;YACF;YACA,4DAA4D;YAC5D,iEAAiE;YACjE,IAAI,WAAW,KAAK,GAAG,OAAO,WAAW,GAAG,GAAG,OAAO;gBACpD,OAAO;YACT;YACA,gCAAgC;YAChC,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB,CAAC;QACvB,iDAAiD;QACjD,MAAM,SAAS,cAAc,SAAS,CAAC,GAAG,WAAW,KAAK;QAC1D,MAAM,QAAQ,cAAc,SAAS,CAAC,WAAW,GAAG;QACpD,MAAM,UAAU,SAAS,WAAW,UAAU,GAAG;QAEjD,0BAA0B;QAC1B,iBAAiB;QAEjB,6BAA6B;QAC7B,IAAI,WAAW,EAAE,EAAE;YACjB,sBAAsB,CAAA;gBACpB,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,GAAG,CAAC,WAAW,EAAE;gBACxB,uBAAuB;gBACvB,OAAO;YACT;QACF;QAEA,uDAAuD;QACvD,IAAI,QAAQ;YACV,sCAAsC;YACtC,MAAM,qBAAqB,OAAO,WAAW,CAAC,MAAM,CAClD,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;YAG7B,kCAAkC;YAClC,MAAM,aAAa,WAAW,UAAU,CAAC,MAAM,GAAG,CAAC,WAAW,GAAG,GAAG,WAAW,KAAK;YAEpF,mEAAmE;YACnE,MAAM,sBAAsB,mBAAmB,GAAG,CAAC,CAAA;gBACjD,IAAI,EAAE,KAAK,GAAG,WAAW,GAAG,EAAE;oBAC5B,OAAO;wBACL,GAAG,CAAC;wBACJ,OAAO,EAAE,KAAK,GAAG;wBACjB,KAAK,EAAE,GAAG,GAAG;oBACf;gBACF;gBACA,OAAO;YACT;YAEA,UAAU;gBACR,GAAG,MAAM;gBACT,aAAa;YACf;YAEA,wDAAwD;YACxD,IAAI,oBAAoB;gBACtB,mBAAmB;YACrB;QACF;QAEA,4BAA4B;QAC5B,sBAAsB;IAEtB,2EAA2E;IAC3E,0DAA0D;IAC1D,6CAA6C;IAC/C;IAEA,0EAA0E;IAC1E,MAAM,oBAAoB,CAAC;QACzB,6BAA6B;QAC7B,IAAI,WAAW,EAAE,EAAE;YACjB,sBAAsB,CAAA;gBACpB,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,GAAG,CAAC,WAAW,EAAE;gBACxB,uBAAuB;gBACvB,OAAO;YACT;QACF;QAEA,yDAAyD;QACzD,IAAI,QAAQ;YACV,MAAM,qBAAqB,OAAO,WAAW,CAAC,MAAM,CAClD,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;YAG7B,UAAU;gBACR,GAAG,MAAM;gBACT,aAAa;YACf;YAEA,wDAAwD;YACxD,IAAI,oBAAoB;gBACtB,mBAAmB;YACrB;QACF;QAEA,4BAA4B;QAC5B,sBAAsB;IACxB;IAEA,gCAAgC;IAChC,MAAM,sBAAsB;QAC1B,IAAI,CAAC,QAAQ;QAEb,yEAAyE;QACzE,MAAM,oBAAoB;eAAI,OAAO,WAAW;SAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;QAElF,wCAAwC;QACxC,IAAI,UAAU;QAEd,wBAAwB;QACxB,KAAK,MAAM,cAAc,kBAAmB;YAC1C,+BAA+B;YAC/B,IAAI,WAAW,KAAK,GAAG,KAAK,WAAW,GAAG,GAAG,QAAQ,MAAM,IAAI,WAAW,KAAK,IAAI,WAAW,GAAG,EAAE;gBACjG;YACF;YAEA,MAAM,SAAS,QAAQ,SAAS,CAAC,GAAG,WAAW,KAAK;YACpD,MAAM,QAAQ,QAAQ,SAAS,CAAC,WAAW,GAAG;YAC9C,UAAU,SAAS,WAAW,UAAU,GAAG;YAE3C,6BAA6B;YAC7B,IAAI,WAAW,EAAE,EAAE;gBACjB,sBAAsB,CAAA;oBACpB,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,GAAG,CAAC,WAAW,EAAE;oBACxB,uBAAuB;oBACvB,OAAO;gBACT;YACF;QACF;QAEA,0BAA0B;QAC1B,iBAAiB;QAEjB,wBAAwB;QACxB,UAAU;YACR,GAAG,MAAM;YACT,aAAa,EAAE;QACjB;QAEA,4BAA4B;QAC5B,sBAAsB;QAEtB,iEAAiE;QACjE,IAAI,oBAAoB;YACtB,mBAAmB,EAAE;QACvB;IAEA,2EAA2E;IAC7E;IAEA,4BAA4B;IAC5B,MAAM,0BAA0B;QAC9B,sBAAsB,IAAI;QAC1B,IAAI,aAAkB,eAAe,OAAO,YAAY,EAAE;YACxD,aAAa,UAAU,CAAC;QAC1B;QACA,iDAAiD;QACjD;IACF;IAEA,+CAA+C;IAC/C,MAAM,wBAAwB;QAC5B,IAAI,CAAC,UAAU,OAAO,WAAW,CAAC,MAAM,KAAK,GAAG;YAC9C,qBAAO,6LAAC;gBAAE,WAAU;0BAAuB;;;;;;QAC7C;QAEA,uDAAuD;QACvD,MAAM,WAA0B,EAAE;QAClC,IAAI,YAAY;QAEhB,oDAAoD;QACpD,MAAM,oBAAoB;eAAI,OAAO,WAAW;SAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;QAElF,mFAAmF;QACnF,MAAM,iBAAiB;QACvB,MAAM,wBAAwB,kBAAkB,MAAM,GAAG;QAEzD,IAAI,uBAAuB;YACzB,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,kBAAkB,MAAM,CAAC,0BAA0B,EAAE,eAAe,iBAAiB,CAAC;QACjH;QAEA,yDAAyD;QACzD,MAAM,qBAAqB,wBACvB,kBAAkB,KAAK,CAAC,GAAG,kBAC3B;QAEJ,mBAAmB,OAAO,CAAC,CAAC,YAAY;YACtC,iCAAiC;YACjC,IAAI,WAAW,KAAK,GAAG,WAAW;gBAChC,SAAS,IAAI,eACX,6LAAC;8BACE,cAAc,SAAS,CAAC,WAAW,WAAW,KAAK;mBAD3C,CAAC,KAAK,EAAE,OAAO;;;;;YAI9B;YAEA,4CAA4C;YAC5C,IAAI,iBAAiB;YACrB,IAAI,WAAW,QAAQ,KAAK,iBAAiB,WAAW,QAAQ,KAAK,eAAe;gBAClF,iBAAiB;YACnB,OAAO,IAAI,WAAW,QAAQ,KAAK,WAAW;gBAC5C,iBAAiB;YACnB,OAAO,IAAI,WAAW,QAAQ,KAAK,cAAc;gBAC/C,iBAAiB;YACnB,OAAO,IAAI,WAAW,QAAQ,KAAK,YAAY;gBAC7C,iBAAiB;YACnB;YAEA,iCAAiC;YACjC,SAAS,IAAI,eACX,6LAAC;gBAEC,WAAW,GAAG,eAAe,eAAe,CAAC;gBAC7C,OAAO,CAAC,YAAY,EAAE,WAAW,UAAU,EAAE;gBAC7C,SAAS;oBACP,sBAAsB;oBACtB,wDAAwD;oBACxD,IAAI,oBAAoB;wBACtB,mBAAmB;+BAAI,OAAO,WAAW;yBAAC;oBAC5C;gBACF;0BAEC,WAAW,SAAS;eAXhB,CAAC,WAAW,EAAE,OAAO;;;;;YAe9B,YAAY,WAAW,GAAG;QAC5B;QAEA,yBAAyB;QACzB,IAAI,YAAY,cAAc,MAAM,EAAE;YACpC,SAAS,IAAI,eACX,6LAAC;0BACE,cAAc,SAAS,CAAC;eADjB;;;;;QAId;QAEA,qBAAO,6LAAC;YAAI,WAAU;sBAAuC;;;;;;IAC/D;IAEA,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;gCACjD,UAAU,OAAO,WAAW,CAAC,MAAM,GAAG,mBACrC,6LAAC;oCAAI,WAAU;;wCACZ,OAAO,WAAW,CAAC,MAAM;wCAAC;wCAAE,OAAO,WAAW,CAAC,MAAM,KAAK,IAAI,UAAU;;;;;;;gCAG5E,CAAC,mBAAmB,UAAU,OAAO,WAAW,CAAC,MAAM,GAAG,mBACzD,6LAAC;oCACC,SAAS,IAAM,qBAAqB,kBAAkB;oCACtD,WAAU;;sDAEV,6LAAC;4CAAK,WAAU;sDAAO;;;;;;sDACvB,6LAAC;4CAAI,OAAM;4CAA6B,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;4CAAO,QAAO;4CAAe,aAAY;4CAAI,eAAc;4CAAQ,gBAAe;sDACxK,cAAA,6LAAC;gDAAS,QAAO;;;;;;;;;;;;;;;;;;;;;;;sCAMzB,6LAAC;4BAAI,WAAU;;gCACZ,UAAU,OAAO,WAAW,CAAC,MAAM,GAAG,mBACrC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAIH,6LAAC;oCACC,SAAS;oCACT,UAAU,aAAa,CAAC,KAAK,IAAI;oCACjC,WAAW,CAAC,uBAAuB,EACjC,aAAa,CAAC,KAAK,IAAI,KACnB,iDACA,8DACJ;8CAED,YAAY,gBAAgB;;;;;;8CAG/B,6LAAC;oCACC,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAU;oCACV,OAAO,eAAe,qBAAqB;8CAE1C,6BAAe,6LAAC,iJAAA,CAAA,cAAW;wCAAC,MAAM;;;;;6DAAS,6LAAC,iJAAA,CAAA,cAAW;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;gBAKpE,uBACC,6LAAC;oBAAI,WAAU;8BACZ;;;;;;gBAKJ,UAAU,OAAO,WAAW,CAAC,MAAM,GAAG,mBACrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAM9C,6LAAC;oBAAI,WAAW,GAAG,eAAe,oCAAoC,gBAAgB,wCAAwC,CAAC;8BAC7H,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAO;;;;;;kDACvB,6LAAC;wCAAI,OAAM;wCAA6B,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;wCAAO,QAAO;wCAAe,aAAY;wCAAI,eAAc;wCAAQ,gBAAe;kDACxK,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;0CAId,6LAAC;gCACC,KAAK;gCACL,WAAU;gCACV,OAAO;oCAAE,QAAQ;gCAAsB;;oCAEtC,6BACC,6LAAC;wCAAI,WAAU;;;;;;oCAIhB,CAAC;wCACA,yDAAyD;wCACzD,IAAI,cAAc,MAAM,GAAG,MAAM;4CAC/B,qBACE,6LAAC;gDAAI,WAAU;;oDAAoG;oDACxF,cAAc,MAAM;oDAAC;;;;;;;wCAGpD;wCACA,OAAO;oCACT,CAAC;oCAGA,CAAC;wCACA,IAAI,UAAU,OAAO,WAAW,CAAC,MAAM,GAAG,KAAK;4CAC7C,qBACE,6LAAC;gDAAI,WAAU;;oDAAyG;oDAC/G,OAAO,WAAW,CAAC,MAAM;oDAAC;;;;;;;wCAGvC;wCACA,OAAO;oCACT,CAAC;kDAED,6LAAC;wCAAI,WAAW,GAAG,cAAc,MAAM,GAAG,QAAS,UAAU,OAAO,WAAW,CAAC,MAAM,GAAG,MAAO,UAAU,IAAI;kDAC3G,0BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;wDAAC;;;;;;;;;;;8DAG1C,6LAAC;oDAAK,WAAU;;wDAAgB;wDAAqB,KAAK,KAAK,CAAC;wDAAiB;;;;;;;;;;;;iEAGnF,6LAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAab,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCACE,cAAc,KAAK,CAAC,OAAO,MAAM,CAAC,SAAS,MAAM;wCAAC;;;;;;;8CAErD,6LAAC;;wCACE,cAAc,MAAM;wCAAC;;;;;;;8CAExB,6LAAC;;wCACE,cAAc,KAAK,CAAC,UAAU,MAAM,CAAC,SAAS,MAAM;wCAAC;;;;;;;gCAEvD,UAAU,OAAO,WAAW,CAAC,MAAM,GAAG,mBACrC,6LAAC;oCAAK,WAAU;;wCACb,OAAO,WAAW,CAAC,MAAM;wCAAC;;;;;;;;;;;;;sCAKjC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAO,WAAU;gCAAgC,SAAS,IAAM;0CAC/D,cAAA,6LAAC,iJAAA,CAAA,cAAW;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gBAMxB,UAAU,OAAO,WAAW,CAAC,MAAM,GAAG,mBACrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;gBAMX,UAAU,OAAO,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC,oCAC3C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,iJAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAS;;;;;;;;;;;;;;;;;;;;;;;AAO1C;GApwBM;KAAA;uCAswBS", "debugId": null}}, {"offset": {"line": 1202, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Scholarar/V2_mentor/frontend/app/components/text-analyzer/CriteriaCheckingComponent.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport axios from 'axios';\nimport { FiSearch, FiCheckCircle, FiAlertCircle, FiXCircle, FiX } from 'react-icons/fi';\n\ninterface CriteriaCheckingComponentProps {\n  text: string;\n  onAnalysisComplete?: (result: CriteriaCheckResult) => void;\n}\n\ninterface CriteriaCheckResult {\n  content: string;\n  matches: {\n    criteria: string;\n    match_level: 'high' | 'medium' | 'low';\n    explanation: string;\n    score: number;\n  }[];\n  overall_score: number;\n}\n\ninterface ScholarshipType {\n  id: string;\n  name: string;\n  logo: string;\n  description: string;\n}\n\ninterface EssayType {\n  id: string;\n  name: string;\n  scholarship_id: string;\n  criteria: string[];\n  instructions: string;\n}\n\nconst CriteriaCheckingComponent: React.FC<CriteriaCheckingComponentProps> = ({\n  text,\n  onAnalysisComplete\n}) => {\n  const [scholarshipType, setScholarshipType] = useState<string>('');\n  const [essayType, setEssayType] = useState<string>('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [result, setResult] = useState<CriteriaCheckResult | null>(null);\n  const [showScholarshipModal, setShowScholarshipModal] = useState(false);\n  const [showWritingTypeModal, setShowWritingTypeModal] = useState(false);\n  const [selectedScholarship, setSelectedScholarship] = useState<ScholarshipType | null>(null);\n\n  // Enhanced scholarship types with logos and descriptions\n  const [scholarshipTypes, setScholarshipTypes] = useState<ScholarshipType[]>([\n    {\n      id: 'gks',\n      name: 'GKS (Global Korea Scholarship)',\n      logo: '🇰🇷',\n      description: 'Korean Government Scholarship Program for international students'\n    },\n    {\n      id: 'fulbright',\n      name: 'Fulbright Scholarship',\n      logo: '🇺🇸',\n      description: 'US Government educational exchange program'\n    },\n    {\n      id: 'chevening',\n      name: 'Chevening Scholarship',\n      logo: '🇬🇧',\n      description: 'UK Government global scholarship programme'\n    },\n    {\n      id: 'erasmus',\n      name: 'Erasmus Mundus',\n      logo: '🇪🇺',\n      description: 'European Union scholarship for joint master programmes'\n    },\n    {\n      id: 'daad',\n      name: 'DAAD Scholarship',\n      logo: '🇩🇪',\n      description: 'German Academic Exchange Service scholarships'\n    },\n    {\n      id: 'australia_awards',\n      name: 'Australia Awards',\n      logo: '🇦🇺',\n      description: 'Australian Government scholarship program'\n    },\n    {\n      id: 'commonwealth',\n      name: 'Commonwealth Scholarship',\n      logo: '🇬🇧',\n      description: 'UK Commonwealth scholarship for developing countries'\n    },\n    {\n      id: 'gates_cambridge',\n      name: 'Gates Cambridge Scholarship',\n      logo: '🎓',\n      description: 'Full-cost scholarship for outstanding applicants from outside the UK'\n    },\n    {\n      id: 'rhodes',\n      name: 'Rhodes Scholarship',\n      logo: '🏛️',\n      description: 'International postgraduate award for selected foreign students to study at Oxford'\n    }\n  ]);\n\n  const [essayTypes, setEssayTypes] = useState<EssayType[]>([\n    {\n      id: 'personal_statement',\n      name: 'Personal Statement',\n      scholarship_id: 'gks',\n      criteria: [\n        'Motivations with which you apply for this program',\n        'Educational background',\n        'Significant experiences you have had; persons or events that have had a significant influence on you',\n        'Extracurricular activities such as club activities, community service activities or work experiences',\n        'Awards you have received, publications you have made, or skills you have acquired'\n      ],\n      instructions: 'Please type in Korean or in English. The essay must be single spaced within TWO pages, with the font Times New Roman/바탕체/돋움체, size 11. (11 points) The essay should include the following items. Please remove the instructions after reading it.'\n    },\n    {\n      id: 'research_proposal',\n      name: 'Research Proposal',\n      scholarship_id: 'gks',\n      criteria: [\n        'Clear research objectives and questions',\n        'Literature review and theoretical framework',\n        'Methodology and research design',\n        'Expected outcomes and significance',\n        'Timeline and feasibility'\n      ],\n      instructions: 'Describe your research plan in detail including objectives, methodology, and expected outcomes.'\n    },\n    {\n      id: 'study_plan',\n      name: 'Study Plan',\n      scholarship_id: 'gks',\n      criteria: [\n        'Academic goals and objectives',\n        'Course selection rationale',\n        'Career development plan',\n        'How the program aligns with your goals',\n        'Post-graduation plans'\n      ],\n      instructions: 'Outline your academic plan and how it relates to your career objectives.'\n    },\n    {\n      id: 'recommendation_letter',\n      name: 'Recommendation Letter',\n      scholarship_id: 'gks',\n      criteria: [\n        'Academic performance assessment',\n        'Character and personal qualities',\n        'Leadership and teamwork abilities',\n        'Research potential',\n        'Specific examples and achievements'\n      ],\n      instructions: 'Letter should be written by academic or professional references who know you well.'\n    },\n    // Fulbright Essays\n    {\n      id: 'fulbright_personal_statement',\n      name: 'Personal Statement',\n      scholarship_id: 'fulbright',\n      criteria: [\n        'Clear articulation of academic and professional goals',\n        'Demonstration of leadership potential',\n        'Cultural adaptability and ambassadorial qualities',\n        'Commitment to mutual understanding',\n        'Specific knowledge of host country'\n      ],\n      instructions: 'Describe your academic and professional background, goals, and why you want to study in the host country.'\n    },\n    {\n      id: 'fulbright_research_statement',\n      name: 'Research Statement',\n      scholarship_id: 'fulbright',\n      criteria: [\n        'Clear research objectives and methodology',\n        'Significance and innovation of research',\n        'Feasibility within the grant period',\n        'Relevance to host country context',\n        'Expected outcomes and impact'\n      ],\n      instructions: 'Outline your proposed research project, methodology, and expected outcomes.'\n    },\n    // Chevening Essays\n    {\n      id: 'chevening_leadership',\n      name: 'Leadership Essay',\n      scholarship_id: 'chevening',\n      criteria: [\n        'Demonstration of leadership experience',\n        'Impact of leadership activities',\n        'Leadership style and approach',\n        'Future leadership potential',\n        'Specific examples and outcomes'\n      ],\n      instructions: 'Describe your leadership experience and how you have influenced others to achieve a common goal.'\n    },\n    {\n      id: 'chevening_networking',\n      name: 'Networking Essay',\n      scholarship_id: 'chevening',\n      criteria: [\n        'Understanding of networking importance',\n        'Examples of successful networking',\n        'Building and maintaining relationships',\n        'Professional network development',\n        'Future networking plans'\n      ],\n      instructions: 'Explain how you build and maintain relationships and how you plan to use these skills in the future.'\n    }\n  ]);\n\n  // Filtered essay types based on selected scholarship\n  const filteredEssayTypes = essayTypes.filter(\n    type => type.scholarship_id === scholarshipType\n  );\n\n  // Get selected essay type details\n  const selectedEssayType = essayTypes.find(type => type.id === essayType);\n\n  // Function to handle scholarship selection\n  const handleScholarshipSelect = (scholarship: ScholarshipType) => {\n    setSelectedScholarship(scholarship);\n    setScholarshipType(scholarship.id);\n    setShowScholarshipModal(false);\n    setShowWritingTypeModal(true);\n  };\n\n  // Function to handle writing type selection\n  const handleWritingTypeSelect = (writingType: EssayType) => {\n    setEssayType(writingType.id);\n    setShowWritingTypeModal(false);\n\n    // Re-analyze with the specific scholarship criteria\n    setTimeout(() => {\n      analyzeCriteria();\n    }, 500);\n  };\n\n  // Function to start criteria check process\n  const startCriteriaCheck = () => {\n    setShowScholarshipModal(true);\n  };\n\n  // Function to analyze text against criteria\n  const analyzeCriteria = async () => {\n    if (!text.trim()) {\n      setError('Please enter some text to analyze');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Generic criteria for initial analysis (before scholarship selection)\n      const genericCriteria = [\n        'Clear thesis and main argument',\n        'Supporting evidence and examples',\n        'Writing clarity and coherence',\n        'Grammar and language usage',\n        'Overall structure and organization'\n      ];\n\n      // Use selected criteria if available, otherwise use generic criteria\n      const criteriaToUse = selectedEssayType?.criteria || genericCriteria;\n\n      // Mock analysis result for demonstration\n      const mockResult: CriteriaCheckResult = {\n        content: 'Analysis completed successfully',\n        overall_score: 33,\n        matches: criteriaToUse.map((criterion, index) => ({\n          criteria: criterion,\n          match_level: index === 0 ? 'high' : index === 1 ? 'medium' : 'low',\n          explanation: `Excellent ${criterion.toLowerCase()} with a compelling approach that effectively captures the reader's attention and sets up your statement perfectly.`,\n          score: index === 0 ? 100 : index === 1 ? 60 : 20\n        }))\n      };\n\n      setResult(mockResult);\n      if (onAnalysisComplete) {\n        onAnalysisComplete(mockResult);\n      }\n\n      // Automatically show scholarship selection popup after analysis is completed\n      // Only if no scholarship is selected yet\n      if (!scholarshipType || !essayType) {\n        setTimeout(() => {\n          setShowScholarshipModal(true);\n        }, 1000); // Small delay to let user see the results first\n      }\n    } catch (err: any) {\n      console.error('Criteria check error:', err);\n      setError(err.message || 'An error occurred during criteria analysis');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Function to get color based on score\n  const getScoreColor = (score: number) => {\n    if (score >= 80) return 'text-green-500';\n    if (score >= 60) return 'text-yellow-500';\n    return 'text-red-500';\n  };\n\n  // Function to get background color based on score\n  const getScoreBgColor = (score: number) => {\n    if (score >= 80) return 'bg-green-50 border-green-200';\n    if (score >= 60) return 'bg-yellow-50 border-yellow-200';\n    return 'bg-red-50 border-red-200';\n  };\n\n  // Function to get progress bar color based on score\n  const getProgressColor = (score: number) => {\n    if (score >= 80) return 'bg-green-500';\n    if (score >= 60) return 'bg-yellow-500';\n    return 'bg-red-500';\n  };\n\n  return (\n    <div className=\"w-full\">\n      <div className=\"mb-4\">\n        <h3 className=\"text-md font-medium mb-2\">Scholarship Criteria Check</h3>\n\n        {/* Selection Summary */}\n        {selectedScholarship && selectedEssayType && (\n          <div className=\"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <span className=\"text-2xl\">{selectedScholarship.logo}</span>\n              <div>\n                <div className=\"font-medium text-sm\">{selectedScholarship.name}</div>\n                <div className=\"text-xs text-gray-600\">{selectedEssayType.name}</div>\n              </div>\n            </div>\n            <div className=\"text-xs text-gray-500 mb-2\">\n              <strong>Instructions:</strong> {selectedEssayType.instructions}\n            </div>\n            <div className=\"text-xs text-gray-500\">\n              <strong>Criteria:</strong> {selectedEssayType.criteria.length} evaluation points\n            </div>\n          </div>\n        )}\n\n        <button\n          onClick={analyzeCriteria}\n          disabled={isLoading || !text.trim()}\n          className={`flex items-center px-4 py-2 rounded-md w-full justify-center ${\n            isLoading || !text.trim()\n              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n              : 'bg-blue-600 text-white hover:bg-blue-700'\n          }`}\n        >\n          {isLoading ? (\n            <>\n              <FiSearch className=\"animate-spin mr-2\" /> Analyzing...\n            </>\n          ) : (\n            <>\n              <FiSearch className=\"mr-2\" /> Analyze Text\n            </>\n          )}\n        </button>\n\n        {/* Optional: Add a manual scholarship selection button */}\n        {!scholarshipType && !essayType && (\n          <button\n            onClick={startCriteriaCheck}\n            disabled={isLoading}\n            className=\"flex items-center px-4 py-2 rounded-md w-full justify-center mt-2 bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300\"\n          >\n            <FiSearch className=\"mr-2\" /> Select Specific Scholarship\n          </button>\n        )}\n      </div>\n\n      {error && (\n        <div className=\"p-3 bg-red-50 text-red-700 rounded-md mb-4\">\n          {error}\n        </div>\n      )}\n\n      {/* Scholarship Selection Modal */}\n      <AnimatePresence>\n        {showScholarshipModal && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\"\n            onClick={() => setShowScholarshipModal(false)}\n          >\n            <motion.div\n              initial={{ scale: 0.9, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0.9, opacity: 0 }}\n              className=\"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\"\n              onClick={(e) => e.stopPropagation()}\n            >\n              <div className=\"flex justify-between items-center mb-4\">\n                <h2 className=\"text-xl font-semibold\">Select Scholarship</h2>\n                <button\n                  onClick={() => setShowScholarshipModal(false)}\n                  className=\"text-gray-500 hover:text-gray-700\"\n                >\n                  <FiX size={24} />\n                </button>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {scholarshipTypes.map((scholarship) => (\n                  <motion.button\n                    key={scholarship.id}\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    onClick={() => handleScholarshipSelect(scholarship)}\n                    className=\"p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all text-left\"\n                  >\n                    <div className=\"flex items-center gap-3 mb-2\">\n                      <span className=\"text-3xl\">{scholarship.logo}</span>\n                      <div>\n                        <div className=\"font-medium text-sm\">{scholarship.name}</div>\n                      </div>\n                    </div>\n                    <p className=\"text-xs text-gray-600\">{scholarship.description}</p>\n                  </motion.button>\n                ))}\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Writing Type Selection Modal */}\n      <AnimatePresence>\n        {showWritingTypeModal && selectedScholarship && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\"\n            onClick={() => setShowWritingTypeModal(false)}\n          >\n            <motion.div\n              initial={{ scale: 0.9, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0.9, opacity: 0 }}\n              className=\"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\"\n              onClick={(e) => e.stopPropagation()}\n            >\n              <div className=\"flex justify-between items-center mb-4\">\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"text-2xl\">{selectedScholarship.logo}</span>\n                  <h2 className=\"text-xl font-semibold\">{selectedScholarship.name}</h2>\n                </div>\n                <button\n                  onClick={() => setShowWritingTypeModal(false)}\n                  className=\"text-gray-500 hover:text-gray-700\"\n                >\n                  <FiX size={24} />\n                </button>\n              </div>\n\n              <p className=\"text-sm text-gray-600 mb-4\">Select the type of writing you want to evaluate:</p>\n\n              <div className=\"space-y-3\">\n                {filteredEssayTypes.map((writingType) => (\n                  <motion.button\n                    key={writingType.id}\n                    whileHover={{ scale: 1.01 }}\n                    whileTap={{ scale: 0.99 }}\n                    onClick={() => handleWritingTypeSelect(writingType)}\n                    className=\"w-full p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all text-left\"\n                  >\n                    <div className=\"font-medium text-sm mb-2\">{writingType.name}</div>\n                    <div className=\"text-xs text-gray-600 mb-2\">{writingType.instructions}</div>\n                    <div className=\"text-xs text-gray-500\">\n                      <strong>Criteria:</strong> {writingType.criteria.length} evaluation points\n                    </div>\n                  </motion.button>\n                ))}\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Results Display */}\n      {result && (\n        <motion.div\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"space-y-4\"\n        >\n          {/* Overall Score Header */}\n          <div className=\"bg-white border border-gray-200 rounded-lg p-4\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h3 className=\"text-lg font-semibold\">Criteria Check</h3>\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-red-500\">{result.overall_score}/100</div>\n                <div className=\"text-sm text-gray-500\">Overall Score</div>\n              </div>\n            </div>\n\n            {/* Overall Progress Bar */}\n            <div className=\"w-full bg-gray-200 rounded-full h-3 mb-4\">\n              <div\n                className={`h-3 rounded-full transition-all duration-500 ${getProgressColor(result.overall_score)}`}\n                style={{ width: `${result.overall_score}%` }}\n              ></div>\n            </div>\n\n            <div className=\"text-sm text-gray-600\">\n              {selectedEssayType?.name} Evaluation\n            </div>\n          </div>\n\n          {/* Individual Criteria Results */}\n          {result.matches && result.matches.length > 0 && (\n            <div className=\"space-y-3\">\n              {result.matches.map((match, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                  className={`border rounded-lg p-4 ${getScoreBgColor(match.score)}`}\n                >\n                  <div className=\"flex justify-between items-start mb-3\">\n                    <h4 className=\"font-medium text-sm flex-1\">{match.criteria}</h4>\n                    <div className={`text-lg font-bold ${getScoreColor(match.score)}`}>\n                      {match.score}/100\n                    </div>\n                  </div>\n\n                  {/* Progress Bar for Individual Criteria */}\n                  <div className=\"w-full bg-gray-200 rounded-full h-2 mb-3\">\n                    <div\n                      className={`h-2 rounded-full transition-all duration-500 ${getProgressColor(match.score)}`}\n                      style={{ width: `${match.score}%` }}\n                    ></div>\n                  </div>\n\n                  <p className=\"text-sm text-gray-700\">{match.explanation}</p>\n                </motion.div>\n              ))}\n            </div>\n          )}\n\n          <div className=\"text-sm text-gray-500 mt-4 p-3 bg-gray-50 rounded-md\">\n            <p>\n              <strong>💡 Tip:</strong> Focus on improving criteria with scores below 60 to enhance your overall application strength.\n            </p>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default CriteriaCheckingComponent;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;;;AALA;;;;AAsCA,MAAM,4BAAsE,CAAC,EAC3E,IAAI,EACJ,kBAAkB,EACnB;;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACjE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAEvF,yDAAyD;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;QAC1E;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;KACD;IAED,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QACxD;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;QACA,mBAAmB;QACnB;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;QACA,mBAAmB;QACnB;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;KACD;IAED,qDAAqD;IACrD,MAAM,qBAAqB,WAAW,MAAM,CAC1C,CAAA,OAAQ,KAAK,cAAc,KAAK;IAGlC,kCAAkC;IAClC,MAAM,oBAAoB,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAE9D,2CAA2C;IAC3C,MAAM,0BAA0B,CAAC;QAC/B,uBAAuB;QACvB,mBAAmB,YAAY,EAAE;QACjC,wBAAwB;QACxB,wBAAwB;IAC1B;IAEA,4CAA4C;IAC5C,MAAM,0BAA0B,CAAC;QAC/B,aAAa,YAAY,EAAE;QAC3B,wBAAwB;QAExB,oDAAoD;QACpD,WAAW;YACT;QACF,GAAG;IACL;IAEA,2CAA2C;IAC3C,MAAM,qBAAqB;QACzB,wBAAwB;IAC1B;IAEA,4CAA4C;IAC5C,MAAM,kBAAkB;QACtB,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,uEAAuE;YACvE,MAAM,kBAAkB;gBACtB;gBACA;gBACA;gBACA;gBACA;aACD;YAED,qEAAqE;YACrE,MAAM,gBAAgB,mBAAmB,YAAY;YAErD,yCAAyC;YACzC,MAAM,aAAkC;gBACtC,SAAS;gBACT,eAAe;gBACf,SAAS,cAAc,GAAG,CAAC,CAAC,WAAW,QAAU,CAAC;wBAChD,UAAU;wBACV,aAAa,UAAU,IAAI,SAAS,UAAU,IAAI,WAAW;wBAC7D,aAAa,CAAC,UAAU,EAAE,UAAU,WAAW,GAAG,kHAAkH,CAAC;wBACrK,OAAO,UAAU,IAAI,MAAM,UAAU,IAAI,KAAK;oBAChD,CAAC;YACH;YAEA,UAAU;YACV,IAAI,oBAAoB;gBACtB,mBAAmB;YACrB;YAEA,6EAA6E;YAC7E,yCAAyC;YACzC,IAAI,CAAC,mBAAmB,CAAC,WAAW;gBAClC,WAAW;oBACT,wBAAwB;gBAC1B,GAAG,OAAO,gDAAgD;YAC5D;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,uCAAuC;IACvC,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,kDAAkD;IAClD,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,oDAAoD;IACpD,MAAM,mBAAmB,CAAC;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2B;;;;;;oBAGxC,uBAAuB,mCACtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAY,oBAAoB,IAAI;;;;;;kDACpD,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAuB,oBAAoB,IAAI;;;;;;0DAC9D,6LAAC;gDAAI,WAAU;0DAAyB,kBAAkB,IAAI;;;;;;;;;;;;;;;;;;0CAGlE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAO;;;;;;oCAAsB;oCAAE,kBAAkB,YAAY;;;;;;;0CAEhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAO;;;;;;oCAAkB;oCAAE,kBAAkB,QAAQ,CAAC,MAAM;oCAAC;;;;;;;;;;;;;kCAKpE,6LAAC;wBACC,SAAS;wBACT,UAAU,aAAa,CAAC,KAAK,IAAI;wBACjC,WAAW,CAAC,6DAA6D,EACvE,aAAa,CAAC,KAAK,IAAI,KACnB,iDACA,4CACJ;kCAED,0BACC;;8CACE,6LAAC,iJAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAsB;;yDAG5C;;8CACE,6LAAC,iJAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAS;;;;;;;;oBAMlC,CAAC,mBAAmB,CAAC,2BACpB,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;;0CAEV,6LAAC,iJAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAS;;;;;;;;;;;;;YAKlC,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAKL,6LAAC,4LAAA,CAAA,kBAAe;0BACb,sCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS,IAAM,wBAAwB;8BAEvC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,MAAM;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAC/B,WAAU;wBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;0CAEjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCACC,SAAS,IAAM,wBAAwB;wCACvC,WAAU;kDAEV,cAAA,6LAAC,iJAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAIf,6LAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,4BACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,wBAAwB;wCACvC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAY,YAAY,IAAI;;;;;;kEAC5C,6LAAC;kEACC,cAAA,6LAAC;4DAAI,WAAU;sEAAuB,YAAY,IAAI;;;;;;;;;;;;;;;;;0DAG1D,6LAAC;gDAAE,WAAU;0DAAyB,YAAY,WAAW;;;;;;;uCAZxD,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BAsBjC,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBAAwB,qCACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS,IAAM,wBAAwB;8BAEvC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,MAAM;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAC/B,WAAU;wBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;0CAEjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAY,oBAAoB,IAAI;;;;;;0DACpD,6LAAC;gDAAG,WAAU;0DAAyB,oBAAoB,IAAI;;;;;;;;;;;;kDAEjE,6LAAC;wCACC,SAAS,IAAM,wBAAwB;wCACvC,WAAU;kDAEV,cAAA,6LAAC,iJAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAIf,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAE1C,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,GAAG,CAAC,CAAC,4BACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,wBAAwB;wCACvC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DAA4B,YAAY,IAAI;;;;;;0DAC3D,6LAAC;gDAAI,WAAU;0DAA8B,YAAY,YAAY;;;;;;0DACrE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAO;;;;;;oDAAkB;oDAAE,YAAY,QAAQ,CAAC,MAAM;oDAAC;;;;;;;;uCATrD,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;YAoBhC,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAGV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDAAmC,OAAO,aAAa;oDAAC;;;;;;;0DACvE,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAK3C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAW,CAAC,6CAA6C,EAAE,iBAAiB,OAAO,aAAa,GAAG;oCACnG,OAAO;wCAAE,OAAO,GAAG,OAAO,aAAa,CAAC,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAI/C,6LAAC;gCAAI,WAAU;;oCACZ,mBAAmB;oCAAK;;;;;;;;;;;;;oBAK5B,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,MAAM,GAAG,mBACzC,6LAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,WAAW,CAAC,sBAAsB,EAAE,gBAAgB,MAAM,KAAK,GAAG;;kDAElE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA8B,MAAM,QAAQ;;;;;;0DAC1D,6LAAC;gDAAI,WAAW,CAAC,kBAAkB,EAAE,cAAc,MAAM,KAAK,GAAG;;oDAC9D,MAAM,KAAK;oDAAC;;;;;;;;;;;;;kDAKjB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAW,CAAC,6CAA6C,EAAE,iBAAiB,MAAM,KAAK,GAAG;4CAC1F,OAAO;gDAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAItC,6LAAC;wCAAE,WAAU;kDAAyB,MAAM,WAAW;;;;;;;+BArBlD;;;;;;;;;;kCA2Bb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;8CAAO;;;;;;gCAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;GA9gBM;KAAA;uCAghBS", "debugId": null}}, {"offset": {"line": 2169, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Scholarar/V2_mentor/frontend/app/components/text-analyzer/HierarchicalHistorySidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  FiPlus,\n  FiMenu,\n  FiX,\n  FiFolder,\n  FiFile,\n  FiFileText,\n  FiEdit3,\n  FiCheck,\n  FiSearch,\n  FiClock\n} from 'react-icons/fi';\nimport { format } from 'date-fns';\nimport { HistoryEntry, GroupedHistoryEntry } from './HistoryEntry';\n\ninterface HierarchicalHistorySidebarProps {\n  isVisible: boolean;\n  toggleSidebar: () => void;\n  groupedEntries: GroupedHistoryEntry[];\n  activeInputId: string | null;\n  activeFeatureId: string | null;\n  onInputClick: (inputId: string) => void;\n  onFeatureClick: (featureId: string) => void;\n  onNewAnalysis: () => void;\n  onShowAllHistory: () => void;\n}\n\nconst HierarchicalHistorySidebar: React.FC<HierarchicalHistorySidebarProps> = ({\n  isVisible,\n  toggleSidebar,\n  groupedEntries,\n  activeInputId,\n  activeFeatureId,\n  onInputClick,\n  onFeatureClick,\n  onNewAnalysis\n}) => {\n  const [expandedInputs, setExpandedInputs] = useState<Set<string>>(new Set());\n\n  // Toggle expansion of an input group\n  const toggleInputExpansion = (inputId: string) => {\n    setExpandedInputs(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(inputId)) {\n        newSet.delete(inputId);\n      } else {\n        newSet.add(inputId);\n      }\n      return newSet;\n    });\n  };\n\n  // Get feature icon\n  const getFeatureIcon = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {\n    switch (featureType) {\n      case 'ai-detector':\n        return <FiSearch className=\"text-purple-500\" size={14} />;\n      case 'grammar-check':\n        return <FiCheck className=\"text-green-500\" size={14} />;\n      case 'criteria-check':\n        return <FiFileText className=\"text-blue-500\" size={14} />;\n      case 'paraphraser':\n        return <FiEdit3 className=\"text-orange-500\" size={14} />;\n      default:\n        return <FiFile className=\"text-gray-500\" size={14} />;\n    }\n  };\n\n  // Get feature name\n  const getFeatureName = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {\n    switch (featureType) {\n      case 'ai-detector':\n        return 'AI Detector';\n      case 'grammar-check':\n        return 'Grammar Check';\n      case 'criteria-check':\n        return 'Criteria Check';\n      case 'paraphraser':\n        return 'Paraphraser';\n      default:\n        return 'Unknown Feature';\n    }\n  };\n\n  // Get truncated text preview\n  const getTextPreview = (text: string) => {\n    if (!text) return 'No input text';\n    return text.length > 40\n      ? `${text.substring(0, 40)}...`\n      : text;\n  };\n\n  // Animation variants\n  const sidebarVariants = {\n    open: {\n      width: '320px',\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 30\n      }\n    },\n    closed: {\n      width: '0px',\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 30\n      }\n    }\n  };\n\n  return (\n    <>\n      {/* Toggle button - visible when sidebar is closed */}\n      {!isVisible && (\n        <motion.button\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          className=\"absolute left-4 top-4 z-20 p-2 bg-white rounded-full shadow-md hover:bg-gray-100\"\n          onClick={toggleSidebar}\n        >\n          <FiMenu size={20} />\n        </motion.button>\n      )}\n\n      {/* Sidebar */}\n      <motion.div\n        className=\"h-full bg-gray-50 border-r border-gray-200 overflow-hidden\"\n        variants={sidebarVariants}\n        initial=\"closed\"\n        animate={isVisible ? \"open\" : \"closed\"}\n      >\n        {isVisible && (\n          <div className=\"h-full flex flex-col p-4\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-lg font-semibold\">History</h2>\n              <button\n                onClick={toggleSidebar}\n                className=\"p-1 rounded-full hover:bg-gray-200\"\n              >\n                <FiX size={20} />\n              </button>\n            </div>\n\n            <button\n              onClick={onNewAnalysis}\n              className=\"w-full bg-blue-600 text-white py-2 rounded-lg mb-4 hover:bg-blue-700 transition-colors flex items-center justify-center\"\n            >\n              <FiPlus className=\"mr-2\" />\n              New Analysis\n            </button>\n\n            <div className=\"flex-1 overflow-y-auto\">\n              <AnimatePresence>\n                {groupedEntries.length > 0 ? (\n                  groupedEntries.map((group) => (\n                    <motion.div\n                      key={group.inputId}\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: -10 }}\n                      className=\"mb-2\"\n                    >\n                      {/* Input Group Header */}\n                      <div\n                        className={`flex items-center p-2 rounded-lg cursor-pointer transition-all ${\n                          activeInputId === group.inputId\n                            ? 'bg-blue-100 border border-blue-300'\n                            : 'bg-white hover:bg-gray-50 border border-gray-200'\n                        }`}\n                        onClick={() => {\n                          onInputClick(group.inputId);\n                          toggleInputExpansion(group.inputId);\n                        }}\n                      >\n                        <div className=\"mr-2\">\n                          {expandedInputs.has(group.inputId) ? (\n                            <FiFolder className=\"text-blue-500\" size={16} />\n                          ) : (\n                            <FiFolder className=\"text-blue-500\" size={16} />\n                          )}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"text-sm font-medium truncate\">\n                            {getTextPreview(group.inputText)}\n                          </div>\n                          <div className=\"flex items-center text-xs text-gray-500\">\n                            <FiClock className=\"mr-1\" size={10} />\n                            {format(group.timestamp, 'MMM d, h:mm a')}\n                            <span className=\"ml-2 bg-gray-200 px-1 rounded text-xs\">\n                              {group.features.length}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Feature List */}\n                      <AnimatePresence>\n                        {expandedInputs.has(group.inputId) && (\n                          <motion.div\n                            initial={{ opacity: 0, height: 0 }}\n                            animate={{ opacity: 1, height: 'auto' }}\n                            exit={{ opacity: 0, height: 0 }}\n                            className=\"ml-4 mt-1 space-y-1\"\n                          >\n                            {group.features.map((feature) => (\n                              <div key={feature.id}>\n                                <motion.div\n                                  initial={{ opacity: 0, x: -10 }}\n                                  animate={{ opacity: 1, x: 0 }}\n                                  exit={{ opacity: 0, x: -10 }}\n                                  className={`flex items-center p-2 rounded cursor-pointer transition-all ${\n                                    activeFeatureId === feature.id\n                                      ? 'bg-blue-50 border-l-2 border-blue-500'\n                                      : 'hover:bg-gray-50'\n                                  }`}\n                                  onClick={() => onFeatureClick(feature.id)}\n                                >\n                                  <div className=\"mr-2\">\n                                    {getFeatureIcon(feature.featureType)}\n                                  </div>\n                                  <div className=\"flex-1 min-w-0\">\n                                    <div className=\"text-xs font-medium\">\n                                      {getFeatureName(feature.featureType)}\n                                    </div>\n                                    <div className=\"text-xs text-gray-500\">\n                                      {format(feature.timestamp, 'h:mm a')}\n                                    </div>\n                                  </div>\n                                </motion.div>\n                              </div>\n                            ))}\n                          </motion.div>\n                        )}\n                      </AnimatePresence>\n                    </motion.div>\n                  ))\n                ) : (\n                  <div className=\"text-center text-gray-500 mt-8\">\n                    <p>No history yet</p>\n                    <p className=\"text-sm mt-2\">\n                      Analyze text to see your history here\n                    </p>\n                  </div>\n                )}\n              </AnimatePresence>\n            </div>\n          </div>\n        )}\n      </motion.div>\n    </>\n  );\n};\n\nexport default HierarchicalHistorySidebar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAYA;;;AAhBA;;;;;AA+BA,MAAM,6BAAwE,CAAC,EAC7E,SAAS,EACT,aAAa,EACb,cAAc,EACd,aAAa,EACb,eAAe,EACf,YAAY,EACZ,cAAc,EACd,aAAa,EACd;;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAEtE,qCAAqC;IACrC,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB,CAAA;YAChB,MAAM,SAAS,IAAI,IAAI;YACvB,IAAI,OAAO,GAAG,CAAC,UAAU;gBACvB,OAAO,MAAM,CAAC;YAChB,OAAO;gBACL,OAAO,GAAG,CAAC;YACb;YACA,OAAO;QACT;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,WAAQ;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YACrD,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACnD,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,aAAU;oBAAC,WAAU;oBAAgB,MAAM;;;;;;YACrD,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YACpD;gBACE,qBAAO,6LAAC,iJAAA,CAAA,SAAM;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACnD;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,6BAA6B;IAC7B,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KAAK,MAAM,GAAG,KACjB,GAAG,KAAK,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAC7B;IACN;IAEA,qBAAqB;IACrB,MAAM,kBAAkB;QACtB,MAAM;YACJ,OAAO;YACP,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;QACA,QAAQ;YACN,OAAO;YACP,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,qBACE;;YAEG,CAAC,2BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;gBACV,SAAS;0BAET,cAAA,6LAAC,iJAAA,CAAA,SAAM;oBAAC,MAAM;;;;;;;;;;;0BAKlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAS,YAAY,SAAS;0BAE7B,2BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,iJAAA,CAAA,MAAG;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAIf,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,iJAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAS;;;;;;;sCAI7B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;0CACb,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,WAAU;;0DAGV,6LAAC;gDACC,WAAW,CAAC,+DAA+D,EACzE,kBAAkB,MAAM,OAAO,GAC3B,uCACA,oDACJ;gDACF,SAAS;oDACP,aAAa,MAAM,OAAO;oDAC1B,qBAAqB,MAAM,OAAO;gDACpC;;kEAEA,6LAAC;wDAAI,WAAU;kEACZ,eAAe,GAAG,CAAC,MAAM,OAAO,kBAC/B,6LAAC,iJAAA,CAAA,WAAQ;4DAAC,WAAU;4DAAgB,MAAM;;;;;iFAE1C,6LAAC,iJAAA,CAAA,WAAQ;4DAAC,WAAU;4DAAgB,MAAM;;;;;;;;;;;kEAG9C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,eAAe,MAAM,SAAS;;;;;;0EAEjC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,iJAAA,CAAA,UAAO;wEAAC,WAAU;wEAAO,MAAM;;;;;;oEAC/B,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,SAAS,EAAE;kFACzB,6LAAC;wEAAK,WAAU;kFACb,MAAM,QAAQ,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0DAO9B,6LAAC,4LAAA,CAAA,kBAAe;0DACb,eAAe,GAAG,CAAC,MAAM,OAAO,mBAC/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,QAAQ;oDAAE;oDACjC,SAAS;wDAAE,SAAS;wDAAG,QAAQ;oDAAO;oDACtC,MAAM;wDAAE,SAAS;wDAAG,QAAQ;oDAAE;oDAC9B,WAAU;8DAET,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACnB,6LAAC;sEACC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,MAAM;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC3B,WAAW,CAAC,4DAA4D,EACtE,oBAAoB,QAAQ,EAAE,GAC1B,0CACA,oBACJ;gEACF,SAAS,IAAM,eAAe,QAAQ,EAAE;;kFAExC,6LAAC;wEAAI,WAAU;kFACZ,eAAe,QAAQ,WAAW;;;;;;kFAErC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACZ,eAAe,QAAQ,WAAW;;;;;;0FAErC,6LAAC;gFAAI,WAAU;0FACZ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,SAAS,EAAE;;;;;;;;;;;;;;;;;;2DApBzB,QAAQ,EAAE;;;;;;;;;;;;;;;;uCAjDvB,MAAM,OAAO;;;;8DAiFtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAE;;;;;;sDACH,6LAAC;4CAAE,WAAU;sDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhD;GAnOM;KAAA;uCAqOS", "debugId": null}}, {"offset": {"line": 2622, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Scholarar/V2_mentor/frontend/app/components/text-analyzer/ResultCardsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  FiFileText,\n  FiEdit3,\n  FiCheck,\n  FiSearch,\n  FiClock,\n  FiAlertTriangle\n} from 'react-icons/fi';\nimport { format } from 'date-fns';\nimport { HistoryEntry } from './HistoryEntry';\n\ninterface ResultCardsPanelProps {\n  entries: HistoryEntry[];\n  isLoading?: boolean;\n  error?: string | null;\n}\n\nconst ResultCardsPanel: React.FC<ResultCardsPanelProps> = ({\n  entries,\n  isLoading = false,\n  error = null\n}) => {\n  // Get feature icon\n  const getFeatureIcon = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {\n    switch (featureType) {\n      case 'ai-detector':\n        return <FiSearch className=\"text-purple-500\" size={18} />;\n      case 'grammar-check':\n        return <FiCheck className=\"text-green-500\" size={18} />;\n      case 'criteria-check':\n        return <FiFileText className=\"text-blue-500\" size={18} />;\n      case 'paraphraser':\n        return <FiEdit3 className=\"text-orange-500\" size={18} />;\n      default:\n        return <FiFileText className=\"text-gray-500\" size={18} />;\n    }\n  };\n\n  // Get feature name\n  const getFeatureName = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {\n    switch (featureType) {\n      case 'ai-detector':\n        return 'AI Detector';\n      case 'grammar-check':\n        return 'Grammar Check';\n      case 'criteria-check':\n        return 'Criteria Check';\n      case 'paraphraser':\n        return 'Paraphraser';\n      default:\n        return 'Unknown Feature';\n    }\n  };\n\n  // Get badge color\n  const getBadgeColor = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {\n    switch (featureType) {\n      case 'ai-detector':\n        return 'bg-purple-100 text-purple-700 border-purple-200';\n      case 'grammar-check':\n        return 'bg-green-100 text-green-700 border-green-200';\n      case 'criteria-check':\n        return 'bg-blue-100 text-blue-700 border-blue-200';\n      case 'paraphraser':\n        return 'bg-orange-100 text-orange-700 border-orange-200';\n      default:\n        return 'bg-gray-100 text-gray-700 border-gray-200';\n    }\n  };\n\n  // Render AI Detection result\n  const renderAIDetectionResult = (resultContent: any) => {\n    if (!resultContent) return <p className=\"text-gray-500\">No AI detection results available.</p>;\n\n    const overallScore = resultContent.ai_score * 100;\n\n    return (\n      <div>\n        <div className=\"mb-4 flex items-center justify-between\">\n          <h4 className=\"text-sm font-medium\">AI Content Detection</h4>\n          <div className=\"text-lg font-bold\">\n            <span className={`${overallScore > 70 ? 'text-red-500' : overallScore > 30 ? 'text-yellow-500' : 'text-green-500'}`}>\n              {overallScore.toFixed(1)}% AI\n            </span>\n          </div>\n        </div>\n\n        <div className=\"mt-2 p-3 bg-yellow-50 rounded-md text-sm\">\n          {resultContent.sentences?.map((sentence: any, index: number) => {\n            const isAI = sentence.prediction === 'ai-generated' && sentence.ai_score > 0.5;\n            return (\n              <span key={index} className={isAI ? 'bg-yellow-200 px-1 rounded' : ''}>\n                {sentence.text}{' '}\n              </span>\n            );\n          })}\n        </div>\n\n        <div className=\"mt-4\">\n          <div className=\"h-2 w-full bg-gray-200 rounded-full overflow-hidden\">\n            <div\n              className={`h-full ${overallScore > 70 ? 'bg-red-500' : overallScore > 30 ? 'bg-yellow-500' : 'bg-green-500'}`}\n              style={{ width: `${overallScore}%` }}\n            ></div>\n          </div>\n          <div className=\"flex justify-between text-xs mt-1\">\n            <span>Human Content</span>\n            <span>AI Content</span>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  // Render Grammar Check result\n  const renderGrammarResult = (resultContent: any) => {\n    if (!resultContent) {\n      return <p className=\"text-gray-500\">No grammar check result.</p>;\n    }\n\n    // Show the corrected text if available, otherwise show original\n    const correctedText = resultContent.corrected_text || resultContent.originalText || 'No corrected text available';\n\n    return (\n      <div>\n        <div className=\"mb-3\">\n          <h4 className=\"text-sm font-medium mb-2\">Grammar Check Result</h4>\n          {resultContent.corrections && (\n            <div className=\"text-xs text-gray-600\">\n              {resultContent.corrections.length} {resultContent.corrections.length === 1 ? 'correction' : 'corrections'} applied\n            </div>\n          )}\n        </div>\n\n        <div className=\"p-4 bg-green-50 rounded-md border border-green-200\">\n          <p className=\"text-sm text-gray-800 leading-relaxed\">{correctedText}</p>\n        </div>\n      </div>\n    );\n  };\n\n  // Render Paraphrase result\n  const renderParaphraseResult = (resultContent: any) => {\n    if (!resultContent || !resultContent.paraphrased_text) {\n      return <p className=\"text-gray-500\">No paraphrased text available.</p>;\n    }\n\n    return (\n      <div>\n        <h4 className=\"text-sm font-medium mb-3\">Paraphrased Text</h4>\n        <div className=\"p-3 bg-blue-50 rounded-md\">\n          <p className=\"text-sm leading-relaxed\">{resultContent.paraphrased_text}</p>\n        </div>\n        {resultContent.options && resultContent.options.length > 0 && (\n          <div className=\"mt-3\">\n            <h5 className=\"text-xs font-medium text-gray-600 mb-2\">Alternative Options:</h5>\n            <div className=\"space-y-2\">\n              {resultContent.options.slice(0, 2).map((option: string, index: number) => (\n                <div key={index} className=\"p-2 bg-gray-50 rounded text-xs\">\n                  {option}\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  // Render Criteria Check result\n  const renderCriteriaResult = (resultContent: any) => {\n    if (!resultContent || !resultContent.matches) {\n      return <p className=\"text-gray-500\">No criteria analysis available.</p>;\n    }\n\n    return (\n      <div>\n        <h4 className=\"text-sm font-medium mb-3\">Criteria Analysis</h4>\n        <div className=\"space-y-3\">\n          {resultContent.matches.map((match: any, index: number) => (\n            <div key={index} className=\"p-3 bg-gray-50 rounded-md\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium\">{match.criteria}</span>\n                <span className={`px-2 py-1 rounded-full text-xs ${\n                  match.match_level === 'high'\n                    ? 'bg-green-100 text-green-700'\n                    : match.match_level === 'medium'\n                    ? 'bg-yellow-100 text-yellow-700'\n                    : 'bg-red-100 text-red-700'\n                }`}>\n                  {match.match_level}\n                </span>\n              </div>\n              <p className=\"text-xs text-gray-600\">{match.explanation}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  };\n\n  // Render result content based on feature type\n  const renderResultContent = (entry: HistoryEntry) => {\n    switch (entry.featureType) {\n      case 'ai-detector':\n        return renderAIDetectionResult(entry.resultContent);\n      case 'grammar-check':\n        return renderGrammarResult(entry.resultContent);\n      case 'paraphraser':\n        return renderParaphraseResult(entry.resultContent);\n      case 'criteria-check':\n        return renderCriteriaResult(entry.resultContent);\n      default:\n        return <p className=\"text-gray-500\">Unknown feature type.</p>;\n    }\n  };\n\n  if (error) {\n    return (\n      <div className=\"h-full flex items-center justify-center\">\n        <div className=\"text-center text-red-600\">\n          <FiAlertTriangle className=\"mx-auto mb-2\" size={24} />\n          <p className=\"font-medium\">Error</p>\n          <p className=\"text-sm\">{error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"h-full flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">Loading results...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (entries.length === 0) {\n    return (\n      <div className=\"h-full flex items-center justify-center\">\n        <div className=\"text-center text-gray-500\">\n          <FiFileText className=\"mx-auto mb-2\" size={24} />\n          <p>No results to display</p>\n          <p className=\"text-sm mt-2\">\n            Select an input or feature from the history to view results\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full overflow-y-auto p-4 space-y-4\">\n      <AnimatePresence>\n        {entries.map((entry) => (\n          <motion.div\n            key={entry.id}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\"\n          >\n            {/* Card Header */}\n            <div className=\"p-4 border-b border-gray-100\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <div className=\"mr-3\">\n                    {getFeatureIcon(entry.featureType)}\n                  </div>\n                  <div>\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getBadgeColor(entry.featureType)}`}>\n                      {getFeatureName(entry.featureType)}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"flex items-center text-xs text-gray-500\">\n                  <FiClock className=\"mr-1\" size={12} />\n                  {format(entry.timestamp, 'MMM d, h:mm a')}\n                </div>\n              </div>\n            </div>\n\n            {/* Card Content */}\n            <div className=\"p-4\">\n              {renderResultContent(entry)}\n            </div>\n          </motion.div>\n        ))}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default ResultCardsPanel;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAQA;AAZA;;;;;AAqBA,MAAM,mBAAoD,CAAC,EACzD,OAAO,EACP,YAAY,KAAK,EACjB,QAAQ,IAAI,EACb;IACC,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,WAAQ;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YACrD,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACnD,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,aAAU;oBAAC,WAAU;oBAAgB,MAAM;;;;;;YACrD,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YACpD;gBACE,qBAAO,6LAAC,iJAAA,CAAA,aAAU;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACvD;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,eAAe,qBAAO,6LAAC;YAAE,WAAU;sBAAgB;;;;;;QAExD,MAAM,eAAe,cAAc,QAAQ,GAAG;QAE9C,qBACE,6LAAC;;8BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsB;;;;;;sCACpC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAW,GAAG,eAAe,KAAK,iBAAiB,eAAe,KAAK,oBAAoB,kBAAkB;;oCAChH,aAAa,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;8BAK/B,6LAAC;oBAAI,WAAU;8BACZ,cAAc,SAAS,EAAE,IAAI,CAAC,UAAe;wBAC5C,MAAM,OAAO,SAAS,UAAU,KAAK,kBAAkB,SAAS,QAAQ,GAAG;wBAC3E,qBACE,6LAAC;4BAAiB,WAAW,OAAO,+BAA+B;;gCAChE,SAAS,IAAI;gCAAE;;2BADP;;;;;oBAIf;;;;;;8BAGF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAW,CAAC,OAAO,EAAE,eAAe,KAAK,eAAe,eAAe,KAAK,kBAAkB,gBAAgB;gCAC9G,OAAO;oCAAE,OAAO,GAAG,aAAa,CAAC,CAAC;gCAAC;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,8BAA8B;IAC9B,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,eAAe;YAClB,qBAAO,6LAAC;gBAAE,WAAU;0BAAgB;;;;;;QACtC;QAEA,gEAAgE;QAChE,MAAM,gBAAgB,cAAc,cAAc,IAAI,cAAc,YAAY,IAAI;QAEpF,qBACE,6LAAC;;8BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2B;;;;;;wBACxC,cAAc,WAAW,kBACxB,6LAAC;4BAAI,WAAU;;gCACZ,cAAc,WAAW,CAAC,MAAM;gCAAC;gCAAE,cAAc,WAAW,CAAC,MAAM,KAAK,IAAI,eAAe;gCAAc;;;;;;;;;;;;;8BAKhH,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAyC;;;;;;;;;;;;;;;;;IAI9D;IAEA,2BAA2B;IAC3B,MAAM,yBAAyB,CAAC;QAC9B,IAAI,CAAC,iBAAiB,CAAC,cAAc,gBAAgB,EAAE;YACrD,qBAAO,6LAAC;gBAAE,WAAU;0BAAgB;;;;;;QACtC;QAEA,qBACE,6LAAC;;8BACC,6LAAC;oBAAG,WAAU;8BAA2B;;;;;;8BACzC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAA2B,cAAc,gBAAgB;;;;;;;;;;;gBAEvE,cAAc,OAAO,IAAI,cAAc,OAAO,CAAC,MAAM,GAAG,mBACvD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAI,WAAU;sCACZ,cAAc,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAgB,sBACtD,6LAAC;oCAAgB,WAAU;8CACxB;mCADO;;;;;;;;;;;;;;;;;;;;;;IASxB;IAEA,+BAA+B;IAC/B,MAAM,uBAAuB,CAAC;QAC5B,IAAI,CAAC,iBAAiB,CAAC,cAAc,OAAO,EAAE;YAC5C,qBAAO,6LAAC;gBAAE,WAAU;0BAAgB;;;;;;QACtC;QAEA,qBACE,6LAAC;;8BACC,6LAAC;oBAAG,WAAU;8BAA2B;;;;;;8BACzC,6LAAC;oBAAI,WAAU;8BACZ,cAAc,OAAO,CAAC,GAAG,CAAC,CAAC,OAAY,sBACtC,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAuB,MAAM,QAAQ;;;;;;sDACrD,6LAAC;4CAAK,WAAW,CAAC,+BAA+B,EAC/C,MAAM,WAAW,KAAK,SAClB,gCACA,MAAM,WAAW,KAAK,WACtB,kCACA,2BACJ;sDACC,MAAM,WAAW;;;;;;;;;;;;8CAGtB,6LAAC;oCAAE,WAAU;8CAAyB,MAAM,WAAW;;;;;;;2BAb/C;;;;;;;;;;;;;;;;IAmBpB;IAEA,8CAA8C;IAC9C,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,MAAM,WAAW;YACvB,KAAK;gBACH,OAAO,wBAAwB,MAAM,aAAa;YACpD,KAAK;gBACH,OAAO,oBAAoB,MAAM,aAAa;YAChD,KAAK;gBACH,OAAO,uBAAuB,MAAM,aAAa;YACnD,KAAK;gBACH,OAAO,qBAAqB,MAAM,aAAa;YACjD;gBACE,qBAAO,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;QACxC;IACF;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,kBAAe;wBAAC,WAAU;wBAAe,MAAM;;;;;;kCAChD,6LAAC;wBAAE,WAAU;kCAAc;;;;;;kCAC3B,6LAAC;wBAAE,WAAU;kCAAW;;;;;;;;;;;;;;;;;IAIhC;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,aAAU;wBAAC,WAAU;wBAAe,MAAM;;;;;;kCAC3C,6LAAC;kCAAE;;;;;;kCACH,6LAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;;;;;;IAMpC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;sBACb,QAAQ,GAAG,CAAC,CAAC,sBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,eAAe,MAAM,WAAW;;;;;;0DAEnC,6LAAC;0DACC,cAAA,6LAAC;oDAAK,WAAW,CAAC,2EAA2E,EAAE,cAAc,MAAM,WAAW,GAAG;8DAC9H,eAAe,MAAM,WAAW;;;;;;;;;;;;;;;;;kDAIvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,UAAO;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAC/B,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,SAAS,EAAE;;;;;;;;;;;;;;;;;;sCAM/B,6LAAC;4BAAI,WAAU;sCACZ,oBAAoB;;;;;;;mBA5BlB,MAAM,EAAE;;;;;;;;;;;;;;;AAmCzB;KArRM;uCAuRS", "debugId": null}}, {"offset": {"line": 3322, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Scholarar/V2_mentor/frontend/app/components/text-analyzer/AiDetectorResultPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaLightbulb, FaShieldAlt, FaExclamationTriangle } from 'react-icons/fa';\nimport { ThumbsUpIcon, ThumbsDownIcon, TrendingUpIcon, TrendingDownIcon } from 'lucide-react';\n\ninterface AISentence {\n  ai_score: number;\n  prediction: 'original' | 'ai-generated';\n  text: string;\n}\n\ninterface AIDetectionResult {\n  ai_score: number;\n  classification: string;\n  human_score: number;\n  sentences: AISentence[];\n}\n\ninterface ResultPanelProps {\n  activeTab: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser';\n  isAnalyzing: boolean;\n  text: string;\n  onAnalyze: () => void;\n  aiDetectionResult?: AIDetectionResult | null;\n  error?: string | null;\n}\n\nconst ProgressBar = ({ label, percentage, color }: { label: string; percentage: number; color: string }) => (\n  <div>\n    <div className=\"flex justify-between text-sm font-medium text-gray-700 mb-2\">\n      <span>{label}</span>\n      <span>{percentage.toFixed(1)}%</span>\n    </div>\n    <div className=\"h-2 bg-gray-200 rounded-full overflow-hidden\">\n      <div className={`h-full ${color} transition-all duration-500`} style={{ width: `${percentage}%` }} />\n    </div>\n  </div>\n);\n\nconst ResultPanel: React.FC<ResultPanelProps> = ({\n  activeTab,\n  isAnalyzing,\n  text,\n  onAnalyze,\n  aiDetectionResult = null,\n  error = null,\n}) => {\n  const [feedbackSubmitted, setFeedbackSubmitted] = React.useState(false);\n\n  const handleFeedback = (positive: boolean) => {\n    setFeedbackSubmitted(true);\n    console.log(`Feedback: ${positive ? \"positive\" : \"negative\"}`);\n  };\n\n  // Calculate statistics for AI analysis\n  const sentences = aiDetectionResult?.sentences || [];\n  const aiSegments = sentences.filter((s) => s.prediction === 'ai-generated').length;\n  const humanSegments = sentences.length - aiSegments;\n  const total = sentences.length;\n  const humanPercentage = aiDetectionResult?.human_score || 0;\n  const aiPercentage = aiDetectionResult?.ai_score || 0;\n\n  const renderAIDetectorResult = () => {\n    if (isAnalyzing) {\n      return (\n        <div className=\"flex items-center justify-center h-64\">\n          <FaSpinner className=\"animate-spin text-3xl text-gray-500\" />\n          <span className=\"ml-3 text-gray-600\">Analyzing text...</span>\n        </div>\n      );\n    }\n\n    if (error) {\n      return (\n        <div className=\"text-red-600 p-4 bg-red-50 rounded-lg border border-red-200\">\n          <h4 className=\"font-medium mb-2\">Analysis Error</h4>\n          <p>{error}</p>\n        </div>\n      );\n    }\n\n    if (!sentences.length) {\n      return (\n        <div className=\"text-center text-gray-500 py-8\">\n          <FaLightbulb className=\"text-4xl mx-auto mb-4 text-gray-400\" />\n          <p className=\"text-lg font-medium mb-2\">No Analysis Yet</p>\n          <p>Click \"Analyze\" to detect AI-generated content</p>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"space-y-6\">\n        {/* Overall Score with Classification */}\n        <div className=\"text-center bg-gradient-to-br from-indigo-50 to-blue-50 rounded-xl p-6 border border-indigo-100\">\n          <div className=\"flex items-center justify-center mb-4\">\n            {aiDetectionResult?.classification === 'Human-Written' ? (\n              <FaShieldAlt className=\"text-3xl text-green-600 mr-3\" />\n            ) : (\n              <FaExclamationTriangle className=\"text-3xl text-red-600 mr-3\" />\n            )}\n            <div>\n              <div className=\"text-4xl font-bold text-gray-800\">\n                {aiPercentage.toFixed(1)}%\n              </div>\n              <div className=\"text-sm text-gray-600 font-medium\">AI Probability</div>\n            </div>\n          </div>\n\n          <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium mb-3 ${\n            aiDetectionResult?.classification === 'Human-Written'\n              ? 'bg-green-100 text-green-800'\n              : 'bg-red-100 text-red-800'\n          }`}>\n            {aiDetectionResult?.classification || 'Unknown'}\n          </div>\n\n          {/* Confidence Indicator */}\n          <div className=\"text-xs text-gray-500\">\n            Confidence: {aiPercentage < 10 ? 'Very High' : aiPercentage < 30 ? 'High' : aiPercentage < 70 ? 'Medium' : 'Low'}\n            {aiPercentage < 10 && ' - Highly likely to be human-written'}\n            {aiPercentage >= 70 && ' - Requires manual review'}\n          </div>\n        </div>\n\n        {/* Progress Bars */}\n        <div className=\"space-y-4\">\n          <ProgressBar label=\"Human Content\" percentage={humanPercentage} color=\"bg-teal-500\" />\n          <ProgressBar label=\"AI Content\" percentage={aiPercentage} color=\"bg-yellow-500\" />\n        </div>\n\n        {/* Summary Statistics */}\n        <div className=\"bg-gray-50 rounded-lg p-4 border\">\n          <h4 className=\"text-sm font-medium text-gray-700 mb-3\">Analysis Summary</h4>\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\n            <div className=\"space-y-2\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Total Sentences:</span>\n                <span className=\"font-medium\">{sentences.length}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Human-Written:</span>\n                <span className=\"font-medium text-teal-600\">\n                  {sentences.filter(s => s.prediction === 'original').length}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">AI-Generated:</span>\n                <span className=\"font-medium text-red-600\">\n                  {sentences.filter(s => s.prediction === 'ai-generated').length}\n                </span>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Avg AI Score:</span>\n                <span className=\"font-medium\">\n                  {sentences.length > 0 ?\n                    (sentences.reduce((sum, s) => sum + s.ai_score, 0) / sentences.length).toFixed(2) + '%'\n                    : '0%'}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Highest Score:</span>\n                <span className=\"font-medium\">\n                  {sentences.length > 0 ?\n                    Math.max(...sentences.map(s => s.ai_score)).toFixed(2) + '%'\n                    : '0%'}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Risk Level:</span>\n                <span className={`font-medium ${\n                  aiPercentage < 10 ? 'text-green-600' :\n                  aiPercentage < 30 ? 'text-yellow-600' :\n                  aiPercentage < 70 ? 'text-orange-600' : 'text-red-600'\n                }`}>\n                  {aiPercentage < 10 ? 'Very Low' :\n                   aiPercentage < 30 ? 'Low' :\n                   aiPercentage < 70 ? 'Medium' : 'High'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Sentence-by-Sentence Analysis */}\n        <div className=\"border-t pt-4\">\n          <h3 className=\"text-sm font-medium text-gray-700 mb-3 flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <FaLightbulb className=\"mr-2 text-indigo-500\" />\n              Sentence Analysis ({sentences.length} sentences)\n            </div>\n            <div className=\"text-xs text-gray-500\">\n              {sentences.filter(s => s.prediction === 'ai-generated').length} flagged\n            </div>\n          </h3>\n          <div className=\"space-y-3 max-h-64 overflow-y-auto\">\n            {sentences.map((sentence, index) => {\n              const isAI = sentence.prediction === 'ai-generated';\n              const scorePercentage = sentence.ai_score;\n\n              // Determine confidence level based on score (0-100 scale)\n              const getConfidenceLevel = (score: number) => {\n                if (score < 0.001) return 'Extremely Low';\n                if (score < 0.1) return 'Very Low';\n                if (score < 1) return 'Low';\n                if (score < 10) return 'Medium';\n                if (score < 50) return 'High';\n                return 'Very High';\n              };\n\n              const confidenceLevel = getConfidenceLevel(scorePercentage);\n\n              return (\n                <div key={index} className={`p-4 rounded-lg border transition-all hover:shadow-sm ${\n                  isAI ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'\n                }`}>\n                  <div className=\"flex items-start gap-3\">\n                    <div className={`w-4 h-4 rounded-full mt-1 flex-shrink-0 flex items-center justify-center ${\n                      isAI ? 'bg-red-500' : 'bg-teal-500'\n                    }`}>\n                      <span className=\"text-white text-xs font-bold\">{index + 1}</span>\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center gap-2 mb-2\">\n                        <span className=\"font-medium text-gray-800\">\n                          {scorePercentage < 0.001 ? '<0.001' :\n                           scorePercentage < 0.1 ? scorePercentage.toFixed(3) :\n                           scorePercentage.toFixed(1)}%\n                        </span>\n                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${\n                          isAI ? 'bg-red-100 text-red-700' : 'bg-teal-100 text-teal-700'\n                        }`}>\n                          {isAI ? 'AI-Generated' : 'Human-Written'}\n                        </span>\n                        <span className=\"text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600\">\n                          {confidenceLevel} Risk\n                        </span>\n                      </div>\n                      <p className=\"text-gray-700 leading-relaxed mb-2\">\n                        \"{sentence.text}\"\n                      </p>\n                      {scorePercentage < 0.001 && (\n                        <div className=\"text-xs text-green-600 bg-green-100 px-2 py-1 rounded\">\n                          ✓ Extremely low AI probability - Strong human writing indicators\n                        </div>\n                      )}\n                      {isAI && (\n                        <div className=\"text-xs text-red-600 bg-red-100 px-2 py-1 rounded\">\n                          ⚠ Flagged for potential AI generation - Manual review recommended\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Feedback Section */}\n        <div className=\"border-t pt-4\">\n          <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Was this analysis helpful?</h3>\n          <div className=\"flex gap-3\">\n            <button\n              onClick={() => handleFeedback(true)}\n              disabled={feedbackSubmitted}\n              className=\"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:bg-gray-400 transition-all\"\n            >\n              <ThumbsUpIcon className=\"w-4 h-4\" /> Yes\n            </button>\n            <button\n              onClick={() => handleFeedback(false)}\n              disabled={feedbackSubmitted}\n              className=\"flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:bg-gray-400 transition-all\"\n            >\n              <ThumbsDownIcon className=\"w-4 h-4\" /> No\n            </button>\n          </div>\n          {feedbackSubmitted && (\n            <p className=\"text-sm text-teal-600 mt-3 font-medium\">✓ Thanks for your feedback!</p>\n          )}\n        </div>\n      </div>\n    );\n  };\n\n  const renderOtherFeatures = () => {\n    return (\n      <div className=\"text-center text-gray-500 py-8\">\n        <FaLightbulb className=\"text-4xl mx-auto mb-4 text-gray-400\" />\n        <p className=\"text-lg font-medium mb-2\">Select a Feature</p>\n        <p>Choose {activeTab.replace('-', ' ')} and click \"Analyze Text\" to see results</p>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-md p-6 h-full overflow-y-auto\">\n      {/* Show Analyze button for all features except AI detector */}\n      {activeTab !== 'ai-detector' && (\n        <div className=\"mb-6\">\n          <button\n            onClick={onAnalyze}\n            disabled={isAnalyzing || text.trim() === ''}\n            className=\"w-full flex items-center justify-center gap-2 bg-indigo-600 text-white px-4 py-3 rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 transition-all font-medium\"\n          >\n            <FaSpinner className={isAnalyzing ? \"animate-spin\" : \"hidden\"} />\n            {isAnalyzing ? \"Analyzing...\" : \"Analyze Text\"}\n          </button>\n        </div>\n      )}\n\n      <div className=\"flex justify-between items-center mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-800\">\n          {activeTab === 'ai-detector' ? 'AI Detection Results' :\n           activeTab === 'grammar-check' ? 'Grammar Check Results' :\n           activeTab === 'paraphraser' ? 'Paraphrasing Results' :\n           activeTab === 'criteria-check' ? 'Criteria Check Results' : 'Analysis Results'}\n        </h2>\n        {/* Show Analyze button only for AI detector in header */}\n        {activeTab === 'ai-detector' && (\n          <button\n            onClick={onAnalyze}\n            disabled={isAnalyzing || text.trim() === ''}\n            className=\"flex items-center gap-2 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 transition-all\"\n          >\n            <FaSpinner className={isAnalyzing ? \"animate-spin\" : \"hidden\"} />\n            {isAnalyzing ? \"Analyzing...\" : \"Analyze\"}\n          </button>\n        )}\n      </div>\n\n      <motion.div\n        key={activeTab}\n        initial={{ opacity: 0, y: 10 }}\n        animate={{ opacity: 1, y: 0 }}\n        exit={{ opacity: 0, y: -10 }}\n        transition={{ duration: 0.3 }}\n        className=\"h-full\"\n      >\n        {activeTab === 'ai-detector' ? renderAIDetectorResult() : renderOtherFeatures()}\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ResultPanel;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AA6BA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAwD,iBACrG,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAM;;;;;;kCACP,6LAAC;;4BAAM,WAAW,OAAO,CAAC;4BAAG;;;;;;;;;;;;;0BAE/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,CAAC,OAAO,EAAE,MAAM,4BAA4B,CAAC;oBAAE,OAAO;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;KAPhG;AAYN,MAAM,cAA0C,CAAC,EAC/C,SAAS,EACT,WAAW,EACX,IAAI,EACJ,SAAS,EACT,oBAAoB,IAAI,EACxB,QAAQ,IAAI,EACb;;IACC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEjE,MAAM,iBAAiB,CAAC;QACtB,qBAAqB;QACrB,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,aAAa,YAAY;IAC/D;IAEA,uCAAuC;IACvC,MAAM,YAAY,mBAAmB,aAAa,EAAE;IACpD,MAAM,aAAa,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,UAAU,KAAK,gBAAgB,MAAM;IAClF,MAAM,gBAAgB,UAAU,MAAM,GAAG;IACzC,MAAM,QAAQ,UAAU,MAAM;IAC9B,MAAM,kBAAkB,mBAAmB,eAAe;IAC1D,MAAM,eAAe,mBAAmB,YAAY;IAEpD,MAAM,yBAAyB;QAC7B,IAAI,aAAa;YACf,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAK,WAAU;kCAAqB;;;;;;;;;;;;QAG3C;QAEA,IAAI,OAAO;YACT,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmB;;;;;;kCACjC,6LAAC;kCAAG;;;;;;;;;;;;QAGV;QAEA,IAAI,CAAC,UAAU,MAAM,EAAE;YACrB,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAE,WAAU;kCAA2B;;;;;;kCACxC,6LAAC;kCAAE;;;;;;;;;;;;QAGT;QAEA,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,mBAAmB,mBAAmB,gCACrC,6LAAC,iJAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,6LAAC,iJAAA,CAAA,wBAAqB;oCAAC,WAAU;;;;;;8CAEnC,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;;gDACZ,aAAa,OAAO,CAAC;gDAAG;;;;;;;sDAE3B,6LAAC;4CAAI,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAIvD,6LAAC;4BAAI,WAAW,CAAC,yEAAyE,EACxF,mBAAmB,mBAAmB,kBAClC,gCACA,2BACJ;sCACC,mBAAmB,kBAAkB;;;;;;sCAIxC,6LAAC;4BAAI,WAAU;;gCAAwB;gCACxB,eAAe,KAAK,cAAc,eAAe,KAAK,SAAS,eAAe,KAAK,WAAW;gCAC1G,eAAe,MAAM;gCACrB,gBAAgB,MAAM;;;;;;;;;;;;;8BAK3B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAY,OAAM;4BAAgB,YAAY;4BAAiB,OAAM;;;;;;sCACtE,6LAAC;4BAAY,OAAM;4BAAa,YAAY;4BAAc,OAAM;;;;;;;;;;;;8BAIlE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DAAe,UAAU,MAAM;;;;;;;;;;;;sDAEjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DACb,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,YAAY,MAAM;;;;;;;;;;;;sDAG9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DACb,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;8CAIpE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DACb,UAAU,MAAM,GAAG,IAClB,CAAC,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,CAAC,KAAK,MAClF;;;;;;;;;;;;sDAGR,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DACb,UAAU,MAAM,GAAG,IAClB,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG,OAAO,CAAC,KAAK,MACvD;;;;;;;;;;;;sDAGR,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAW,CAAC,YAAY,EAC5B,eAAe,KAAK,mBACpB,eAAe,KAAK,oBACpB,eAAe,KAAK,oBAAoB,gBACxC;8DACC,eAAe,KAAK,aACpB,eAAe,KAAK,QACpB,eAAe,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAyB;wCAC5B,UAAU,MAAM;wCAAC;;;;;;;8CAEvC,6LAAC;oCAAI,WAAU;;wCACZ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,gBAAgB,MAAM;wCAAC;;;;;;;;;;;;;sCAGnE,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,UAAU;gCACxB,MAAM,OAAO,SAAS,UAAU,KAAK;gCACrC,MAAM,kBAAkB,SAAS,QAAQ;gCAEzC,0DAA0D;gCAC1D,MAAM,qBAAqB,CAAC;oCAC1B,IAAI,QAAQ,OAAO,OAAO;oCAC1B,IAAI,QAAQ,KAAK,OAAO;oCACxB,IAAI,QAAQ,GAAG,OAAO;oCACtB,IAAI,QAAQ,IAAI,OAAO;oCACvB,IAAI,QAAQ,IAAI,OAAO;oCACvB,OAAO;gCACT;gCAEA,MAAM,kBAAkB,mBAAmB;gCAE3C,qBACE,6LAAC;oCAAgB,WAAW,CAAC,qDAAqD,EAChF,OAAO,6BAA6B,gCACpC;8CACA,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,yEAAyE,EACxF,OAAO,eAAe,eACtB;0DACA,cAAA,6LAAC;oDAAK,WAAU;8DAAgC,QAAQ;;;;;;;;;;;0DAE1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEACb,kBAAkB,QAAQ,WAC1B,kBAAkB,MAAM,gBAAgB,OAAO,CAAC,KAChD,gBAAgB,OAAO,CAAC;oEAAG;;;;;;;0EAE9B,6LAAC;gEAAK,WAAW,CAAC,2CAA2C,EAC3D,OAAO,4BAA4B,6BACnC;0EACC,OAAO,iBAAiB;;;;;;0EAE3B,6LAAC;gEAAK,WAAU;;oEACb;oEAAgB;;;;;;;;;;;;;kEAGrB,6LAAC;wDAAE,WAAU;;4DAAqC;4DAC9C,SAAS,IAAI;4DAAC;;;;;;;oDAEjB,kBAAkB,uBACjB,6LAAC;wDAAI,WAAU;kEAAwD;;;;;;oDAIxE,sBACC,6LAAC;wDAAI,WAAU;kEAAoD;;;;;;;;;;;;;;;;;;mCAlCjE;;;;;4BA0Cd;;;;;;;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,UAAU;oCACV,WAAU;;sDAEV,6LAAC,qNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAEtC,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,UAAU;oCACV,WAAU;;sDAEV,6LAAC,yNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;wBAGzC,mCACC,6LAAC;4BAAE,WAAU;sCAAyC;;;;;;;;;;;;;;;;;;IAKhE;IAEA,MAAM,sBAAsB;QAC1B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,iJAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAE,WAAU;8BAA2B;;;;;;8BACxC,6LAAC;;wBAAE;wBAAQ,UAAU,OAAO,CAAC,KAAK;wBAAK;;;;;;;;;;;;;IAG7C;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,cAAc,+BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,UAAU,eAAe,KAAK,IAAI,OAAO;oBACzC,WAAU;;sCAEV,6LAAC,iJAAA,CAAA,YAAS;4BAAC,WAAW,cAAc,iBAAiB;;;;;;wBACpD,cAAc,iBAAiB;;;;;;;;;;;;0BAKtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,cAAc,gBAAgB,yBAC9B,cAAc,kBAAkB,0BAChC,cAAc,gBAAgB,yBAC9B,cAAc,mBAAmB,2BAA2B;;;;;;oBAG9D,cAAc,+BACb,6LAAC;wBACC,SAAS;wBACT,UAAU,eAAe,KAAK,IAAI,OAAO;wBACzC,WAAU;;0CAEV,6LAAC,iJAAA,CAAA,YAAS;gCAAC,WAAW,cAAc,iBAAiB;;;;;;4BACpD,cAAc,iBAAiB;;;;;;;;;;;;;0BAKtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC3B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAET,cAAc,gBAAgB,2BAA2B;eAPrD;;;;;;;;;;;AAWb;GAnTM;MAAA;uCAqTS", "debugId": null}}, {"offset": {"line": 4195, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Scholarar/V2_mentor/frontend/app/components/text-analyzer/Paraphraser.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport axios from 'axios';\nimport { FiRefreshCw, FiCopy, FiX, FiMaximize2, FiMinimize2 } from 'react-icons/fi';\n\ninterface ParaphrasingComponentProps {\n  text: string;\n  onParaphrased?: (paraphrasedText: string) => void;\n}\n\nconst API_BASE_URL = 'http://localhost:5000'; // Update this to your Flask API URL\n\nconst Paraphraser: React.FC<ParaphrasingComponentProps> = ({ text, onParaphrased }) => {\n  // State for paraphrasing options\n  const [paraphraseStyle, setParaphraseStyle] = useState<string>('standard');\n  const [synonymLevel, setSynonymLevel] = useState<number>(2); // 0-4 scale\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [copied, setCopied] = useState(false);\n  const [isFullScreen, setIsFullScreen] = useState(false);\n  const [hasOverflow, setHasOverflow] = useState(false);\n\n  // State for sentence selection and options\n  const [selectedSentence, setSelectedSentence] = useState<string | null>(null);\n  const [showSentenceOptions, setShowSentenceOptions] = useState<boolean>(false);\n  const [sentenceOptions, setSentenceOptions] = useState<string[]>([]);\n  const [sentenceSelectionCoords, setSentenceSelectionCoords] = useState<{top: number, left: number} | null>(null);\n\n  // Ref for the options popup\n  const optionsPopupRef = useRef<HTMLDivElement>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // Close options popup when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (optionsPopupRef.current && !optionsPopupRef.current.contains(event.target as Node)) {\n        setShowSentenceOptions(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  // State for tracking the original text and modified text\n  const [modifiedText, setModifiedText] = useState<string>(text);\n  const textContainerRef = useRef<HTMLDivElement>(null);\n\n  // Update modifiedText when text prop changes\n  useEffect(() => {\n    setModifiedText(text);\n  }, [text]);\n\n  // Check for overflow when text changes\n  useEffect(() => {\n    const checkForOverflow = () => {\n      if (textContainerRef.current) {\n        const hasVerticalOverflow = textContainerRef.current.scrollHeight > textContainerRef.current.clientHeight;\n        setHasOverflow(hasVerticalOverflow);\n      }\n    };\n\n    checkForOverflow();\n    // Add resize listener to recheck on window resize\n    window.addEventListener('resize', checkForOverflow);\n    return () => window.removeEventListener('resize', checkForOverflow);\n  }, [modifiedText]);\n\n  // Function to generate paraphrase options for a sentence\n\n  const generateParaphraseOptions = async (sentence: string) => {\n    try {\n      setIsLoading(true);\n      setShowSentenceOptions(true);\n      const response = await axios.post(`${API_BASE_URL}/paraphrasing`, {\n        \n        text: sentence,\n        tone: paraphraseStyle,\n        synonym_level: synonymLevel,\n      });\n      console.log('Generating paraphrase options for sentence:', response.data);\n\n      if (response.status === 200) {\n        const { paraphrases } = response.data;\n        if (paraphrases && paraphrases.length > 0) {\n          const options = paraphrases[0].versions.map((version: any) => version.text);\n          setSentenceOptions(options);\n        } else {\n          throw new Error('No paraphrases returned');\n        }\n      } else {\n        throw new Error('API request failed');\n      }\n    } catch (error: any) {\n      console.error('Error generating paraphrase options:', error);\n      setError(error.response?.data?.error || 'Failed to generate paraphrase options');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Function to apply a paraphrase option to the text\n  const applyParaphraseOption = (originalSentence: string, newSentence: string) => {\n    try {\n      // Use regex to find the exact sentence with its punctuation\n      const escapedSentence = originalSentence.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n      const sentenceRegex = new RegExp(`(${escapedSentence})`, 'g');\n      const updatedText = modifiedText.replace(sentenceRegex, newSentence);\n\n      if (updatedText === modifiedText) {\n        console.warn('No changes were made to the text. The sentence might not have been found exactly.');\n        console.log('Original sentence:', originalSentence);\n        console.log('New sentence:', newSentence);\n        console.log('Text:', modifiedText);\n      } else {\n        setModifiedText(updatedText);\n        if (onParaphrased) {\n          onParaphrased(updatedText);\n        }\n      }\n    } catch (error) {\n      console.error('Error applying paraphrase option:', error);\n      setError('Failed to apply paraphrase option');\n    }\n  };\n\n  // Function to copy text to clipboard\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(modifiedText); // Changed to copy modifiedText\n    setCopied(true);\n    setTimeout(() => setCopied(false), 2000);\n  };\n\n  return (\n    <div ref={containerRef} className=\"h-full overflow-hidden flex flex-col\">\n      {/* Top mode tabs */}\n      <div className=\"border-b border-gray-200 mb-2\">\n        <div className=\"flex flex-wrap\">\n          {['Standard', 'Fluency', 'Creative', 'Expand', 'Shorten', 'Custom'].map((mode) => (\n            <button\n              key={mode}\n              onClick={() => setParaphraseStyle(mode.toLowerCase())}\n              className={`px-4 py-2 text-sm transition-all ${\n                paraphraseStyle === mode.toLowerCase()\n                  ? 'text-blue-600 font-medium border-b-2 border-blue-600'\n                  : 'text-gray-600 hover:text-gray-800'\n              }`}\n            >\n              {mode}\n            </button>\n          ))}\n\n          <div className=\"ml-auto flex items-center px-4\">\n            <span className=\"text-sm text-gray-700 mr-2\">Synonyms:</span>\n            <div className=\"relative w-32 h-4 bg-gray-200 rounded-full\">\n              <div\n                className=\"absolute top-0 left-0 h-full bg-blue-600 rounded-full\"\n                style={{ width: `${(synonymLevel / 4) * 100}%` }}\n              ></div>\n              <div\n                className=\"absolute top-0 right-0 w-4 h-4 bg-white border-2 border-blue-600 rounded-full shadow-md cursor-pointer\"\n                style={{ right: `${100 - (synonymLevel / 4) * 100}%`, transform: 'translateX(50%)' }}\n                onMouseDown={(e) => {\n                  const slider = e.currentTarget.parentElement;\n                  if (!slider) return;\n\n                  const handleMouseMove = (e: MouseEvent) => {\n                    if (!slider) return;\n                    const rect = slider.getBoundingClientRect();\n                    const x = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));\n                    setSynonymLevel(Math.round(x * 4));\n                  };\n\n                  const handleMouseUp = () => {\n                    document.removeEventListener('mousemove', handleMouseMove);\n                    document.removeEventListener('mouseup', handleMouseUp);\n                  };\n\n                  document.addEventListener('mousemove', handleMouseMove);\n                  document.addEventListener('mouseup', handleMouseUp);\n                }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Rephrase button and controls */}\n      <div className=\"mb-2 flex items-center\">\n        <button\n          onClick={async () => {\n            if (!text.trim()) return;\n\n            setIsLoading(true);\n            setError(null);\n            try {\n              const response = await axios.post(`${API_BASE_URL}/paraphrasing`, {\n                text,\n                tone: paraphraseStyle,\n                synonym_level: synonymLevel,\n              });\n\n              if (response.status === 200) {\n                const { paraphrases } = response.data;\n                if (paraphrases && paraphrases.length > 0) {\n                  // Combine the first version of each paraphrased sentence\n                  const paraphrasedText = paraphrases\n                    .map((para: any) => para.versions[0]?.text || para.sentence)\n                    .join(' ');\n                  setModifiedText(paraphrasedText);\n                  if (onParaphrased) {\n                    onParaphrased(paraphrasedText);\n                  }\n                } else {\n                  throw new Error('No paraphrases returned');\n                }\n              } else {\n                throw new Error('API request failed');\n              }\n            } catch (error: any) {\n              console.error('Error paraphrasing text:', error);\n              setError(error.response?.data?.error || 'Failed to paraphrase text. Please try again.');\n            } finally {\n              setIsLoading(false);\n            }\n          }}\n          disabled={isLoading || !text.trim()}\n          className={`px-4 py-1.5 rounded-md text-sm font-medium ${\n            isLoading || !text.trim()\n              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n              : 'bg-blue-600 text-white hover:bg-blue-700 transition-colors'\n          }`}\n        >\n          {isLoading ? (\n            <span className=\"flex items-center\">\n              <FiRefreshCw className=\"animate-spin mr-2\" size={14} />\n              Rephrasing...\n            </span>\n          ) : (\n            'Rephrase'\n          )}\n        </button>\n\n        <div className=\"flex ml-4 space-x-2\">\n          <button className=\"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded\">\n            <FiRefreshCw size={16} />\n          </button>\n          <button className=\"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded\" onClick={copyToClipboard}>\n            <FiCopy size={16} />\n          </button>\n        </div>\n\n        <button\n          onClick={() => setIsFullScreen(!isFullScreen)}\n          className=\"ml-auto p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded\"\n          title={isFullScreen ? \"Exit full screen\" : \"Full screen\"}\n        >\n          {isFullScreen ? <FiMinimize2 size={16} /> : <FiMaximize2 size={16} />}\n        </button>\n      </div>\n\n      {/* Error message display */}\n      {error && (\n        <div className=\"mb-2 p-3 bg-red-50 text-red-700 rounded-md\">\n          <p className=\"text-sm\">{error}</p>\n        </div>\n      )}\n\n      {/* Main content area */}\n      <div className={`${isFullScreen ? 'fixed inset-0 z-50 bg-white p-6' : 'flex-1 w-full'} font-sans overflow-auto`}>\n        {/* Text display with sentence selection */}\n        <div className=\"bg-white rounded-lg border border-gray-200 p-4 relative flex-1\">\n          {hasOverflow && (\n            <div className=\"absolute top-2 right-2 z-10 text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded-md flex items-center\">\n              <span className=\"mr-1\">Scroll for more</span>\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                <path d=\"M12 5v14M5 12l7 7 7-7\"/>\n              </svg>\n            </div>\n          )}\n          <div\n            ref={textContainerRef}\n            className=\"text-gray-900 text-base h-full max-h-[calc(100vh-400px)] min-h-auto relative\"\n            style={{ height: 'calc(100vh - 180px)' }}\n          >\n            {hasOverflow && (\n              <div className=\"absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none\" />\n            )}\n\n            {/* Warning for very long text */}\n            {(() => {\n              const sentenceCount = modifiedText.split(/[.!?]+/).filter(Boolean).length;\n              if (sentenceCount > 100) {\n                return (\n                  <div className=\"absolute top-0 left-0 right-0 bg-yellow-50 text-yellow-800 text-xs p-2 border-b border-yellow-200\">\n                    This text is very long ({sentenceCount} sentences). Some sentences may not be displayed for performance reasons.\n                  </div>\n                );\n              }\n              return null;\n            })()}\n            {/* Split text into sentences for selection */}\n            <div className={`${modifiedText.split(/[.!?]+/).filter(Boolean).length > 100 ? 'pt-8' : ''}`}>\n              {(() => {\n                const sentences: {text: string, startIndex: number, endIndex: number}[] = [];\n                const regex = /[^.!?]+[.!?]+/g;\n                let match;\n\n                while ((match = regex.exec(modifiedText)) !== null) {\n                  sentences.push({\n                    text: match[0].trim(),\n                    startIndex: match.index,\n                    endIndex: match.index + match[0].length\n                  });\n                }\n\n                const maxSentences = 1000;\n                const hasTooManySentences = sentences.length > maxSentences;\n\n                if (hasTooManySentences) {\n                  console.warn(`Text has ${sentences.length} sentences, limiting to ${maxSentences} for performance.`);\n                }\n\n                const displaySentences = hasTooManySentences\n                  ? sentences.slice(0, maxSentences)\n                  : sentences;\n\n                return displaySentences.map((sentence, sentenceIndex) => {\n                  const isSelected = selectedSentence === sentence.text;\n\n                  return (\n                    <span\n                      key={sentenceIndex}\n                      className={`cursor-pointer py-1 px-0.5 rounded ${isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'}`}\n                      onClick={() => {\n                        setSentenceSelectionCoords({\n                          top: 0,\n                          left: 0\n                        });\n                        setSelectedSentence(sentence.text);\n                        generateParaphraseOptions(sentence.text);\n                      }}\n                    >\n                      {sentence.text.split(' ').map((word, wordIndex) => {\n                        const isChanged = (word.length > 3 && wordIndex % 3 === 0) ||\n                                         (word.length > 5 && wordIndex % 5 === 0);\n                        return (\n                          <span\n                            key={wordIndex}\n                            className={isChanged ? 'text-red-500 font-medium' : ''}\n                          >\n                            {word}{' '}\n                          </span>\n                        );\n                      })}\n                    </span>\n                  );\n                });\n              })()}\n            </div>\n          </div>\n\n          {/* Paraphrase options popup */}\n          {selectedSentence && sentenceSelectionCoords && showSentenceOptions && (\n            <div\n              ref={optionsPopupRef}\n              className=\"fixed bg-white shadow-xl rounded-md border border-gray-200 z-50 w-[90%] max-w-[500px]\"\n              style={{\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)'\n              }}\n            >\n              <button\n                onClick={() => setShowSentenceOptions(false)}\n                className=\"absolute top-2 right-2 text-gray-500 hover:text-gray-700 z-20\"\n              >\n                <FiX />\n              </button>\n\n              <div className=\"p-4 space-y-3 max-h-[300px] overflow-y-auto\">\n                {isLoading ? (\n                  <div className=\"flex justify-center items-center py-8\">\n                    <FiRefreshCw className=\"animate-spin mr-2\" size={20} />\n                    <span>Generating options...</span>\n                  </div>\n                ) : (\n                  sentenceOptions.map((option, index) => (\n                    <div\n                      key={index}\n                      className=\"p-3 text-sm border border-gray-200 rounded-md cursor-pointer hover:bg-blue-50\"\n                      onClick={() => {\n                        if (selectedSentence) {\n                          applyParaphraseOption(selectedSentence, option);\n                        }\n                        setShowSentenceOptions(false);\n                        setSelectedSentence(null);\n                      }}\n                    >\n                      {option.split(' ').map((word, wordIndex) => {\n                        const normalizedWord = word.replace(/[.,!?;:'\"()]/g, '').toLowerCase();\n                        const normalizedSentence = selectedSentence?.toLowerCase().replace(/[.,!?;:'\"()]/g, '');\n                        const isChanged = normalizedWord.length > 2 &&\n                                         normalizedSentence &&\n                                         !normalizedSentence.includes(normalizedWord);\n                        return (\n                          <span\n                            key={wordIndex}\n                            className={isChanged ? 'text-red-500 font-medium' : ''}\n                          >\n                            {word}{' '}\n                          </span>\n                        );\n                      })}\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Word/sentence count footer */}\n      <div className=\"mt-2 flex justify-between items-center text-sm text-gray-500 pt-2\">\n        <div className=\"flex items-center space-x-3\">\n          <span>\n            {modifiedText.split(/\\s+/).filter(Boolean).length} Words\n          </span>\n          <span>\n            {modifiedText.length} Characters\n          </span>\n          <span>\n            {modifiedText.split(/[.!?]+/).filter(Boolean).length} Sentences\n          </span>\n        </div>\n\n        <div className=\"flex items-center space-x-2\">\n          <button className=\"p-1 hover:bg-gray-100 rounded\">\n            <FiRefreshCw size={16} />\n          </button>\n          <button className=\"p-1 hover:bg-gray-100 rounded\" onClick={copyToClipboard}>\n            <FiCopy size={16} />\n          </button>\n        </div>\n      </div>\n\n      {/* Legend */}\n      <div className=\"mt-1 flex items-center justify-center space-x-6 text-xs text-gray-600\">\n        <div className=\"flex items-center\">\n          <span className=\"w-2 h-2 bg-red-500 rounded-full mr-1\"></span>\n          <span>Changed Words</span>\n        </div>\n        <div className=\"flex items-center\">\n          <span className=\"w-2 h-2 bg-yellow-500 rounded-full mr-1\"></span>\n          <span>Structural Changes</span>\n        </div>\n        <div className=\"flex items-center\">\n          <span className=\"w-2 h-2 bg-blue-500 rounded-full mr-1\"></span>\n          <span>Longest Unchanged Words</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Paraphraser;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWA,MAAM,eAAe,yBAAyB,oCAAoC;AAElF,MAAM,cAAoD,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE;;IAChF,iCAAiC;IACjC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,IAAI,YAAY;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2CAA2C;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsC;IAE3G,4BAA4B;IAC5B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,IAAI,gBAAgB,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBACtF,uBAAuB;oBACzB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;yCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;gCAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEhD,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,gBAAgB;QAClB;gCAAG;QAAC;KAAK;IAET,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;0DAAmB;oBACvB,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,MAAM,sBAAsB,iBAAiB,OAAO,CAAC,YAAY,GAAG,iBAAiB,OAAO,CAAC,YAAY;wBACzG,eAAe;oBACjB;gBACF;;YAEA;YACA,kDAAkD;YAClD,OAAO,gBAAgB,CAAC,UAAU;YAClC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;gCAAG;QAAC;KAAa;IAEjB,yDAAyD;IAEzD,MAAM,4BAA4B,OAAO;QACvC,IAAI;YACF,aAAa;YACb,uBAAuB;YACvB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,aAAa,aAAa,CAAC,EAAE;gBAEhE,MAAM;gBACN,MAAM;gBACN,eAAe;YACjB;YACA,QAAQ,GAAG,CAAC,+CAA+C,SAAS,IAAI;YAExE,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,MAAM,EAAE,WAAW,EAAE,GAAG,SAAS,IAAI;gBACrC,IAAI,eAAe,YAAY,MAAM,GAAG,GAAG;oBACzC,MAAM,UAAU,WAAW,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,UAAiB,QAAQ,IAAI;oBAC1E,mBAAmB;gBACrB,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wCAAwC;YACtD,SAAS,MAAM,QAAQ,EAAE,MAAM,SAAS;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,oDAAoD;IACpD,MAAM,wBAAwB,CAAC,kBAA0B;QACvD,IAAI;YACF,4DAA4D;YAC5D,MAAM,kBAAkB,iBAAiB,OAAO,CAAC,uBAAuB;YACxE,MAAM,gBAAgB,IAAI,OAAO,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE;YACzD,MAAM,cAAc,aAAa,OAAO,CAAC,eAAe;YAExD,IAAI,gBAAgB,cAAc;gBAChC,QAAQ,IAAI,CAAC;gBACb,QAAQ,GAAG,CAAC,sBAAsB;gBAClC,QAAQ,GAAG,CAAC,iBAAiB;gBAC7B,QAAQ,GAAG,CAAC,SAAS;YACvB,OAAO;gBACL,gBAAgB;gBAChB,IAAI,eAAe;oBACjB,cAAc;gBAChB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,SAAS;QACX;IACF;IAEA,qCAAqC;IACrC,MAAM,kBAAkB;QACtB,UAAU,SAAS,CAAC,SAAS,CAAC,eAAe,+BAA+B;QAC5E,UAAU;QACV,WAAW,IAAM,UAAU,QAAQ;IACrC;IAEA,qBACE,6LAAC;QAAI,KAAK;QAAc,WAAU;;0BAEhC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ;4BAAC;4BAAY;4BAAW;4BAAY;4BAAU;4BAAW;yBAAS,CAAC,GAAG,CAAC,CAAC,qBACvE,6LAAC;gCAEC,SAAS,IAAM,mBAAmB,KAAK,WAAW;gCAClD,WAAW,CAAC,iCAAiC,EAC3C,oBAAoB,KAAK,WAAW,KAChC,yDACA,qCACJ;0CAED;+BARI;;;;;sCAYT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAA6B;;;;;;8CAC7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,AAAC,eAAe,IAAK,IAAI,CAAC,CAAC;4CAAC;;;;;;sDAEjD,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,MAAM,AAAC,eAAe,IAAK,IAAI,CAAC,CAAC;gDAAE,WAAW;4CAAkB;4CACnF,aAAa,CAAC;gDACZ,MAAM,SAAS,EAAE,aAAa,CAAC,aAAa;gDAC5C,IAAI,CAAC,QAAQ;gDAEb,MAAM,kBAAkB,CAAC;oDACvB,IAAI,CAAC,QAAQ;oDACb,MAAM,OAAO,OAAO,qBAAqB;oDACzC,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK;oDACtE,gBAAgB,KAAK,KAAK,CAAC,IAAI;gDACjC;gDAEA,MAAM,gBAAgB;oDACpB,SAAS,mBAAmB,CAAC,aAAa;oDAC1C,SAAS,mBAAmB,CAAC,WAAW;gDAC1C;gDAEA,SAAS,gBAAgB,CAAC,aAAa;gDACvC,SAAS,gBAAgB,CAAC,WAAW;4CACvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;4BACP,IAAI,CAAC,KAAK,IAAI,IAAI;4BAElB,aAAa;4BACb,SAAS;4BACT,IAAI;gCACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,aAAa,aAAa,CAAC,EAAE;oCAChE;oCACA,MAAM;oCACN,eAAe;gCACjB;gCAEA,IAAI,SAAS,MAAM,KAAK,KAAK;oCAC3B,MAAM,EAAE,WAAW,EAAE,GAAG,SAAS,IAAI;oCACrC,IAAI,eAAe,YAAY,MAAM,GAAG,GAAG;wCACzC,yDAAyD;wCACzD,MAAM,kBAAkB,YACrB,GAAG,CAAC,CAAC,OAAc,KAAK,QAAQ,CAAC,EAAE,EAAE,QAAQ,KAAK,QAAQ,EAC1D,IAAI,CAAC;wCACR,gBAAgB;wCAChB,IAAI,eAAe;4CACjB,cAAc;wCAChB;oCACF,OAAO;wCACL,MAAM,IAAI,MAAM;oCAClB;gCACF,OAAO;oCACL,MAAM,IAAI,MAAM;gCAClB;4BACF,EAAE,OAAO,OAAY;gCACnB,QAAQ,KAAK,CAAC,4BAA4B;gCAC1C,SAAS,MAAM,QAAQ,EAAE,MAAM,SAAS;4BAC1C,SAAU;gCACR,aAAa;4BACf;wBACF;wBACA,UAAU,aAAa,CAAC,KAAK,IAAI;wBACjC,WAAW,CAAC,2CAA2C,EACrD,aAAa,CAAC,KAAK,IAAI,KACnB,iDACA,8DACJ;kCAED,0BACC,6LAAC;4BAAK,WAAU;;8CACd,6LAAC,iJAAA,CAAA,cAAW;oCAAC,WAAU;oCAAoB,MAAM;;;;;;gCAAM;;;;;;mCAIzD;;;;;;kCAIJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC,iJAAA,CAAA,cAAW;oCAAC,MAAM;;;;;;;;;;;0CAErB,6LAAC;gCAAO,WAAU;gCAAoE,SAAS;0CAC7F,cAAA,6LAAC,iJAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAIlB,6LAAC;wBACC,SAAS,IAAM,gBAAgB,CAAC;wBAChC,WAAU;wBACV,OAAO,eAAe,qBAAqB;kCAE1C,6BAAe,6LAAC,iJAAA,CAAA,cAAW;4BAAC,MAAM;;;;;iDAAS,6LAAC,iJAAA,CAAA,cAAW;4BAAC,MAAM;;;;;;;;;;;;;;;;;YAKlE,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAW;;;;;;;;;;;0BAK5B,6LAAC;gBAAI,WAAW,GAAG,eAAe,oCAAoC,gBAAgB,wBAAwB,CAAC;0BAE7G,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,6BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAO;;;;;;8CACvB,6LAAC;oCAAI,OAAM;oCAA6B,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;oCAAO,QAAO;oCAAe,aAAY;oCAAI,eAAc;oCAAQ,gBAAe;8CACxK,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;sCAId,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,QAAQ;4BAAsB;;gCAEtC,6BACC,6LAAC;oCAAI,WAAU;;;;;;gCAIhB,CAAC;oCACA,MAAM,gBAAgB,aAAa,KAAK,CAAC,UAAU,MAAM,CAAC,SAAS,MAAM;oCACzE,IAAI,gBAAgB,KAAK;wCACvB,qBACE,6LAAC;4CAAI,WAAU;;gDAAoG;gDACxF;gDAAc;;;;;;;oCAG7C;oCACA,OAAO;gCACT,CAAC;8CAED,6LAAC;oCAAI,WAAW,GAAG,aAAa,KAAK,CAAC,UAAU,MAAM,CAAC,SAAS,MAAM,GAAG,MAAM,SAAS,IAAI;8CACzF,CAAC;wCACA,MAAM,YAAoE,EAAE;wCAC5E,MAAM,QAAQ;wCACd,IAAI;wCAEJ,MAAO,CAAC,QAAQ,MAAM,IAAI,CAAC,aAAa,MAAM,KAAM;4CAClD,UAAU,IAAI,CAAC;gDACb,MAAM,KAAK,CAAC,EAAE,CAAC,IAAI;gDACnB,YAAY,MAAM,KAAK;gDACvB,UAAU,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;4CACzC;wCACF;wCAEA,MAAM,eAAe;wCACrB,MAAM,sBAAsB,UAAU,MAAM,GAAG;wCAE/C,IAAI,qBAAqB;4CACvB,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,MAAM,CAAC,wBAAwB,EAAE,aAAa,iBAAiB,CAAC;wCACrG;wCAEA,MAAM,mBAAmB,sBACrB,UAAU,KAAK,CAAC,GAAG,gBACnB;wCAEJ,OAAO,iBAAiB,GAAG,CAAC,CAAC,UAAU;4CACrC,MAAM,aAAa,qBAAqB,SAAS,IAAI;4CAErD,qBACE,6LAAC;gDAEC,WAAW,CAAC,mCAAmC,EAAE,aAAa,eAAe,oBAAoB;gDACjG,SAAS;oDACP,2BAA2B;wDACzB,KAAK;wDACL,MAAM;oDACR;oDACA,oBAAoB,SAAS,IAAI;oDACjC,0BAA0B,SAAS,IAAI;gDACzC;0DAEC,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM;oDACnC,MAAM,YAAY,AAAC,KAAK,MAAM,GAAG,KAAK,YAAY,MAAM,KACtC,KAAK,MAAM,GAAG,KAAK,YAAY,MAAM;oDACvD,qBACE,6LAAC;wDAEC,WAAW,YAAY,6BAA6B;;4DAEnD;4DAAM;;uDAHF;;;;;gDAMX;+CAtBK;;;;;wCAyBX;oCACF,CAAC;;;;;;;;;;;;wBAKJ,oBAAoB,2BAA2B,qCAC9C,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCACL,KAAK;gCACL,MAAM;gCACN,WAAW;4BACb;;8CAEA,6LAAC;oCACC,SAAS,IAAM,uBAAuB;oCACtC,WAAU;8CAEV,cAAA,6LAAC,iJAAA,CAAA,MAAG;;;;;;;;;;8CAGN,6LAAC;oCAAI,WAAU;8CACZ,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,cAAW;gDAAC,WAAU;gDAAoB,MAAM;;;;;;0DACjD,6LAAC;0DAAK;;;;;;;;;;;+CAGR,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC3B,6LAAC;4CAEC,WAAU;4CACV,SAAS;gDACP,IAAI,kBAAkB;oDACpB,sBAAsB,kBAAkB;gDAC1C;gDACA,uBAAuB;gDACvB,oBAAoB;4CACtB;sDAEC,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM;gDAC5B,MAAM,iBAAiB,KAAK,OAAO,CAAC,iBAAiB,IAAI,WAAW;gDACpE,MAAM,qBAAqB,kBAAkB,cAAc,QAAQ,iBAAiB;gDACpF,MAAM,YAAY,eAAe,MAAM,GAAG,KACzB,sBACA,CAAC,mBAAmB,QAAQ,CAAC;gDAC9C,qBACE,6LAAC;oDAEC,WAAW,YAAY,6BAA6B;;wDAEnD;wDAAM;;mDAHF;;;;;4CAMX;2CAxBK;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAmCrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCACE,aAAa,KAAK,CAAC,OAAO,MAAM,CAAC,SAAS,MAAM;oCAAC;;;;;;;0CAEpD,6LAAC;;oCACE,aAAa,MAAM;oCAAC;;;;;;;0CAEvB,6LAAC;;oCACE,aAAa,KAAK,CAAC,UAAU,MAAM,CAAC,SAAS,MAAM;oCAAC;;;;;;;;;;;;;kCAIzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC,iJAAA,CAAA,cAAW;oCAAC,MAAM;;;;;;;;;;;0CAErB,6LAAC;gCAAO,WAAU;gCAAgC,SAAS;0CACzD,cAAA,6LAAC,iJAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAMpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GAtcM;KAAA;uCAwcS", "debugId": null}}, {"offset": {"line": 4999, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Scholarar/V2_mentor/frontend/app/text-analyzer/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { AnimatePresence, motion } from 'framer-motion';\nimport { FiCheck, FiX, FiAlertTriangle, FiEdit3, FiRefreshCw, FiSearch, FiFileText } from 'react-icons/fi';\nimport axios from 'axios';\nimport { v4 as uuidv4 } from 'uuid';\n\n// Import our custom components\nimport GrammarCheckComponent from '../components/text-analyzer/GrammarCheckComponent';\nimport CriteriaCheckingComponent from '../components/text-analyzer/CriteriaCheckingComponent';\nimport HierarchicalHistorySidebar from '../components/text-analyzer/HierarchicalHistorySidebar';\nimport ResultCardsPanel from '../components/text-analyzer/ResultCardsPanel';\nimport AiDetectorResultPanel from '../components/text-analyzer/AiDetectorResultPanel';\nimport { HistoryEntry, GroupedHistoryEntry } from '../components/text-analyzer/HistoryEntry';\nimport Paraphraser from '../components/text-analyzer/Paraphraser';\n\n// Types for our analysis results\ntype IssueType = 'grammar' | 'spelling' | 'ai-content' | 'criteria';\n\ninterface Issue {\n  id: string;\n  type: IssueType;\n  original: string;\n  suggestion: string;\n  position: { start: number; end: number };\n  explanation?: string;\n  category?: 'misspelling' | 'correctness' | 'clarity' | 'engagement' | 'delivery';\n}\n\ninterface AIAnalysisResult {\n  ai_score: number;\n  classification: string;\n  human_score: number;\n  sentences: {\n    text: string;\n    ai_score: number;\n    prediction: 'original' | 'ai-generated';\n  }[];\n}\n\ninterface GrammarCheckResult {\n  corrections: {\n    incorrect: string;\n    suggestion: string;\n    start: number;\n    end: number;\n    type: string;\n    explanation: string;\n  }[];\n  corrected_text?: string;\n}\n\ninterface ParaphraseResult {\n  paraphrased_text: string;\n  options?: string[];\n}\n\ninterface CriteriaCheckResult {\n  content: string;\n  matches: {\n    criteria: string;\n    match_level: 'high' | 'medium' | 'low';\n    explanation: string;\n  }[];\n}\n\nconst TextAnalyzer = () => {\n  // State for analysis results\n  const [activeTab, setActiveTab] = useState<'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser'>('ai-detector');\n  const [text, setText] = useState<string>('I have went to the store yesterday and buyed some grocerys. Their was alot of people their.');\n  const [issues, setIssues] = useState<Issue[]>([]);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [showSuggestions, setShowSuggestions] = useState(true);\n  // Column width states\n  const [inputColumnWidth, setInputColumnWidth] = useState(40); // Default width in percentage\n  const [resultColumnWidth, setResultColumnWidth] = useState(60); // Default width in percentage\n  const [suggestionColumnWidth, setSuggestionColumnWidth] = useState(30); // Default width in percentage\n\n  // Results for different analysis types\n  const [aiDetectionResult, setAiDetectionResult] = useState<AIAnalysisResult | null>(null);\n  const [grammarResult, setGrammarResult] = useState<GrammarCheckResult | null>(null);\n  const [paraphraseResult, setParaphraseResult] = useState<ParaphraseResult | null>(null);\n  const [criteriaResult, setCriteriaResult] = useState<CriteriaCheckResult | null>(null);\n  const [error, setError] = useState<string | null>(null);\n\n  // History tracking\n  const [historyEntries, setHistoryEntries] = useState<HistoryEntry[]>([]);\n  const [currentInputId, setCurrentInputId] = useState<string>(uuidv4());\n  const [showHistorySidebar, setShowHistorySidebar] = useState<boolean>(false);\n  const [activeInputId, setActiveInputId] = useState<string | null>(null);\n  const [activeFeatureId, setActiveFeatureId] = useState<string | null>(null);\n  const [selectedEntries, setSelectedEntries] = useState<HistoryEntry[]>([]);\n\n  // Paraphraser options\n  const [paraphraseStyle, setParaphraseStyle] = useState<'standard' | 'formal' | 'simple' | 'creative'>('standard');\n\n  // Criteria check options\n  const [scholarshipType, setScholarshipType] = useState<string>('academic');\n  const [essayType, setEssayType] = useState<string>('personal_statement');\n\n  // Calculate word count\n  const wordCount = text.split(/\\s+/).filter(Boolean).length;\n\n  // Group history entries by input text\n  const groupedHistoryEntries: GroupedHistoryEntry[] = React.useMemo(() => {\n    const groups: { [key: string]: HistoryEntry[] } = {};\n\n    // Group entries by exact input text match\n    historyEntries.forEach(entry => {\n      const key = entry.inputText.trim();\n      if (!groups[key]) {\n        groups[key] = [];\n      }\n      groups[key].push(entry);\n    });\n\n    // Convert to GroupedHistoryEntry array and sort by most recent\n    return Object.entries(groups)\n      .map(([inputText, features]) => {\n        // Sort features by timestamp (most recent first)\n        const sortedFeatures = features.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());\n\n        return {\n          inputText,\n          inputId: `input-${inputText.substring(0, 20).replace(/\\s+/g, '-')}`, // Create a stable ID\n          timestamp: sortedFeatures[0].timestamp, // Use most recent feature timestamp\n          features: sortedFeatures\n        };\n      })\n      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()); // Sort groups by most recent\n  }, [historyEntries]);\n\n  // Load text from localStorage and handle URL parameters\n  useEffect(() => {\n    // Check if we're in the browser\n    if (typeof window !== 'undefined') {\n      // Get the tab from URL query parameters\n      const params = new URLSearchParams(window.location.search);\n      const tabParam = params.get('tab');\n\n      // Set the active tab if it's valid\n      if (tabParam && ['ai-detector', 'grammar-check', 'criteria-check', 'paraphraser'].includes(tabParam as any)) {\n        setActiveTab(tabParam as 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser');\n      }\n\n      // Load saved text from localStorage\n      const savedText = localStorage.getItem('scholarar_input_text');\n      if (savedText) {\n        setText(savedText);\n        // Clear the localStorage after loading to avoid reusing the same text unintentionally\n        localStorage.removeItem('scholarar_input_text');\n      }\n    }\n  }, []);\n\n  // Function to analyze text based on active tab\n  const analyzeText = async () => {\n    if (!text.trim()) return;\n\n    setIsAnalyzing(true);\n    setError(null);\n\n    // Clear selected entries to show the normal interface\n    setSelectedEntries([]);\n    setActiveFeatureId(null);\n    setActiveInputId(null);\n\n    try {\n      switch (activeTab) {\n        case 'ai-detector':\n          await analyzeAIContent();\n          break;\n        case 'grammar-check':\n          await checkGrammar();\n          break;\n        case 'paraphraser':\n          await paraphraseText();\n          break;\n        case 'criteria-check':\n          await checkCriteria();\n          break;\n      }\n    } catch (err: any) {\n      console.error('Analysis error:', err);\n      setError(err.message || 'An error occurred during analysis');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  // AI Detection analysis\n  const analyzeAIContent = async () => {\n    try {\n      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\n      const effectiveText = getEffectiveInputText();\n      const response = await axios.post(`${API_URL}/detect-ai`, { text: effectiveText });\n\n      if (response.data) {\n        setAiDetectionResult(response.data);\n\n        // Convert AI detection results to issues\n        const aiIssues: Issue[] = response.data.sentences\n          .filter((sentence: any) => sentence.prediction === 'ai-generated' && sentence.ai_score > 0.7)\n          .map((sentence: any, index: number) => {\n            const startPos = effectiveText.indexOf(sentence.text);\n            return {\n              id: `ai-${index}`,\n              type: 'ai-content',\n              original: sentence.text,\n              suggestion: sentence.text, // No suggestion for AI content, just highlighting\n              position: {\n                start: startPos,\n                end: startPos + sentence.text.length\n              },\n              explanation: `AI probability: ${(sentence.ai_score * 100).toFixed(1)}%`\n            };\n          });\n\n        setIssues(aiIssues);\n\n        // Add to history (use original input text for grouping, but store the effective text used)\n        addToHistory('ai-detector', response.data);\n      }\n    } catch (err) {\n      console.error('AI detection error:', err);\n      throw err;\n    }\n  };\n\n  // Grammar check analysis - now fully handled by the GrammarCheckComponent\n  const checkGrammar = async () => {\n    try {\n      // We don't need to do anything here anymore\n      // The GrammarCheckComponent handles everything internally\n    } catch (err) {\n      console.error('Grammar check error:', err);\n      throw err;\n    }\n  };\n\n  // Paraphrasing analysis - now handled by the ParaphrasingComponent\n  const paraphraseText = async () => {\n    try {\n      // We'll just set issues to empty since the component handles the API call\n      setIssues([]);\n    } catch (err) {\n      console.error('Paraphrasing error:', err);\n      throw err;\n    }\n  };\n\n  // Criteria check analysis - now handled by the CriteriaCheckingComponent\n  const checkCriteria = async () => {\n    try {\n      // We'll just set issues to empty since the component handles the API call\n      setIssues([]);\n    } catch (err) {\n      console.error('Criteria check error:', err);\n      throw err;\n    }\n  };\n\n  // Apply a suggestion to the text (only updates the output, not the input)\n  const applySuggestion = (issue: Issue) => {\n    // We don't modify the input text, only update the output in the grammar component\n    // This is handled by the GrammarCheckComponent internally\n\n    // Remove the issue from the list\n    setIssues(issues.filter(i => i.id !== issue.id));\n\n    // If we're in grammar check mode, update the analysis after applying the suggestion\n    if (activeTab === 'grammar-check' && grammarResult) {\n      const updatedCorrections = grammarResult.corrections.filter(\n        c => !(c.start === issue.position.start && c.end === issue.position.end)\n      );\n      setGrammarResult({\n        ...grammarResult,\n        corrections: updatedCorrections\n      });\n\n      // Call the apply correction function in the grammar component\n      if (issue.id && typeof window !== 'undefined' && 'applyGrammarCorrection' in window) {\n        (window as any).applyGrammarCorrection(issue.id);\n      }\n    }\n  };\n\n  // We no longer need this function as the GrammarCheckComponent handles everything internally\n\n  // State to track which column is being resized\n  const [isResizing, setIsResizing] = useState<'input' | 'result' | null>(null);\n\n  // Handle resizing of the input column\n  const startInputResize = (e: React.MouseEvent) => {\n    e.preventDefault();\n    setIsResizing('input');\n    const startX = e.clientX;\n    const startInputWidth = inputColumnWidth;\n    const startResultWidth = resultColumnWidth;\n\n    const handleMouseMove = (e: MouseEvent) => {\n      const containerWidth = document.querySelector('main')?.clientWidth || 1000;\n      const delta = ((e.clientX - startX) / containerWidth) * 100;\n\n      // Calculate new widths ensuring they stay within reasonable bounds\n      const newInputWidth = Math.max(20, Math.min(70, startInputWidth + delta));\n      const newResultWidth = Math.max(30, Math.min(80, startResultWidth - delta));\n\n      // Update column widths\n      setInputColumnWidth(newInputWidth);\n      setResultColumnWidth(newResultWidth);\n    };\n\n    const handleMouseUp = () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n      setIsResizing(null);\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n  };\n\n  // Handle resizing of the result column when suggestion panel is visible\n  const startResultResize = (e: React.MouseEvent) => {\n    e.preventDefault();\n    setIsResizing('result');\n    const startX = e.clientX;\n    const startResultWidth = resultColumnWidth;\n    const startSuggestionWidth = suggestionColumnWidth;\n\n    const handleMouseMove = (e: MouseEvent) => {\n      const containerWidth = document.querySelector('main')?.clientWidth || 1000;\n      const delta = ((e.clientX - startX) / containerWidth) * 100;\n\n      // Calculate new widths ensuring they stay within reasonable bounds\n      const newResultWidth = Math.max(20, Math.min(60, startResultWidth + delta));\n      const newSuggestionWidth = Math.max(20, Math.min(60, startSuggestionWidth - delta));\n\n      // Update column widths\n      setResultColumnWidth(newResultWidth);\n      setSuggestionColumnWidth(newSuggestionWidth);\n    };\n\n    const handleMouseUp = () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n      setIsResizing(null);\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n  };\n\n  // Accept all suggestions at once (only updates the output, not the input)\n  const acceptAllSuggestions = () => {\n    // We don't modify the input text, only update the output in the grammar component\n    // This is handled by the GrammarCheckComponent internally\n\n    // Clear the issues list\n    setIssues([]);\n\n    // Clear the specific result based on active tab\n    if (activeTab === 'grammar-check') {\n      setGrammarResult(prev => prev ? {...prev, corrections: []} : null);\n\n      // Call the apply all corrections function in the grammar component\n      if (typeof window !== 'undefined' && 'applyAllGrammarCorrections' in window) {\n        (window as any).applyAllGrammarCorrections();\n      }\n    }\n  };\n\n  // Clear the editor and start a new analysis\n  const clearEditor = () => {\n    setText('');\n    setIssues([]);\n    setAiDetectionResult(null);\n    setGrammarResult(null);\n    setParaphraseResult(null);\n    setCriteriaResult(null);\n    setError(null);\n    setCurrentInputId(uuidv4());\n    setActiveInputId(null);\n    setActiveFeatureId(null);\n    setSelectedEntries([]);\n  };\n\n  // Handle tab change - input text never changes automatically\n  const handleTabChange = (tab: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {\n    setActiveTab(tab);\n    setIssues([]);\n\n    // Clear current results when switching tabs\n    setAiDetectionResult(null);\n    setGrammarResult(null);\n    setParaphraseResult(null);\n    setCriteriaResult(null);\n    setError(null);\n\n    // Clear history selection to show normal interface\n    setSelectedEntries([]);\n    setActiveFeatureId(null);\n    setActiveInputId(null);\n  };\n\n  // Add entry to history - create a new entry only when switching features or when input text changes\n  const addToHistory = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser', resultContent: any) => {\n    const outputText = getOutputText(featureType, resultContent);\n\n    // Check if this is the same feature as the most recent entry with the same input text\n    const mostRecentEntry = historyEntries[0];\n    const isSameFeatureAndInput = mostRecentEntry &&\n      mostRecentEntry.featureType === featureType &&\n      mostRecentEntry.inputText.trim() === text.trim();\n\n    if (isSameFeatureAndInput) {\n      // Update the existing entry instead of creating a new one\n      const updatedEntry: HistoryEntry = {\n        ...mostRecentEntry,\n        timestamp: new Date(),\n        resultContent,\n        outputText\n      };\n\n      setHistoryEntries(prev => [updatedEntry, ...prev.slice(1)]);\n    } else {\n      // Create a new entry for different feature or different input\n      const newEntry: HistoryEntry = {\n        id: uuidv4(),\n        timestamp: new Date(),\n        featureType,\n        inputText: text, // Always use the original input text for grouping\n        resultContent,\n        outputText\n      };\n\n      // Add to history (most recent first)\n      setHistoryEntries(prev => [newEntry, ...prev]);\n    }\n\n    // Clear selected entries to show the normal interface with the new result\n    setSelectedEntries([]);\n    setActiveFeatureId(null);\n    setActiveInputId(null);\n  };\n\n  // Toggle history sidebar\n  const toggleHistorySidebar = () => {\n    setShowHistorySidebar(prev => !prev);\n  };\n\n  // Check if there are current results for the active tab\n  const hasCurrentResults = () => {\n    switch (activeTab) {\n      case 'ai-detector':\n        return aiDetectionResult !== null;\n      case 'grammar-check':\n        return grammarResult !== null;\n      case 'paraphraser':\n        return paraphraseResult !== null;\n      case 'criteria-check':\n        return criteriaResult !== null;\n      default:\n        return false;\n    }\n  };\n\n  // Get output text from a feature result\n  const getOutputText = (featureType: string, resultContent: any): string => {\n    switch (featureType) {\n      case 'grammar-check':\n        return resultContent?.corrected_text || text;\n      case 'paraphraser':\n        return resultContent?.paraphrased_text || text;\n      case 'criteria-check':\n        return resultContent?.content || text;\n      case 'ai-detector':\n        return text; // AI detector doesn't modify text\n      default:\n        return text;\n    }\n  };\n\n\n\n  // Get the effective input text for analysis (uses output from the most recent feature)\n  const getEffectiveInputText = (): string => {\n    // Use the output from the most recent history entry if available\n    if (historyEntries.length > 0) {\n      const mostRecentEntry = historyEntries[0]; // Most recent is first\n      if (mostRecentEntry.outputText && mostRecentEntry.outputText !== text) {\n        return mostRecentEntry.outputText;\n      }\n    }\n\n    // Default to the original input text\n    return text;\n  };\n\n  // Handle input click - show all features for this input\n  const handleInputClick = (inputId: string) => {\n    const group = groupedHistoryEntries.find(g => g.inputId === inputId);\n    if (group) {\n      setText(group.inputText);\n      setActiveInputId(inputId);\n      setActiveFeatureId(null);\n      setSelectedEntries(group.features);\n\n      // Clear current results to show the cards panel\n      setAiDetectionResult(null);\n      setGrammarResult(null);\n      setParaphraseResult(null);\n      setCriteriaResult(null);\n      setIssues([]);\n    }\n  };\n\n\n\n  // Handle feature click - show only this specific feature result\n  const handleFeatureClick = (featureId: string) => {\n    const entry = historyEntries.find(e => e.id === featureId);\n    if (entry) {\n      setText(entry.inputText);\n      setActiveFeatureId(featureId);\n      setSelectedEntries([entry]);\n\n      // Find the input group for this entry\n      const inputGroup = groupedHistoryEntries.find(group =>\n        group.inputText.trim() === entry.inputText.trim()\n      );\n      if (inputGroup) {\n        setActiveInputId(inputGroup.inputId);\n      }\n\n      // Set the active tab to match the feature\n      setActiveTab(entry.featureType);\n\n      // Restore the result based on feature type for the main interface\n      switch (entry.featureType) {\n        case 'ai-detector':\n          setAiDetectionResult(entry.resultContent);\n          break;\n        case 'grammar-check':\n          setGrammarResult(entry.resultContent);\n          if (entry.resultContent?.corrections) {\n            const newIssues = entry.resultContent.corrections.map((correction: any) => ({\n              id: correction.id || `grammar-${Math.random()}`,\n              type: 'grammar' as IssueType,\n              original: correction.incorrect,\n              suggestion: correction.suggestion,\n              position: {\n                start: correction.start,\n                end: correction.end,\n              },\n              explanation: correction.explanation,\n              category: correction.category,\n            }));\n            setIssues(newIssues);\n          }\n          break;\n        case 'paraphraser':\n          setParaphraseResult(entry.resultContent);\n          break;\n        case 'criteria-check':\n          setCriteriaResult(entry.resultContent);\n          break;\n      }\n    }\n  };\n\n  // Get the appropriate icon for the active tab\n  const getTabIcon = (tab: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {\n    switch (tab) {\n      case 'ai-detector':\n        return <FiFileText className=\"mr-1\" />;\n      case 'grammar-check':\n        return <FiEdit3 className=\"mr-1\" />;\n      case 'paraphraser':\n        return <FiRefreshCw className=\"mr-1\" />;\n      case 'criteria-check':\n        return <FiSearch className=\"mr-1\" />;\n    }\n  };\n\n  // Helper function to get message based on tab type\n  const getTabMessage = (tab: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser'): string => {\n    switch (tab) {\n      case 'paraphraser':\n        return 'Click \"Analyze Text\" to paraphrase your content.';\n      case 'criteria-check':\n        return 'Click \"Analyze Text\" to check against scholarship criteria.';\n      case 'grammar-check':\n        return 'Click \"Analyze Text\" to check grammar.';\n      default:\n        return 'Click \"Analyze Text\" to check for issues.';\n    }\n  };\n\n  // Format the result display based on active tab\n  const renderResultContent = () => {\n    switch (activeTab) {\n      case 'ai-detector':\n        return renderAIDetectionResult();\n      case 'grammar-check':\n        return renderGrammarResult();\n      case 'paraphraser':\n        return renderParaphraseResult();\n      case 'criteria-check':\n        return renderCriteriaResult();\n      default:\n        return <p>Select an analysis type and click \"Analyze Text\"</p>;\n    }\n  };\n\n  // Render AI Detection result\n  const renderAIDetectionResult = () => {\n    if (!aiDetectionResult) return <p>No AI detection results yet. Click \"Analyze Text\" to check.</p>;\n\n    const overallScore = aiDetectionResult.ai_score * 100;\n\n    return (\n      <div>\n        <div className=\"mb-4 flex items-center justify-between\">\n          <h3 className=\"text-md font-medium\">AI Content Detection</h3>\n          <div className=\"text-lg font-bold\">\n            <span className={`${overallScore > 70 ? 'text-red-500' : overallScore > 30 ? 'text-yellow-500' : 'text-green-500'}`}>\n              {overallScore.toFixed(1)}% AI\n            </span>\n          </div>\n        </div>\n\n        <div className=\"mt-2 p-3 bg-yellow-50 rounded-md\">\n          {aiDetectionResult.sentences.map((sentence, index) => {\n            const isAI = sentence.prediction === 'ai-generated' && sentence.ai_score > 0.5;\n            return (\n              <span key={index} className={isAI ? 'bg-yellow-200 px-1 rounded' : ''}>\n                {sentence.text}{' '}\n              </span>\n            );\n          })}\n        </div>\n\n        <div className=\"mt-4\">\n          <div className=\"h-2 w-full bg-gray-200 rounded-full overflow-hidden\">\n            <div\n              className={`h-full ${overallScore > 70 ? 'bg-red-500' : overallScore > 30 ? 'bg-yellow-500' : 'bg-green-500'}`}\n              style={{ width: `${overallScore}%` }}\n            ></div>\n          </div>\n          <div className=\"flex justify-between text-xs mt-1\">\n            <span>Human Content</span>\n            <span>AI Content</span>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  // Render Grammar Check result\n  const renderGrammarResult = () => {\n    const effectiveText = getEffectiveInputText();\n    return (\n      <GrammarCheckComponent\n        text={effectiveText}\n        showSuggestions={showSuggestions}\n        onShowSuggestions={(show) => setShowSuggestions(show)}\n        onCorrectionsApplied={(_) => {\n          // We're NOT updating the input text here\n          // Clear the issues since the correction was applied\n          setIssues([]);\n        }}\n        onSuggestionsFound={(corrections) => {\n          // Map corrections to issues\n          const newIssues = corrections.map((correction) => ({\n            id: correction.id || `grammar-${Math.random()}`,\n            type: 'grammar' as IssueType,\n            original: correction.incorrect,\n            suggestion: correction.suggestion,\n            position: {\n              start: correction.start,\n              end: correction.end,\n            },\n            explanation: correction.explanation,\n            category: correction.category,\n          }));\n\n          const result = {\n            corrections: corrections,\n            corrected_text: effectiveText, // Use the effective text that was actually processed\n          };\n\n          setIssues(newIssues);\n          setGrammarResult(result);\n\n          // Add to history\n          addToHistory('grammar-check', result);\n        }}\n      />\n    );\n  };\n\n  // Update the suggestion panel visibility based on active tab\n  useEffect(() => {\n    // Show suggestions for grammar check\n    setShowSuggestions(activeTab === 'grammar-check');\n  }, [activeTab]);\n\n  // Store previous column widths when toggling suggestions\n  const [prevColumnWidths, setPrevColumnWidths] = useState({\n    input: 30,\n    result: 40,\n    suggestion: 30\n  });\n\n  // Ref to track if we're in the middle of a layout change to prevent infinite loops\n  const isChangingLayout = useRef(false);\n\n  // Adjust column widths when suggestion panel is shown/hidden or tab changes\n  useEffect(() => {\n    if (isChangingLayout.current) return;\n    isChangingLayout.current = true;\n\n    try {\n      if (showSuggestions && activeTab === 'grammar-check') {\n        // When suggestions are shown, restore from previous 3-column layout if available\n        if (prevColumnWidths.input + prevColumnWidths.result + prevColumnWidths.suggestion === 100) {\n          setInputColumnWidth(prevColumnWidths.input);\n          setResultColumnWidth(prevColumnWidths.result);\n          setSuggestionColumnWidth(prevColumnWidths.suggestion);\n        } else {\n          // Use default 30/40/30 split if previous values are invalid\n          setInputColumnWidth(30);\n          setResultColumnWidth(40);\n          setSuggestionColumnWidth(30);\n        }\n      } else if ((activeTab === 'grammar-check' && !showSuggestions) ||\n                 activeTab === 'ai-detector' ||\n                 activeTab === 'paraphraser' ||\n                 activeTab === 'criteria-check') {\n        // When hiding suggestions or switching to other tabs, save the current 3-column layout if needed\n        if (showSuggestions && activeTab === 'grammar-check') {\n          setPrevColumnWidths({\n            input: inputColumnWidth,\n            result: resultColumnWidth,\n            suggestion: suggestionColumnWidth\n          });\n        }\n\n        // Reset to 40/60 split for 2-column layout\n        setInputColumnWidth(40);\n        setResultColumnWidth(60);\n      }\n    } finally {\n      isChangingLayout.current = false;\n    }\n  }, [showSuggestions, activeTab]);\n\n  // Render Paraphrase result\n  const renderParaphraseResult = () => {\n    const effectiveText = getEffectiveInputText();\n    return (\n      <Paraphraser\n        text={effectiveText}\n        onParaphrased={(paraphrasedText: string) => {\n          const result = {\n            paraphrased_text: paraphrasedText\n          };\n\n          if (paraphraseResult) {\n            setParaphraseResult({\n              ...paraphraseResult,\n              paraphrased_text: paraphrasedText\n            });\n          } else {\n            setParaphraseResult(result);\n          }\n\n          // Add to history\n          addToHistory('paraphraser', result);\n        }}\n      />\n    );\n  };\n\n  // Render Criteria Check result\n  const renderCriteriaResult = () => {\n    const effectiveText = getEffectiveInputText();\n    return (\n      <CriteriaCheckingComponent\n        text={effectiveText}\n        onAnalysisComplete={(result) => {\n          setCriteriaResult(result);\n\n          // Add to history\n          addToHistory('criteria-check', result);\n        }}\n      />\n    );\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{\n        duration: 0.5,\n        type: \"spring\",\n        stiffness: 100,\n        damping: 15\n      }}\n      className={`h-[calc(100vh-64px)] bg-gray-50 flex flex-col overflow-hidden max-h-[calc(100vh-64px)] ${isResizing ? 'cursor-ew-resize' : ''}`}\n    >\n      {/* Main content area */}\n      <main className=\"flex-1 flex overflow-hidden\">\n        {/* History Sidebar */}\n        <HierarchicalHistorySidebar\n          isVisible={showHistorySidebar}\n          toggleSidebar={toggleHistorySidebar}\n          groupedEntries={groupedHistoryEntries}\n          activeInputId={activeInputId}\n          activeFeatureId={activeFeatureId}\n          onInputClick={handleInputClick}\n          onFeatureClick={handleFeatureClick}\n          onNewAnalysis={clearEditor}\n        />\n        {/* Input column */}\n        <motion.div\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n          className={`relative p-3 border-r border-gray-200 ${\n            isResizing === 'input' ? 'bg-blue-50 transition-colors' : ''\n          }`}\n          style={{\n            width: `${inputColumnWidth}%`\n          }}\n        >\n          {/* Resize handle - show for all modes */}\n          <div\n            className=\"absolute right-0 top-0 bottom-0 w-1 cursor-ew-resize bg-gray-200 hover:bg-blue-500 hover:w-1.5 transition-all z-10\"\n            onMouseDown={startInputResize}\n          ></div>\n          <div className=\"flex justify-between items-center mb-3\">\n            <h2 className=\"text-lg font-medium\">Input</h2>\n            {isResizing === 'input' && (\n              <span className=\"text-xs text-gray-600 font-mono bg-blue-100 px-2 py-1 rounded\">\n                {Math.round(inputColumnWidth)}%\n              </span>\n            )}\n          </div>\n          <div className=\"bg-white rounded-md shadow-sm border border-gray-200 flex flex-col\" style={{ height: \"calc(100vh - 180px)\" }}>\n            <textarea\n              className=\"flex-1 p-4 focus:outline-none resize-none overflow-auto\"\n              placeholder=\"I have went to the store yesterday and buyed some grocerys. Their was alot of people their.\"\n              value={text}\n              onChange={(e) => setText(e.target.value)}\n            />\n            <div className=\"border-t border-gray-200 p-2 flex justify-between items-center\">\n              <button\n                onClick={clearEditor}\n                className=\"px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded\"\n              >\n                Clear\n              </button>\n              <div className=\"text-sm text-gray-500\">\n                {wordCount} words\n              </div>\n            </div>\n          </div>\n\n          {/* Tool selection tabs */}\n          <div className=\"mt-4 flex space-x-2\">\n            <button\n              onClick={() => handleTabChange('ai-detector')}\n              className={`px-3 py-1 text-sm rounded flex items-center ${activeTab === 'ai-detector' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}\n            >\n              {getTabIcon('ai-detector')} AI detector\n            </button>\n            <button\n              onClick={() => handleTabChange('grammar-check')}\n              className={`px-3 py-1 text-sm rounded flex items-center ${activeTab === 'grammar-check' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}\n            >\n              {getTabIcon('grammar-check')} Grammar Check\n            </button>\n            <button\n              onClick={() => handleTabChange('paraphraser')}\n              className={`px-3 py-1 text-sm rounded flex items-center ${activeTab === 'paraphraser' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}\n            >\n              {getTabIcon('paraphraser')} Paraphraser\n            </button>\n            <button\n              onClick={() => handleTabChange('criteria-check')}\n              className={`px-3 py-1 text-sm rounded flex items-center ${activeTab === 'criteria-check' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}\n            >\n              {getTabIcon('criteria-check')} Criteria check\n            </button>\n          </div>\n        </motion.div>\n\n        {/* Result column */}\n        <motion.div\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5, delay: 0.3 }}\n          className={`relative p-3 ${\n            activeTab === 'ai-detector' || activeTab === 'paraphraser' ? '' : 'border-r border-gray-200'\n          } ${\n            isResizing === 'result' ? 'bg-blue-50 transition-colors' : ''\n          }`}\n          style={{\n            width: `${resultColumnWidth}%`\n          }}\n        >\n          {/* Resize handle - only shown when suggestions are visible */}\n          {showSuggestions && activeTab === 'grammar-check' && (\n            <div\n              className=\"absolute right-0 top-0 bottom-0 w-1 cursor-ew-resize bg-gray-200 hover:bg-blue-500 hover:w-1.5 transition-all z-10\"\n              onMouseDown={startResultResize}\n            ></div>\n          )}\n          <div className=\"flex justify-between items-center mb-3\">\n            <h2 className=\"text-lg font-medium\">Result</h2>\n            <div className=\"flex items-center space-x-2\">\n              {isResizing === 'result' && (\n                <span className=\"text-xs text-blue-600 font-mono bg-blue-100 px-2 py-1 rounded\">\n                  {Math.round(resultColumnWidth)}%\n                </span>\n              )}\n              {activeTab === 'grammar-check' && !showSuggestions && (\n                <button\n                  onClick={() => setShowSuggestions(true)}\n                  className=\"text-blue-600 hover:text-blue-800 text-sm flex items-center\"\n                >\n                  <span className=\"mr-1\">Show suggestions</span>\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                    <polyline points=\"9 18 15 12 9 6\"></polyline>\n                  </svg>\n                </button>\n              )}\n            </div>\n          </div>\n          <div className=\"bg-white rounded-md shadow-sm border border-gray-200 flex flex-col\" style={{ height: \"calc(100vh - 180px)\" }}>\n            {/* Show cards panel when viewing history AND not currently analyzing, otherwise show normal result content */}\n            {selectedEntries.length > 0 && !isAnalyzing && !hasCurrentResults() ? (\n              <ResultCardsPanel\n                entries={selectedEntries}\n                isLoading={isAnalyzing}\n                error={error}\n              />\n            ) : activeTab === 'ai-detector' ? (\n              <AiDetectorResultPanel\n                activeTab={activeTab}\n                isAnalyzing={isAnalyzing}\n                text={text}\n                onAnalyze={analyzeText}\n                aiDetectionResult={aiDetectionResult}\n                error={error}\n              />\n            ) : (\n              <div className=\"p-4 flex-1 flex flex-col overflow-hidden\">\n                <motion.div\n                  key={activeTab}\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  exit={{ opacity: 0 }}\n                  transition={{ duration: 0.3 }}\n                  className=\"h-full flex flex-col overflow-hidden\"\n                >\n                  {error ? (\n                    <div className=\"p-4 bg-red-50 text-red-700 rounded-md\">\n                      <h3 className=\"font-medium mb-2\">Error</h3>\n                      <p>{error}</p>\n                    </div>\n                  ) : (\n                    <div className=\"overflow-y-auto flex-1\">\n                      {renderResultContent()}\n                    </div>\n                  )}\n                </motion.div>\n              </div>\n            )}\n          </div>\n\n\n        </motion.div>\n\n        {/* Suggestion column with fixed width - only shown for grammar check */}\n        <AnimatePresence>\n          {showSuggestions && activeTab === 'grammar-check' && (\n            <motion.div\n              initial={{ opacity: 0, width: 0 }}\n              animate={{ opacity: 1, width: `${suggestionColumnWidth}%` }}\n              exit={{ opacity: 0, width: 0 }}\n              transition={{ duration: 0.4, type: \"spring\", stiffness: 100 }}\n              className={`relative ${isResizing === 'result' ? 'bg-blue-50 transition-colors' : ''}`}\n            >\n            {/* No resize handle needed here - the result column already has one */}\n\n            <div className=\"h-full p-3 border-r border-gray-200\">\n              <div className=\"flex justify-between items-center mb-3\">\n                <h2 className=\"text-lg font-medium\">Suggestion</h2>\n                <div className=\"flex items-center space-x-2\">\n                  {isResizing === 'result' && (\n                    <span className=\"text-xs text-blue-600 font-mono bg-blue-100 px-2 py-1 rounded\">\n                      {Math.round(suggestionColumnWidth)}%\n                    </span>\n                  )}\n                  <button\n                    onClick={() => setShowSuggestions(false)}\n                    className=\"text-gray-500 hover:text-gray-700\"\n                  >\n                    Hide\n                  </button>\n                </div>\n              </div>\n\n              <AnimatePresence>\n                {issues.length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    className=\"flex items-center mb-4 bg-yellow-50 p-3 rounded-md\"\n                  >\n                    <div className=\"flex-shrink-0 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-white mr-3\">\n                      <FiAlertTriangle />\n                    </div>\n                    <div>\n                      <span className=\"font-medium\">Found {issues.length} {issues.length === 1 ? 'issue' : 'issues'}</span>\n                    </div>\n                    <button\n                      onClick={acceptAllSuggestions}\n                      className=\"ml-auto bg-blue-600 text-white px-3 py-1 rounded text-sm\"\n                    >\n                      Accept all\n                    </button>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n\n              {/* Fixed height scrollable container for suggestions */}\n              <div className=\"overflow-y-auto border border-gray-200 rounded-lg\" style={{ height: \"calc(100vh - 180px)\" }}>\n                <AnimatePresence>\n                  {issues.map((issue) => (\n                    <motion.div\n                      key={issue.id}\n                      initial={{ opacity: 0, height: 0 }}\n                      animate={{ opacity: 1, height: 'auto' }}\n                      exit={{ opacity: 0, height: 0 }}\n                      transition={{ duration: 0.2 }}\n                      className=\"mb-4 bg-white p-3 rounded-md border border-gray-200\"\n                    >\n                      {issue.category ? (\n                        <>\n                          <div className=\"flex items-center mb-2\">\n                            <span className={`w-3 h-3 rounded-full mr-2 ${\n                              issue.category === 'misspelling' || issue.category === 'correctness'\n                                ? 'bg-red-500'\n                                : issue.category === 'clarity'\n                                ? 'bg-blue-500'\n                                : issue.category === 'engagement'\n                                ? 'bg-purple-500'\n                                : 'bg-green-500'\n                            }`}></span>\n                            <span className=\"text-sm font-medium text-gray-700\">\n                              {issue.category === 'misspelling'\n                                ? 'Misspelling'\n                                : issue.category === 'correctness'\n                                ? 'Correctness'\n                                : issue.category === 'clarity'\n                                ? 'Clarity'\n                                : issue.category === 'engagement'\n                                ? 'Engagement'\n                                : 'Delivery'}\n                            </span>\n                          </div>\n                          <div className=\"mb-2\">\n                            <p className=\"text-sm text-gray-600 mb-1\">Original:</p>\n                            <p className=\"px-2 py-1 bg-white rounded border border-gray-200 line-through\">\n                              {issue.original}\n                            </p>\n                          </div>\n                          <div className=\"mb-2\">\n                            <p className=\"text-sm text-gray-600 mb-1\">Suggestion:</p>\n                            <p className=\"px-2 py-1 bg-white rounded border border-gray-200 font-medium text-green-600\">\n                              {issue.suggestion}\n                            </p>\n                          </div>\n                        </>\n                      ) : (\n                        <>\n                          <div className=\"mb-2\">\n                            <span className=\"text-sm font-medium text-gray-700\">\n                              {issue.original}\n                            </span>\n                          </div>\n                          <div className=\"flex items-center mb-2\">\n                            <span className=\"text-green-600 font-medium\">{issue.suggestion}</span>\n                          </div>\n                        </>\n                      )}\n                      {issue.explanation && issue.type !== 'ai-content' && (\n                        <div className=\"mb-2\">\n                          <p className=\"text-sm text-gray-600 mb-1\">Explanation:</p>\n                          <p className=\"px-2 py-1 bg-white rounded border border-gray-200 text-sm\">\n                            {issue.explanation}\n                          </p>\n                        </div>\n                      )}\n                      <div className=\"flex space-x-2 mt-3\">\n                        {issue.type !== 'ai-content' && (\n                          <button\n                            onClick={() => applySuggestion(issue)}\n                            className=\"flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm flex items-center justify-center\"\n                          >\n                            <FiCheck className=\"mr-1\" /> Accept\n                          </button>\n                        )}\n                        <button\n                          onClick={() => setIssues(issues.filter(i => i.id !== issue.id))}\n                          className=\"flex-1 bg-white border border-gray-300 text-gray-700 px-3 py-2 rounded-md text-sm flex items-center justify-center\"\n                        >\n                          <FiX className=\"mr-1\" /> {issue.type === 'ai-content' ? 'Dismiss' : 'Ignore'}\n                        </button>\n                      </div>\n                    </motion.div>\n                  ))}\n                </AnimatePresence>\n\n                {issues.length === 0 && !isAnalyzing && (\n                  <div className=\"text-center text-gray-500 mt-8\">\n                    {activeTab === 'grammar-check' && grammarResult ? (\n                      <div className=\"p-4 bg-green-50 text-green-700 rounded-md\">\n                        <div className=\"flex items-center justify-center\">\n                          <FiCheck className=\"mr-2\" />\n                          <span className=\"font-medium\">No grammar issues found. Your text looks good!</span>\n                        </div>\n                      </div>\n                    ) : (\n                      <>\n                        <p>No issues found or all issues resolved.</p>\n                        <p className=\"text-sm mt-2\">\n                          {getTabMessage(activeTab)}\n                        </p>\n                      </>\n                    )}\n                  </div>\n                )}\n\n                {isAnalyzing && (\n                  <div className=\"flex flex-col items-center justify-center h-40\">\n                    <svg className=\"animate-spin h-8 w-8 text-blue-600\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    <p className=\"mt-3 text-gray-600\">Analyzing your text...</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n        )}\n        </AnimatePresence>\n      </main>\n    </motion.div>\n  );\n};\n\nexport default TextAnalyzer;\n\n"], "names": [], "mappings": ";;;AAkMsB;;AAhMtB;AACA;AAAA;AACA;AACA;AACA;AAEA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AAEA;;;AAfA;;;;;;;;;;;;AAmEA,MAAM,eAAe;;IACnB,6BAA6B;IAC7B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsE;IAC/G,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,sBAAsB;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,8BAA8B;IAC5F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,8BAA8B;IAC9F,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,8BAA8B;IAEtG,uCAAuC;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IACpF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAC9E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAClF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACjF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,mBAAmB;IACnB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;IAClE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAEzE,sBAAsB;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiD;IAEtG,yBAAyB;IACzB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,uBAAuB;IACvB,MAAM,YAAY,KAAK,KAAK,CAAC,OAAO,MAAM,CAAC,SAAS,MAAM;IAE1D,sCAAsC;IACtC,MAAM,wBAA+C,6JAAA,CAAA,UAAK,CAAC,OAAO;uDAAC;YACjE,MAAM,SAA4C,CAAC;YAEnD,0CAA0C;YAC1C,eAAe,OAAO;+DAAC,CAAA;oBACrB,MAAM,MAAM,MAAM,SAAS,CAAC,IAAI;oBAChC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;wBAChB,MAAM,CAAC,IAAI,GAAG,EAAE;oBAClB;oBACA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;gBACnB;;YAEA,+DAA+D;YAC/D,OAAO,OAAO,OAAO,CAAC,QACnB,GAAG;+DAAC,CAAC,CAAC,WAAW,SAAS;oBACzB,iDAAiD;oBACjD,MAAM,iBAAiB,SAAS,IAAI;sFAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;;oBAE1F,OAAO;wBACL;wBACA,SAAS,CAAC,MAAM,EAAE,UAAU,SAAS,CAAC,GAAG,IAAI,OAAO,CAAC,QAAQ,MAAM;wBACnE,WAAW,cAAc,CAAC,EAAE,CAAC,SAAS;wBACtC,UAAU;oBACZ;gBACF;8DACC,IAAI;+DAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;+DAAK,6BAA6B;QACjG;sDAAG;QAAC;KAAe;IAEnB,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,gCAAgC;YAChC,wCAAmC;gBACjC,wCAAwC;gBACxC,MAAM,SAAS,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;gBACzD,MAAM,WAAW,OAAO,GAAG,CAAC;gBAE5B,mCAAmC;gBACnC,IAAI,YAAY;oBAAC;oBAAe;oBAAiB;oBAAkB;iBAAc,CAAC,QAAQ,CAAC,WAAkB;oBAC3G,aAAa;gBACf;gBAEA,oCAAoC;gBACpC,MAAM,YAAY,aAAa,OAAO,CAAC;gBACvC,IAAI,WAAW;oBACb,QAAQ;oBACR,sFAAsF;oBACtF,aAAa,UAAU,CAAC;gBAC1B;YACF;QACF;iCAAG,EAAE;IAEL,+CAA+C;IAC/C,MAAM,cAAc;QAClB,IAAI,CAAC,KAAK,IAAI,IAAI;QAElB,eAAe;QACf,SAAS;QAET,sDAAsD;QACtD,mBAAmB,EAAE;QACrB,mBAAmB;QACnB,iBAAiB;QAEjB,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,MAAM;oBACN;gBACF,KAAK;oBACH,MAAM;oBACN;gBACF,KAAK;oBACH,MAAM;oBACN;gBACF,KAAK;oBACH,MAAM;oBACN;YACJ;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,wBAAwB;IACxB,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,UAAU,6DAAmC;YACnD,MAAM,gBAAgB;YACtB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,UAAU,CAAC,EAAE;gBAAE,MAAM;YAAc;YAEhF,IAAI,SAAS,IAAI,EAAE;gBACjB,qBAAqB,SAAS,IAAI;gBAElC,yCAAyC;gBACzC,MAAM,WAAoB,SAAS,IAAI,CAAC,SAAS,CAC9C,MAAM,CAAC,CAAC,WAAkB,SAAS,UAAU,KAAK,kBAAkB,SAAS,QAAQ,GAAG,KACxF,GAAG,CAAC,CAAC,UAAe;oBACnB,MAAM,WAAW,cAAc,OAAO,CAAC,SAAS,IAAI;oBACpD,OAAO;wBACL,IAAI,CAAC,GAAG,EAAE,OAAO;wBACjB,MAAM;wBACN,UAAU,SAAS,IAAI;wBACvB,YAAY,SAAS,IAAI;wBACzB,UAAU;4BACR,OAAO;4BACP,KAAK,WAAW,SAAS,IAAI,CAAC,MAAM;wBACtC;wBACA,aAAa,CAAC,gBAAgB,EAAE,CAAC,SAAS,QAAQ,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;oBACzE;gBACF;gBAEF,UAAU;gBAEV,2FAA2F;gBAC3F,aAAa,eAAe,SAAS,IAAI;YAC3C;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF;IAEA,0EAA0E;IAC1E,MAAM,eAAe;QACnB,IAAI;QACF,4CAA4C;QAC5C,0DAA0D;QAC5D,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,mEAAmE;IACnE,MAAM,iBAAiB;QACrB,IAAI;YACF,0EAA0E;YAC1E,UAAU,EAAE;QACd,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF;IAEA,yEAAyE;IACzE,MAAM,gBAAgB;QACpB,IAAI;YACF,0EAA0E;YAC1E,UAAU,EAAE;QACd,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,0EAA0E;IAC1E,MAAM,kBAAkB,CAAC;QACvB,kFAAkF;QAClF,0DAA0D;QAE1D,iCAAiC;QACjC,UAAU,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;QAE9C,oFAAoF;QACpF,IAAI,cAAc,mBAAmB,eAAe;YAClD,MAAM,qBAAqB,cAAc,WAAW,CAAC,MAAM,CACzD,CAAA,IAAK,CAAC,CAAC,EAAE,KAAK,KAAK,MAAM,QAAQ,CAAC,KAAK,IAAI,EAAE,GAAG,KAAK,MAAM,QAAQ,CAAC,GAAG;YAEzE,iBAAiB;gBACf,GAAG,aAAa;gBAChB,aAAa;YACf;YAEA,8DAA8D;YAC9D,IAAI,MAAM,EAAE,IAAI,aAAkB,eAAe,4BAA4B,QAAQ;gBAClF,OAAe,sBAAsB,CAAC,MAAM,EAAE;YACjD;QACF;IACF;IAEA,6FAA6F;IAE7F,+CAA+C;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAExE,sCAAsC;IACtC,MAAM,mBAAmB,CAAC;QACxB,EAAE,cAAc;QAChB,cAAc;QACd,MAAM,SAAS,EAAE,OAAO;QACxB,MAAM,kBAAkB;QACxB,MAAM,mBAAmB;QAEzB,MAAM,kBAAkB,CAAC;YACvB,MAAM,iBAAiB,SAAS,aAAa,CAAC,SAAS,eAAe;YACtE,MAAM,QAAQ,AAAC,CAAC,EAAE,OAAO,GAAG,MAAM,IAAI,iBAAkB;YAExD,mEAAmE;YACnE,MAAM,gBAAgB,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,kBAAkB;YAClE,MAAM,iBAAiB,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,mBAAmB;YAEpE,uBAAuB;YACvB,oBAAoB;YACpB,qBAAqB;QACvB;QAEA,MAAM,gBAAgB;YACpB,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;YACxC,cAAc;QAChB;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,WAAW;IACvC;IAEA,wEAAwE;IACxE,MAAM,oBAAoB,CAAC;QACzB,EAAE,cAAc;QAChB,cAAc;QACd,MAAM,SAAS,EAAE,OAAO;QACxB,MAAM,mBAAmB;QACzB,MAAM,uBAAuB;QAE7B,MAAM,kBAAkB,CAAC;YACvB,MAAM,iBAAiB,SAAS,aAAa,CAAC,SAAS,eAAe;YACtE,MAAM,QAAQ,AAAC,CAAC,EAAE,OAAO,GAAG,MAAM,IAAI,iBAAkB;YAExD,mEAAmE;YACnE,MAAM,iBAAiB,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,mBAAmB;YACpE,MAAM,qBAAqB,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,uBAAuB;YAE5E,uBAAuB;YACvB,qBAAqB;YACrB,yBAAyB;QAC3B;QAEA,MAAM,gBAAgB;YACpB,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;YACxC,cAAc;QAChB;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,WAAW;IACvC;IAEA,0EAA0E;IAC1E,MAAM,uBAAuB;QAC3B,kFAAkF;QAClF,0DAA0D;QAE1D,wBAAwB;QACxB,UAAU,EAAE;QAEZ,gDAAgD;QAChD,IAAI,cAAc,iBAAiB;YACjC,iBAAiB,CAAA,OAAQ,OAAO;oBAAC,GAAG,IAAI;oBAAE,aAAa,EAAE;gBAAA,IAAI;YAE7D,mEAAmE;YACnE,IAAI,aAAkB,eAAe,gCAAgC,QAAQ;gBAC1E,OAAe,0BAA0B;YAC5C;QACF;IACF;IAEA,4CAA4C;IAC5C,MAAM,cAAc;QAClB,QAAQ;QACR,UAAU,EAAE;QACZ,qBAAqB;QACrB,iBAAiB;QACjB,oBAAoB;QACpB,kBAAkB;QAClB,SAAS;QACT,kBAAkB,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACvB,iBAAiB;QACjB,mBAAmB;QACnB,mBAAmB,EAAE;IACvB;IAEA,6DAA6D;IAC7D,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,UAAU,EAAE;QAEZ,4CAA4C;QAC5C,qBAAqB;QACrB,iBAAiB;QACjB,oBAAoB;QACpB,kBAAkB;QAClB,SAAS;QAET,mDAAmD;QACnD,mBAAmB,EAAE;QACrB,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,oGAAoG;IACpG,MAAM,eAAe,CAAC,aAAiF;QACrG,MAAM,aAAa,cAAc,aAAa;QAE9C,sFAAsF;QACtF,MAAM,kBAAkB,cAAc,CAAC,EAAE;QACzC,MAAM,wBAAwB,mBAC5B,gBAAgB,WAAW,KAAK,eAChC,gBAAgB,SAAS,CAAC,IAAI,OAAO,KAAK,IAAI;QAEhD,IAAI,uBAAuB;YACzB,0DAA0D;YAC1D,MAAM,eAA6B;gBACjC,GAAG,eAAe;gBAClB,WAAW,IAAI;gBACf;gBACA;YACF;YAEA,kBAAkB,CAAA,OAAQ;oBAAC;uBAAiB,KAAK,KAAK,CAAC;iBAAG;QAC5D,OAAO;YACL,8DAA8D;YAC9D,MAAM,WAAyB;gBAC7B,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,WAAW,IAAI;gBACf;gBACA,WAAW;gBACX;gBACA;YACF;YAEA,qCAAqC;YACrC,kBAAkB,CAAA,OAAQ;oBAAC;uBAAa;iBAAK;QAC/C;QAEA,0EAA0E;QAC1E,mBAAmB,EAAE;QACrB,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,yBAAyB;IACzB,MAAM,uBAAuB;QAC3B,sBAAsB,CAAA,OAAQ,CAAC;IACjC;IAEA,wDAAwD;IACxD,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO,sBAAsB;YAC/B,KAAK;gBACH,OAAO,kBAAkB;YAC3B,KAAK;gBACH,OAAO,qBAAqB;YAC9B,KAAK;gBACH,OAAO,mBAAmB;YAC5B;gBACE,OAAO;QACX;IACF;IAEA,wCAAwC;IACxC,MAAM,gBAAgB,CAAC,aAAqB;QAC1C,OAAQ;YACN,KAAK;gBACH,OAAO,eAAe,kBAAkB;YAC1C,KAAK;gBACH,OAAO,eAAe,oBAAoB;YAC5C,KAAK;gBACH,OAAO,eAAe,WAAW;YACnC,KAAK;gBACH,OAAO,MAAM,kCAAkC;YACjD;gBACE,OAAO;QACX;IACF;IAIA,uFAAuF;IACvF,MAAM,wBAAwB;QAC5B,iEAAiE;QACjE,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,kBAAkB,cAAc,CAAC,EAAE,EAAE,uBAAuB;YAClE,IAAI,gBAAgB,UAAU,IAAI,gBAAgB,UAAU,KAAK,MAAM;gBACrE,OAAO,gBAAgB,UAAU;YACnC;QACF;QAEA,qCAAqC;QACrC,OAAO;IACT;IAEA,wDAAwD;IACxD,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,sBAAsB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;QAC5D,IAAI,OAAO;YACT,QAAQ,MAAM,SAAS;YACvB,iBAAiB;YACjB,mBAAmB;YACnB,mBAAmB,MAAM,QAAQ;YAEjC,gDAAgD;YAChD,qBAAqB;YACrB,iBAAiB;YACjB,oBAAoB;YACpB,kBAAkB;YAClB,UAAU,EAAE;QACd;IACF;IAIA,gEAAgE;IAChE,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,OAAO;YACT,QAAQ,MAAM,SAAS;YACvB,mBAAmB;YACnB,mBAAmB;gBAAC;aAAM;YAE1B,sCAAsC;YACtC,MAAM,aAAa,sBAAsB,IAAI,CAAC,CAAA,QAC5C,MAAM,SAAS,CAAC,IAAI,OAAO,MAAM,SAAS,CAAC,IAAI;YAEjD,IAAI,YAAY;gBACd,iBAAiB,WAAW,OAAO;YACrC;YAEA,0CAA0C;YAC1C,aAAa,MAAM,WAAW;YAE9B,kEAAkE;YAClE,OAAQ,MAAM,WAAW;gBACvB,KAAK;oBACH,qBAAqB,MAAM,aAAa;oBACxC;gBACF,KAAK;oBACH,iBAAiB,MAAM,aAAa;oBACpC,IAAI,MAAM,aAAa,EAAE,aAAa;wBACpC,MAAM,YAAY,MAAM,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,aAAoB,CAAC;gCAC1E,IAAI,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,MAAM,IAAI;gCAC/C,MAAM;gCACN,UAAU,WAAW,SAAS;gCAC9B,YAAY,WAAW,UAAU;gCACjC,UAAU;oCACR,OAAO,WAAW,KAAK;oCACvB,KAAK,WAAW,GAAG;gCACrB;gCACA,aAAa,WAAW,WAAW;gCACnC,UAAU,WAAW,QAAQ;4BAC/B,CAAC;wBACD,UAAU;oBACZ;oBACA;gBACF,KAAK;oBACH,oBAAoB,MAAM,aAAa;oBACvC;gBACF,KAAK;oBACH,kBAAkB,MAAM,aAAa;oBACrC;YACJ;QACF;IACF;IAEA,8CAA8C;IAC9C,MAAM,aAAa,CAAC;QAClB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,mDAAmD;IACnD,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,gDAAgD;IAChD,MAAM,sBAAsB;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,qBAAO,6LAAC;8BAAE;;;;;;QACd;IACF;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B;QAC9B,IAAI,CAAC,mBAAmB,qBAAO,6LAAC;sBAAE;;;;;;QAElC,MAAM,eAAe,kBAAkB,QAAQ,GAAG;QAElD,qBACE,6LAAC;;8BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsB;;;;;;sCACpC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAW,GAAG,eAAe,KAAK,iBAAiB,eAAe,KAAK,oBAAoB,kBAAkB;;oCAChH,aAAa,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;8BAK/B,6LAAC;oBAAI,WAAU;8BACZ,kBAAkB,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU;wBAC1C,MAAM,OAAO,SAAS,UAAU,KAAK,kBAAkB,SAAS,QAAQ,GAAG;wBAC3E,qBACE,6LAAC;4BAAiB,WAAW,OAAO,+BAA+B;;gCAChE,SAAS,IAAI;gCAAE;;2BADP;;;;;oBAIf;;;;;;8BAGF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAW,CAAC,OAAO,EAAE,eAAe,KAAK,eAAe,eAAe,KAAK,kBAAkB,gBAAgB;gCAC9G,OAAO;oCAAE,OAAO,GAAG,aAAa,CAAC,CAAC;gCAAC;;;;;;;;;;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,8BAA8B;IAC9B,MAAM,sBAAsB;QAC1B,MAAM,gBAAgB;QACtB,qBACE,6LAAC,kKAAA,CAAA,UAAqB;YACpB,MAAM;YACN,iBAAiB;YACjB,mBAAmB,CAAC,OAAS,mBAAmB;YAChD,sBAAsB,CAAC;gBACrB,yCAAyC;gBACzC,oDAAoD;gBACpD,UAAU,EAAE;YACd;YACA,oBAAoB,CAAC;gBACnB,4BAA4B;gBAC5B,MAAM,YAAY,YAAY,GAAG,CAAC,CAAC,aAAe,CAAC;wBACjD,IAAI,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,MAAM,IAAI;wBAC/C,MAAM;wBACN,UAAU,WAAW,SAAS;wBAC9B,YAAY,WAAW,UAAU;wBACjC,UAAU;4BACR,OAAO,WAAW,KAAK;4BACvB,KAAK,WAAW,GAAG;wBACrB;wBACA,aAAa,WAAW,WAAW;wBACnC,UAAU,WAAW,QAAQ;oBAC/B,CAAC;gBAED,MAAM,SAAS;oBACb,aAAa;oBACb,gBAAgB;gBAClB;gBAEA,UAAU;gBACV,iBAAiB;gBAEjB,iBAAiB;gBACjB,aAAa,iBAAiB;YAChC;;;;;;IAGN;IAEA,6DAA6D;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,qCAAqC;YACrC,mBAAmB,cAAc;QACnC;iCAAG;QAAC;KAAU;IAEd,yDAAyD;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IAEA,mFAAmF;IACnF,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,4EAA4E;IAC5E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,iBAAiB,OAAO,EAAE;YAC9B,iBAAiB,OAAO,GAAG;YAE3B,IAAI;gBACF,IAAI,mBAAmB,cAAc,iBAAiB;oBACpD,iFAAiF;oBACjF,IAAI,iBAAiB,KAAK,GAAG,iBAAiB,MAAM,GAAG,iBAAiB,UAAU,KAAK,KAAK;wBAC1F,oBAAoB,iBAAiB,KAAK;wBAC1C,qBAAqB,iBAAiB,MAAM;wBAC5C,yBAAyB,iBAAiB,UAAU;oBACtD,OAAO;wBACL,4DAA4D;wBAC5D,oBAAoB;wBACpB,qBAAqB;wBACrB,yBAAyB;oBAC3B;gBACF,OAAO,IAAI,AAAC,cAAc,mBAAmB,CAAC,mBACnC,cAAc,iBACd,cAAc,iBACd,cAAc,kBAAkB;oBACzC,iGAAiG;oBACjG,IAAI,mBAAmB,cAAc,iBAAiB;wBACpD,oBAAoB;4BAClB,OAAO;4BACP,QAAQ;4BACR,YAAY;wBACd;oBACF;oBAEA,2CAA2C;oBAC3C,oBAAoB;oBACpB,qBAAqB;gBACvB;YACF,SAAU;gBACR,iBAAiB,OAAO,GAAG;YAC7B;QACF;iCAAG;QAAC;QAAiB;KAAU;IAE/B,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,MAAM,gBAAgB;QACtB,qBACE,6LAAC,wJAAA,CAAA,UAAW;YACV,MAAM;YACN,eAAe,CAAC;gBACd,MAAM,SAAS;oBACb,kBAAkB;gBACpB;gBAEA,IAAI,kBAAkB;oBACpB,oBAAoB;wBAClB,GAAG,gBAAgB;wBACnB,kBAAkB;oBACpB;gBACF,OAAO;oBACL,oBAAoB;gBACtB;gBAEA,iBAAiB;gBACjB,aAAa,eAAe;YAC9B;;;;;;IAGN;IAEA,+BAA+B;IAC/B,MAAM,uBAAuB;QAC3B,MAAM,gBAAgB;QACtB,qBACE,6LAAC,sKAAA,CAAA,UAAyB;YACxB,MAAM;YACN,oBAAoB,CAAC;gBACnB,kBAAkB;gBAElB,iBAAiB;gBACjB,aAAa,kBAAkB;YACjC;;;;;;IAGN;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YACV,UAAU;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;QACA,WAAW,CAAC,uFAAuF,EAAE,aAAa,qBAAqB,IAAI;kBAG3I,cAAA,6LAAC;YAAK,WAAU;;8BAEd,6LAAC,uKAAA,CAAA,UAA0B;oBACzB,WAAW;oBACX,eAAe;oBACf,gBAAgB;oBAChB,eAAe;oBACf,iBAAiB;oBACjB,cAAc;oBACd,gBAAgB;oBAChB,eAAe;;;;;;8BAGjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAW,CAAC,sCAAsC,EAChD,eAAe,UAAU,iCAAiC,IAC1D;oBACF,OAAO;wBACL,OAAO,GAAG,iBAAiB,CAAC,CAAC;oBAC/B;;sCAGA,6LAAC;4BACC,WAAU;4BACV,aAAa;;;;;;sCAEf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsB;;;;;;gCACnC,eAAe,yBACd,6LAAC;oCAAK,WAAU;;wCACb,KAAK,KAAK,CAAC;wCAAkB;;;;;;;;;;;;;sCAIpC,6LAAC;4BAAI,WAAU;4BAAqE,OAAO;gCAAE,QAAQ;4BAAsB;;8CACzH,6LAAC;oCACC,WAAU;oCACV,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CAAI,WAAU;;gDACZ;gDAAU;;;;;;;;;;;;;;;;;;;sCAMjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,4CAA4C,EAAE,cAAc,gBAAgB,8BAA8B,6BAA6B;;wCAElJ,WAAW;wCAAe;;;;;;;8CAE7B,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,4CAA4C,EAAE,cAAc,kBAAkB,8BAA8B,6BAA6B;;wCAEpJ,WAAW;wCAAiB;;;;;;;8CAE/B,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,4CAA4C,EAAE,cAAc,gBAAgB,8BAA8B,6BAA6B;;wCAElJ,WAAW;wCAAe;;;;;;;8CAE7B,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,4CAA4C,EAAE,cAAc,mBAAmB,8BAA8B,6BAA6B;;wCAErJ,WAAW;wCAAkB;;;;;;;;;;;;;;;;;;;8BAMpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAW,CAAC,aAAa,EACvB,cAAc,iBAAiB,cAAc,gBAAgB,KAAK,2BACnE,CAAC,EACA,eAAe,WAAW,iCAAiC,IAC3D;oBACF,OAAO;wBACL,OAAO,GAAG,kBAAkB,CAAC,CAAC;oBAChC;;wBAGC,mBAAmB,cAAc,iCAChC,6LAAC;4BACC,WAAU;4BACV,aAAa;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,6LAAC;oCAAI,WAAU;;wCACZ,eAAe,0BACd,6LAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK,CAAC;gDAAmB;;;;;;;wCAGlC,cAAc,mBAAmB,CAAC,iCACjC,6LAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,6LAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;8DACxK,cAAA,6LAAC;wDAAS,QAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BAAI,WAAU;4BAAqE,OAAO;gCAAE,QAAQ;4BAAsB;sCAExH,gBAAgB,MAAM,GAAG,KAAK,CAAC,eAAe,CAAC,oCAC9C,6LAAC,6JAAA,CAAA,UAAgB;gCACf,SAAS;gCACT,WAAW;gCACX,OAAO;;;;;uCAEP,cAAc,8BAChB,6LAAC,kKAAA,CAAA,UAAqB;gCACpB,WAAW;gCACX,aAAa;gCACb,MAAM;gCACN,WAAW;gCACX,mBAAmB;gCACnB,OAAO;;;;;qDAGT,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,MAAM;wCAAE,SAAS;oCAAE;oCACnB,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAET,sBACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,6LAAC;0DAAG;;;;;;;;;;;6DAGN,6LAAC;wCAAI,WAAU;kDACZ;;;;;;mCAdA;;;;;;;;;;;;;;;;;;;;;8BA0Bf,6LAAC,4LAAA,CAAA,kBAAe;8BACb,mBAAmB,cAAc,iCAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,SAAS;4BAAE,SAAS;4BAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC;wBAAC;wBAC1D,MAAM;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAC7B,YAAY;4BAAE,UAAU;4BAAK,MAAM;4BAAU,WAAW;wBAAI;wBAC5D,WAAW,CAAC,SAAS,EAAE,eAAe,WAAW,iCAAiC,IAAI;kCAIxF,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;;gDACZ,eAAe,0BACd,6LAAC;oDAAK,WAAU;;wDACb,KAAK,KAAK,CAAC;wDAAuB;;;;;;;8DAGvC,6LAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAML,6LAAC,4LAAA,CAAA,kBAAe;8CACb,OAAO,MAAM,GAAG,mBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,iJAAA,CAAA,kBAAe;;;;;;;;;;0DAElB,6LAAC;0DACC,cAAA,6LAAC;oDAAK,WAAU;;wDAAc;wDAAO,OAAO,MAAM;wDAAC;wDAAE,OAAO,MAAM,KAAK,IAAI,UAAU;;;;;;;;;;;;0DAEvF,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAQP,6LAAC;oCAAI,WAAU;oCAAoD,OAAO;wCAAE,QAAQ;oCAAsB;;sDACxG,6LAAC,4LAAA,CAAA,kBAAe;sDACb,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,QAAQ;oDAAE;oDACjC,SAAS;wDAAE,SAAS;wDAAG,QAAQ;oDAAO;oDACtC,MAAM;wDAAE,SAAS;wDAAG,QAAQ;oDAAE;oDAC9B,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,WAAU;;wDAET,MAAM,QAAQ,iBACb;;8EACE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAW,CAAC,0BAA0B,EAC1C,MAAM,QAAQ,KAAK,iBAAiB,MAAM,QAAQ,KAAK,gBACnD,eACA,MAAM,QAAQ,KAAK,YACnB,gBACA,MAAM,QAAQ,KAAK,eACnB,kBACA,gBACJ;;;;;;sFACF,6LAAC;4EAAK,WAAU;sFACb,MAAM,QAAQ,KAAK,gBAChB,gBACA,MAAM,QAAQ,KAAK,gBACnB,gBACA,MAAM,QAAQ,KAAK,YACnB,YACA,MAAM,QAAQ,KAAK,eACnB,eACA;;;;;;;;;;;;8EAGR,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFACV,MAAM,QAAQ;;;;;;;;;;;;8EAGnB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFACV,MAAM,UAAU;;;;;;;;;;;;;yFAKvB;;8EACE,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;kFACb,MAAM,QAAQ;;;;;;;;;;;8EAGnB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;kFAA8B,MAAM,UAAU;;;;;;;;;;;;;wDAInE,MAAM,WAAW,IAAI,MAAM,IAAI,KAAK,8BACnC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAC1C,6LAAC;oEAAE,WAAU;8EACV,MAAM,WAAW;;;;;;;;;;;;sEAIxB,6LAAC;4DAAI,WAAU;;gEACZ,MAAM,IAAI,KAAK,8BACd,6LAAC;oEACC,SAAS,IAAM,gBAAgB;oEAC/B,WAAU;;sFAEV,6LAAC,iJAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAAS;;;;;;;8EAGhC,6LAAC;oEACC,SAAS,IAAM,UAAU,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;oEAC7D,WAAU;;sFAEV,6LAAC,iJAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAS;wEAAE,MAAM,IAAI,KAAK,eAAe,YAAY;;;;;;;;;;;;;;mDA7EnE,MAAM,EAAE;;;;;;;;;;wCAoFlB,OAAO,MAAM,KAAK,KAAK,CAAC,6BACvB,6LAAC;4CAAI,WAAU;sDACZ,cAAc,mBAAmB,8BAChC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iJAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;qEAIlC;;kEACE,6LAAC;kEAAE;;;;;;kEACH,6LAAC;wDAAE,WAAU;kEACV,cAAc;;;;;;;;;;;;;wCAOxB,6BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAAqC,OAAM;oDAA6B,MAAK;oDAAO,SAAQ;;sEACzG,6LAAC;4DAAO,WAAU;4DAAa,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;;;;;;sEACxF,6LAAC;4DAAK,WAAU;4DAAa,MAAK;4DAAe,GAAE;;;;;;;;;;;;8DAErD,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtD;GA9kCM;KAAA;uCAglCS", "debugId": null}}]}