import requests
import json
import re
from http import HTTPStatus
import logging
from modules.config import Config
from modules.text_utils import extract_json

logger = logging.getLogger(__name__)


## API for grammar checking asnd paraphrasing
def call_openrouter_api(prompt, system_instruction):
    """
    Call OpenRouter AI API with retry logic and return parsed response.
    """
    api_key = Config.OPENROUTER_API_KEY
    if not api_key:
        logger.error("OPENROUTER_API_KEY not found")
        return {"error": "Server configuration error: API key missing"}, HTTPStatus.INTERNAL_SERVER_ERROR

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "User-Agent": "Paraphrase-API/1.0"
    }
    data = {
        # "model": "openai/chatgpt-4o-latest",
        "model": "google/gemini-2.5-flash-preview-05-20",
        "messages": [
            {"role": "system", "content": system_instruction},
            {"role": "user", "content": prompt},
        ],
        "temperature": 0.2,
        "max_tokens": 10000,
    }

    max_retries = 2
    for attempt in range(max_retries):
        try:
            resp = requests.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=Config.API_TIMEOUT
            )
            resp.raise_for_status()
            result = resp.json()
            content = result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            
            if not content:
                logger.warning("Empty response from AI")
                return {"error": "Empty response from AI"}, HTTPStatus.INTERNAL_SERVER_ERROR
            
            # Try direct JSON parsing first
            json_str = extract_json(content)
            try:
                parsed = json.loads(json_str)
                if not isinstance(parsed, dict) or ("corrections" not in parsed and "paraphrases" not in parsed):
                    raise ValueError("Invalid response structure")
                logger.debug("Parsed JSON: %s", parsed)
                return parsed, HTTPStatus.OK
            except json.JSONDecodeError:
                if attempt < max_retries - 1:
                    data["messages"].append({"role": "assistant", "content": content})
                    data["messages"].append({
                        "role": "user",
                        "content": "Your response wasn't valid JSON or didn't follow the required structure. Please provide the response in the exact JSON format specified in the system instructions."
                    })
                    continue
                logger.error("Unable to parse AI response as valid JSON: %s", json_str)
                return {"error": "Unable to parse AI response as valid JSON"}, HTTPStatus.INTERNAL_SERVER_ERROR

        except requests.exceptions.Timeout:
            logger.error("API request timed out")
            return {"error": "Service timed out, please try again later"}, HTTPStatus.GATEWAY_TIMEOUT
        except requests.exceptions.RequestException as e:
            if attempt == max_retries - 1:
                logger.error("API request failed after retries: %s", str(e))
                return {"error": "External service unavailable"}, HTTPStatus.SERVICE_UNAVAILABLE
            continue
        except Exception as e:
            logger.error("Unexpected error: %s", str(e))
            return {"error": "Internal server error"}, HTTPStatus.INTERNAL_SERVER_ERROR
        
        
    
def call_openrouter_api_stream(prompt, system_instruction):
    """
    Properly stream parsed assistant content from OpenRouter API.
    """
    api_key = Config.OPENROUTER_API_KEY
    if not api_key:
        logger.error("OPENROUTER_API_KEY not found")
        yield "Server configuration error: API key missing"
        return

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "User-Agent": "Paraphrase-API/1.0"
    }

    data = {
        "model": "google/gemini-2.5-flash-preview-05-20",
        "messages": [
            {"role": "system", "content": system_instruction},
            {"role": "user", "content": prompt},
        ],
        "temperature": 0.2,
        "max_tokens": 5000,
        "stream": True  # important for OpenRouter
    }

    try:
        with requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=data,
            stream=True,
            timeout=Config.API_TIMEOUT
        ) as response:
            response.raise_for_status()

            for line in response.iter_lines(decode_unicode=True):
                if not line or not line.startswith("data:"):
                    continue

                # Remove 'data:' prefix and parse JSON
                line = line[5:].strip()
                if line == "[DONE]":
                    break

                try:
                    payload = json.loads(line)
                    delta = payload.get("choices", [{}])[0].get("delta", {})
                    message_content = delta.get("content")
                    if message_content:
                        yield message_content
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse chunk: {line}")
                    continue

    except requests.exceptions.Timeout:
        logger.error("Streaming API request timed out")
        yield "Service timed out, please try again later"
    except requests.exceptions.RequestException as e:
        logger.error("Streaming API request error: %s", str(e))
        yield "External service unavailable"
    except Exception as e:
        logger.error("Unexpected streaming error: %s", str(e))
        yield "Internal server error"
