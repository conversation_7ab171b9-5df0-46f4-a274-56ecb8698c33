'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaLightbulb, FaShieldAlt, FaExclamationTriangle } from 'react-icons/fa';
import { ThumbsUpIcon, ThumbsDownIcon, TrendingUpIcon, TrendingDownIcon } from 'lucide-react';

interface AISentence {
  ai_score: number;
  prediction: 'original' | 'ai-generated';
  text: string;
}

interface AIDetectionResult {
  ai_score: number;
  classification: string;
  human_score: number;
  sentences: AISentence[];
}

interface ResultPanelProps {
  activeTab: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser';
  isAnalyzing: boolean;
  text: string;
  onAnalyze: () => void;
  aiDetectionResult?: AIDetectionResult | null;
  error?: string | null;
}

const ProgressBar = ({ label, percentage, color }: { label: string; percentage: number; color: string }) => (
  <div>
    <div className="flex justify-between text-sm font-medium text-gray-700 mb-2">
      <span>{label}</span>
      <span>{percentage.toFixed(1)}%</span>
    </div>
    <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
      <div className={`h-full ${color} transition-all duration-500`} style={{ width: `${percentage}%` }} />
    </div>
  </div>
);

const ResultPanel: React.FC<ResultPanelProps> = ({
  activeTab,
  isAnalyzing,
  text,
  onAnalyze,
  aiDetectionResult = null,
  error = null,
}) => {
  const [feedbackSubmitted, setFeedbackSubmitted] = React.useState(false);

  const handleFeedback = (positive: boolean) => {
    setFeedbackSubmitted(true);
    console.log(`Feedback: ${positive ? "positive" : "negative"}`);
  };

  // Calculate statistics for AI analysis
  const sentences = aiDetectionResult?.sentences || [];
  const aiSegments = sentences.filter((s) => s.prediction === 'ai-generated').length;
  const humanSegments = sentences.length - aiSegments;
  const total = sentences.length;
  const humanPercentage = aiDetectionResult?.human_score || 0;
  const aiPercentage = aiDetectionResult?.ai_score || 0;

  const renderAIDetectorResult = () => {
    if (isAnalyzing) {
      return (
        <div className="flex items-center justify-center h-64">
          <FaSpinner className="animate-spin text-3xl text-gray-500" />
          <span className="ml-3 text-gray-600">Analyzing text...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-red-600 p-4 bg-red-50 rounded-lg border border-red-200">
          <h4 className="font-medium mb-2">Analysis Error</h4>
          <p>{error}</p>
        </div>
      );
    }

    if (!sentences.length) {
      return (
        <div className="text-center text-gray-500 py-8">
          <FaLightbulb className="text-4xl mx-auto mb-4 text-gray-400" />
          <p className="text-lg font-medium mb-2">No Analysis Yet</p>
          <p>Click "Analyze" to detect AI-generated content</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Overall Score with Classification */}
        <div className="text-center bg-gradient-to-br from-indigo-50 to-blue-50 rounded-xl p-6 border border-indigo-100">
          <div className="flex items-center justify-center mb-4">
            {aiDetectionResult?.classification === 'Human-Written' ? (
              <FaShieldAlt className="text-3xl text-green-600 mr-3" />
            ) : (
              <FaExclamationTriangle className="text-3xl text-red-600 mr-3" />
            )}
            <div>
              <div className="text-4xl font-bold text-gray-800">
                {aiPercentage.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600 font-medium">AI Probability</div>
            </div>
          </div>

          <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium mb-3 ${
            aiDetectionResult?.classification === 'Human-Written'
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {aiDetectionResult?.classification || 'Unknown'}
          </div>

          {/* Confidence Indicator */}
          <div className="text-xs text-gray-500">
            Confidence: {aiPercentage < 10 ? 'Very High' : aiPercentage < 30 ? 'High' : aiPercentage < 70 ? 'Medium' : 'Low'}
            {aiPercentage < 10 && ' - Highly likely to be human-written'}
            {aiPercentage >= 70 && ' - Requires manual review'}
          </div>
        </div>

        {/* Progress Bars */}
        <div className="space-y-4">
          <ProgressBar label="Human Content" percentage={humanPercentage} color="bg-teal-500" />
          <ProgressBar label="AI Content" percentage={aiPercentage} color="bg-yellow-500" />
        </div>

        {/* Summary Statistics */}
        <div className="bg-gray-50 rounded-lg p-4 border">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Analysis Summary</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Sentences:</span>
                <span className="font-medium">{sentences.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Human-Written:</span>
                <span className="font-medium text-teal-600">
                  {sentences.filter(s => s.prediction === 'original').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">AI-Generated:</span>
                <span className="font-medium text-red-600">
                  {sentences.filter(s => s.prediction === 'ai-generated').length}
                </span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Avg AI Score:</span>
                <span className="font-medium">
                  {sentences.length > 0 ?
                    (sentences.reduce((sum, s) => sum + s.ai_score, 0) / sentences.length).toFixed(2) + '%'
                    : '0%'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Highest Score:</span>
                <span className="font-medium">
                  {sentences.length > 0 ?
                    Math.max(...sentences.map(s => s.ai_score)).toFixed(2) + '%'
                    : '0%'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Risk Level:</span>
                <span className={`font-medium ${
                  aiPercentage < 10 ? 'text-green-600' :
                  aiPercentage < 30 ? 'text-yellow-600' :
                  aiPercentage < 70 ? 'text-orange-600' : 'text-red-600'
                }`}>
                  {aiPercentage < 10 ? 'Very Low' :
                   aiPercentage < 30 ? 'Low' :
                   aiPercentage < 70 ? 'Medium' : 'High'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Sentence-by-Sentence Analysis */}
        <div className="border-t pt-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center justify-between">
            <div className="flex items-center">
              <FaLightbulb className="mr-2 text-indigo-500" />
              Sentence Analysis ({sentences.length} sentences)
            </div>
            <div className="text-xs text-gray-500">
              {sentences.filter(s => s.prediction === 'ai-generated').length} flagged
            </div>
          </h3>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {sentences.map((sentence, index) => {
              const isAI = sentence.prediction === 'ai-generated';
              const scorePercentage = sentence.ai_score;

              // Determine confidence level based on score (0-100 scale)
              const getConfidenceLevel = (score: number) => {
                if (score < 0.001) return 'Extremely Low';
                if (score < 0.1) return 'Very Low';
                if (score < 1) return 'Low';
                if (score < 10) return 'Medium';
                if (score < 50) return 'High';
                return 'Very High';
              };

              const confidenceLevel = getConfidenceLevel(scorePercentage);

              return (
                <div key={index} className={`p-4 rounded-lg border transition-all hover:shadow-sm ${
                  isAI ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'
                }`}>
                  <div className="flex items-start gap-3">
                    <div className={`w-4 h-4 rounded-full mt-1 flex-shrink-0 flex items-center justify-center ${
                      isAI ? 'bg-red-500' : 'bg-teal-500'
                    }`}>
                      <span className="text-white text-xs font-bold">{index + 1}</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium text-gray-800">
                          {scorePercentage < 0.001 ? '<0.001' :
                           scorePercentage < 0.1 ? scorePercentage.toFixed(3) :
                           scorePercentage.toFixed(1)}%
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                          isAI ? 'bg-red-100 text-red-700' : 'bg-teal-100 text-teal-700'
                        }`}>
                          {isAI ? 'AI-Generated' : 'Human-Written'}
                        </span>
                        <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600">
                          {confidenceLevel} Risk
                        </span>
                      </div>
                      <p className="text-gray-700 leading-relaxed mb-2">
                        "{sentence.text}"
                      </p>
                      {scorePercentage < 0.001 && (
                        <div className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">
                          ✓ Extremely low AI probability - Strong human writing indicators
                        </div>
                      )}
                      {isAI && (
                        <div className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded">
                          ⚠ Flagged for potential AI generation - Manual review recommended
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Feedback Section */}
        <div className="border-t pt-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Was this analysis helpful?</h3>
          <div className="flex gap-3">
            <button
              onClick={() => handleFeedback(true)}
              disabled={feedbackSubmitted}
              className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:bg-gray-400 transition-all"
            >
              <ThumbsUpIcon className="w-4 h-4" /> Yes
            </button>
            <button
              onClick={() => handleFeedback(false)}
              disabled={feedbackSubmitted}
              className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:bg-gray-400 transition-all"
            >
              <ThumbsDownIcon className="w-4 h-4" /> No
            </button>
          </div>
          {feedbackSubmitted && (
            <p className="text-sm text-teal-600 mt-3 font-medium">✓ Thanks for your feedback!</p>
          )}
        </div>
      </div>
    );
  };

  const renderOtherFeatures = () => {
    return (
      <div className="text-center text-gray-500 py-8">
        <FaLightbulb className="text-4xl mx-auto mb-4 text-gray-400" />
        <p className="text-lg font-medium mb-2">Select a Feature</p>
        <p>Choose {activeTab.replace('-', ' ')} and click "Analyze Text" to see results</p>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-md p-6 h-full overflow-y-auto">
      {/* Show Analyze button for all features except AI detector */}
      {activeTab !== 'ai-detector' && (
        <div className="mb-6">
          <button
            onClick={onAnalyze}
            disabled={isAnalyzing || text.trim() === ''}
            className="w-full flex items-center justify-center gap-2 bg-indigo-600 text-white px-4 py-3 rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 transition-all font-medium"
          >
            <FaSpinner className={isAnalyzing ? "animate-spin" : "hidden"} />
            {isAnalyzing ? "Analyzing..." : "Analyze Text"}
          </button>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">
          {activeTab === 'ai-detector' ? 'AI Detection Results' :
           activeTab === 'grammar-check' ? 'Grammar Check Results' :
           activeTab === 'paraphraser' ? 'Paraphrasing Results' :
           activeTab === 'criteria-check' ? 'Criteria Check Results' : 'Analysis Results'}
        </h2>
        {/* Show Analyze button only for AI detector in header */}
        {activeTab === 'ai-detector' && (
          <button
            onClick={onAnalyze}
            disabled={isAnalyzing || text.trim() === ''}
            className="flex items-center gap-2 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 transition-all"
          >
            <FaSpinner className={isAnalyzing ? "animate-spin" : "hidden"} />
            {isAnalyzing ? "Analyzing..." : "Analyze"}
          </button>
        )}
      </div>

      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.3 }}
        className="h-full"
      >
        {activeTab === 'ai-detector' ? renderAIDetectorResult() : renderOtherFeatures()}
      </motion.div>
    </div>
  );
};

export default ResultPanel;
