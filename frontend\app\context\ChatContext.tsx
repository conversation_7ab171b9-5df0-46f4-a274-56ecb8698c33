'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

type ChatMode = 'criteria' | 'detector' | 'grammar' | 'paraphraser';

interface Message {
  role: 'user' | 'assistant';
  content: string;
}

interface ChatContextType {
  messages: Message[];
  mode: ChatMode;
  isLoading: boolean;
  error: string | null;
  setMode: (mode: ChatMode) => void;
  sendMessage: (content: string) => Promise<void>;
  clearMessages: () => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export function ChatProvider({ children }: { children: React.ReactNode }) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [mode, setMode] = useState<ChatMode>('criteria');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return;
    
    setIsLoading(true);
    setError(null);
    
    // Add user message immediately
    setMessages(prev => [...prev, { role: 'user', content }]);
    
    try {
      const urlMap = {
        criteria: '/generate',
        detector: '/detect-ai',
        grammar: '/grammar-check',
        paraphraser: '/paraphrasing'
      };
      
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}${urlMap[mode]}`, 
        { prompt: content, text: content }, // Support both prompt and text fields
        { withCredentials: true }
      );
      
      // Add assistant response
      let responseContent = '';
      
      if (mode === 'detector') {
        const { ai_score } = response.data;
        responseContent = `AI Detection Score: ${ai_score * 100}%\n\n${response.data.analysis || ''}`;
      } else if (mode === 'grammar') {
        responseContent = response.data.corrected_text || response.data.content || '';
      } else {
        responseContent = response.data.content || response.data.text || '';
      }
      
      setMessages(prev => [...prev, { role: 'assistant', content: responseContent }]);
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const clearMessages = () => {
    setMessages([]);
    setError(null);
  };

  return (
    <ChatContext.Provider value={{
      messages,
      mode,
      isLoading,
      error,
      setMode,
      sendMessage,
      clearMessages
    }}>
      {children}
    </ChatContext.Provider>
  );
}

export function useChat() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
}