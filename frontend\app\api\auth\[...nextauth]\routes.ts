// app/auth/[...nextauth]/routes.ts
import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import axios from 'axios';

export const { handlers, auth } = NextAuth({
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        username: { label: 'Username', type: 'text' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        try {
          const res = await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/login`, {
            username: credentials?.username,
            password: credentials?.password,
          });
          const userData = res.data;
          if (userData.user_id) {
            return {
              id: userData.user_id, // Ensure id is returned
              name: credentials?.username,
              email: credentials?.username,
            };
          }
          return null;
        } catch (error) {
          console.error('Login error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async session({ session, token }) {
      // Since we've defined user.id as required in the type, this is safe
      session.user.id = token.sub as string; // token.sub is the user id from authorize
      return session;
    },
  },
  pages: {
    signIn: '/login',
  },
});