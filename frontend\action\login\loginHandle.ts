import axios from "axios";

type apihandle = {
    username: string;
    password: string;
}

export default async function loginHandle({username, password}: apihandle) {
    try {
        // First check if already logged in
        const authCheck = await axios.get(
            `${process.env.NEXT_PUBLIC_API_URL}/check-auth`,
            { withCredentials: true }
        );
        
        // If already logged in, return success
        if (authCheck.data?.authenticated) {
            return 'success';
        }
        
        // If not logged in, attempt login
        const res = await axios.post(
            `${process.env.NEXT_PUBLIC_API_URL}/login`,
            { username, password },
            { withCredentials: true } // Important for cookies
        );
        
        if (res.status === 200) {
            return 'success';
        }
        return "false";
    } catch (error: any) {
        console.error("Login error:", error);
        // Return more specific error message if available
        if (error.response?.data?.error) {
            return `Error: ${error.response.data.error}`;
        }
        return "Error: " + error;
    }
}
