'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { api } from '../utils/api';

type AuthView = 'login' | 'signup' | 'forgot' | null;

interface User {
  id: string;
  username: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  view: AuthView;
  openAuth: (view: AuthView) => void;
  closeAuth: () => void;
  login: (username: string, password: string) => Promise<void>;
  register: (username: string, password: string, email: string) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [view, setView] = useState<AuthView>(null);
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const res = await api.checkAuth();
      if (res.data?.authenticated) {
        setUser({
          id: res.data.user_id || '0',
          username: res.data.username || 'User'
        });
      } else {
        setUser(null);
      }
    } catch (err) {
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const openAuth = (newView: AuthView) => {
    setView(newView);
    setError(null);
  };

  const closeAuth = () => {
    setView(null);
    setError(null);
  };

  const login = async (username: string, password: string) => {
    setError(null);
    try {
      const res = await api.login(username, password);
      setUser({
        id: res.data.user_id || '0',
        username: res.data.username || username
      });
      closeAuth();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Login failed');
    }
  };

  const register = async (username: string, password: string, email: string) => {
    setError(null);
    try {
      const res = await api.register(username, password, email);
      setUser({
        id: res.data.user_id || '0',
        username
      });
      closeAuth();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Registration failed');
    }
  };

  const logout = async () => {
    try {
      await api.logout();
    } finally {
      setUser(null);
      // Clear cookies as a fallback
      document.cookie = 'session_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      router.push('/');
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      isLoading,
      error,
      view,
      openAuth,
      closeAuth,
      login,
      register,
      logout
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthProvider;