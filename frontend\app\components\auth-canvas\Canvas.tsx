'use client';

import React, { useEffect, useRef } from 'react';
import { useAuth } from '../../context/AuthContext';
;
import { FiX } from 'react-icons/fi';
import LoginForm from './LoginForm';
import SignupForm from './SignupForm';
import ForgotForm from './ForgotForm';

export default function Canvas() {
  const { view, closeAuth } = useAuth();
  const canvasRef = useRef<HTMLDivElement>(null);

  // Handle ESC key to close
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && view) {
        closeAuth();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [view, closeAuth]);

  // Trap focus within modal when open
  useEffect(() => {
    if (!view) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const focusableElements = canvas.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus();
          e.preventDefault();
        }
      }
    };

    canvas.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      canvas.removeEventListener('keydown', handleTabKey);
    };
  }, [view]);

  if (!view) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div 
        ref={canvasRef}
        className="bg-white rounded-xl shadow-xl w-full max-w-md p-6 relative"
        role="dialog"
        aria-modal="true"
      >
        <button 
          onClick={closeAuth}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
          aria-label="Close"
        >
          <FiX size={24} />
        </button>
        
        {view === 'login' && <LoginForm />}
        {view === 'signup' && <SignupForm />}
        {view === 'forgot' && <ForgotForm />}
      </div>
    </div>
  );
}