'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { FiFileText, FiEdit3, FiCheck, FiSearch, FiClock } from 'react-icons/fi';
import { format } from 'date-fns';

export interface HistoryEntry {
  id: string;
  timestamp: Date;
  featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser';
  inputText: string;
  resultContent: any;
  outputText?: string; // The modified/processed text from this feature
}

export interface GroupedHistoryEntry {
  inputText: string;
  inputId: string;
  timestamp: Date; // Most recent feature timestamp for this input
  features: HistoryEntry[];
}

interface HistoryEntryProps {
  entry: HistoryEntry;
  isActive: boolean;
  onClick: () => void;
}

const HistoryEntry: React.FC<HistoryEntryProps> = ({ entry, isActive, onClick }) => {
  // Get the appropriate icon for the feature type
  const getFeatureIcon = () => {
    switch (entry.featureType) {
      case 'ai-detector':
        return <FiSearch className="text-purple-500" />;
      case 'grammar-check':
        return <FiCheck className="text-green-500" />;
      case 'criteria-check':
        return <FiFileText className="text-blue-500" />;
      case 'paraphraser':
        return <FiEdit3 className="text-orange-500" />;
      default:
        return <FiFileText className="text-gray-500" />;
    }
  };

  // Get a formatted feature name
  const getFeatureName = () => {
    switch (entry.featureType) {
      case 'ai-detector':
        return 'AI Detector';
      case 'grammar-check':
        return 'Grammar Check';
      case 'criteria-check':
        return 'Criteria Check';
      case 'paraphraser':
        return 'Paraphraser';
      default:
        return 'Unknown Feature';
    }
  };

  // Get a truncated preview of the input text
  const getTextPreview = () => {
    if (!entry.inputText) return 'No input text';
    return entry.inputText.length > 50
      ? `${entry.inputText.substring(0, 50)}...`
      : entry.inputText;
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      className={`p-3 rounded-lg mb-2 cursor-pointer transition-all ${
        isActive
          ? 'bg-blue-100 border-l-4 border-blue-500'
          : 'bg-white hover:bg-gray-50 border border-gray-200'
      }`}
    >
      <div className="flex items-center mb-1">
        <div className="mr-2">
          {getFeatureIcon()}
        </div>
        <div className="font-medium text-sm">{getFeatureName()}</div>
      </div>
      <div className="text-xs text-gray-600 line-clamp-2 mb-1">
        {getTextPreview()}
      </div>
      <div className="flex items-center text-xs text-gray-500">
        <FiClock className="mr-1" size={12} />
        {format(entry.timestamp, 'MMM d, h:mm a')}
      </div>
    </motion.div>
  );
};

export default HistoryEntry;
