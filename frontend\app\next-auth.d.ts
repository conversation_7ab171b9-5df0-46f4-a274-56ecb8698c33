// next-auth.d.ts
import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string; // Add id as a required property
      name?: string | null;
      email?: string | null;
      //image?: string | null;
    };
    //accessToken?: string; // Optional, if you're using it
  }

  interface User {
    id: string; // Ensure User type also has id
    name?: string | null;
    email?: string | null;
    //image?: string | null;
  }
}