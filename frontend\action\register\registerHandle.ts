// /action/register/registerHandle.ts
import axios from 'axios';

interface RegisterCredentials {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword?: string;
  agreeToTerms: boolean;
}

export interface RegisterResponse {
  status: 'success' | 'conflict' | 'error' | 'validation_error';
  message: string;
  errors?: {
    firstName?: string[];
    lastName?: string[];
    email?: string[];
    password?: string[];
    confirmPassword?: string[];
    agreeToTerms?: string[];
  };
}

export default async function registerHandle({ 
  firstName,
  lastName,
  email,
  password,
  confirmPassword,
  agreeToTerms
}: RegisterCredentials): Promise<RegisterResponse> {
  try {
    // Client-side validation
    const errors: RegisterResponse['errors'] = {};
    
    // First name validation
    if (!firstName) {
      errors.firstName = ['First name is required'];
    } else if (firstName.length < 2) {
      errors.firstName = ['First name must be at least 2 characters'];
    }
    
    // Last name validation
    if (!lastName) {
      errors.lastName = ['Last name is required'];
    } else if (lastName.length < 2) {
      errors.lastName = ['Last name must be at least 2 characters'];
    }
    
    // Email validation
    if (!email) {
      errors.email = ['Email address is required'];
    } else if (!email.endsWith('@gmail.com')) {
      errors.email = ['Please use a valid Gmail address (@gmail.com)'];
    } else if (email.length < 10) {
      errors.email = ['Email address is too short'];
    }
    
    // Password validation
    if (!password) {
      errors.password = ['Password is required'];
    } else if (password.length < 8) {
      errors.password = ['Password must be at least 8 characters long'];
    } else if (!/[A-Z]/.test(password)) {
      errors.password = ['Password must contain at least one uppercase letter'];
    } else if (!/[a-z]/.test(password)) {
      errors.password = ['Password must contain at least one lowercase letter'];
    } else if (!/[0-9]/.test(password)) {
      errors.password = ['Password must contain at least one number'];
    }
    
    // Confirm password validation
    if (confirmPassword !== undefined && confirmPassword !== password) {
      errors.confirmPassword = ['Passwords do not match'];
    }
    
    // Terms agreement validation
    if (!agreeToTerms) {
      errors.agreeToTerms = ['You must agree to the Terms of Service and Privacy Policy'];
    }
    
    // If there are validation errors, return them
    if (Object.keys(errors).length > 0) {
      return {
        status: 'validation_error',
        message: 'Please fix the validation errors',
        errors
      };
    }

    // First check if already logged in
    const authCheck = await axios.get(
      `${process.env.NEXT_PUBLIC_API_URL}/check-auth`,
      { withCredentials: true }
    );
    
    // If already logged in, return success
    if (authCheck.data?.authenticated) {
      return {
        status: 'success',
        message: 'You are already logged in'
      };
    }
    
    // If not logged in, attempt registration
    const res = await axios.post(
      `${process.env.NEXT_PUBLIC_API_URL}/register`,
      { 
        username: email, 
        password,
        firstName,
        lastName
      },
      { withCredentials: true }
    );

    if (res.status === 201) {
      return {
        status: 'success',
        message: 'Registration successful! Redirecting to dashboard...'
      };
    } else {
      return {
        status: 'conflict',
        message: 'An account with this email already exists'
      };
    }
  } catch (err: any) {
    console.error('Register error:', err.response?.data || err.message);
    
    // Return more specific error message if available
    if (err.response?.data?.error) {
      return {
        status: 'error',
        message: err.response.data.error
      };
    }
    
    return {
      status: 'error',
      message: 'An unexpected error occurred. Please try again later.'
    };
  }
}
