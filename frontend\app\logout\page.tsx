'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import axios from 'axios';

export default function LogoutPage() {
  const router = useRouter();
  const [status, setStatus] = useState<string>('Logging out...');
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    const performLogout = async () => {
      try {
        // Clear cookies on client side first as a failsafe
        document.cookie = 'session_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        
        // Clear any local storage items
        localStorage.removeItem('user');
        sessionStorage.removeItem('user');
        
        // Then try server-side logout
        try {
          await axios.post(
            `${process.env.NEXT_PUBLIC_API_URL}/logout`,
            {}, // Empty body
            { 
              withCredentials: true,
              timeout: 5000,
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              }
            }
          );
          setStatus('Logged out successfully. Redirecting...');
        } catch (serverError) {
          console.warn('Server logout failed, but continuing with client-side logout:', serverError);
          setStatus('Logged out locally. Redirecting...');
        }
        
        // Redirect to login page after a short delay regardless of server response
        setTimeout(() => {
          router.push('/login');
        }, 1500);
      } catch (error) {
        console.error('Logout error:', error);
        setError(true);
        setStatus('An error occurred during logout. Please try again.');
      }
    };

    performLogout();
  }, [router]);

  const handleRetry = async () => {
    setStatus('Retrying logout...');
    setError(false);
    
    // Force clear cookies on client side
    document.cookie = 'session_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    localStorage.removeItem('user');
    sessionStorage.removeItem('user');
    
    setStatus('Logged out locally. Redirecting...');
    setTimeout(() => {
      router.push('/login');
    }, 1500);
  };

  const handleForceRedirect = () => {
    // Force clear cookies on client side as a fallback
    document.cookie = 'session_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    router.push('/login');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md text-center">
        <h2 className="text-3xl font-bold mb-6 text-gray-800">Logging Out</h2>
        <div className="mb-4">
          <p className="text-gray-600">{status}</p>
        </div>
        {error ? (
          <div className="flex flex-col gap-3">
            <button
              onClick={handleRetry}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
            >
              Retry Logout
            </button>
            <button
              onClick={handleForceRedirect}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition"
            >
              Return to Login Anyway
            </button>
          </div>
        ) : (
          <button
            onClick={() => router.push('/login')}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
          >
            Return to Login
          </button>
        )}
      </div>
    </div>
  );
}


