"use client";

import React, { useMemo, useCallback, useState, useEffect, useRef } from "react";
import { Editor, Transforms, Element, Node, Descendant, createEditor } from "slate";
import { Slate, Editable, withReact, ReactEditor, RenderLeafProps } from "slate-react";
import { withHistory } from "slate-history";
import { BsArrowLeftRight, BsClipboard, BsDownload, BsArrowsExpand } from "react-icons/bs";

// Define custom types inline, ensuring compatibility with Slate's Descendant
interface CustomText extends Record<string, any> {
  text: string;
  added?: boolean; // Optional
  removed?: boolean; // Optional
}

interface CustomElement {
  type: "paragraph";
  children: Descendant[]; // Use Slate's Descendant
}

const styles: { [key: string]: React.CSSProperties } = {
  pageContainer: { display: "flex", flexDirection: "column", minHeight: "100vh", fontFamily: "Arial, sans-serif", backgroundColor: "#fff", zIndex: 1000 },
  topToolbar: { display: "flex", justifyContent: "space-between", alignItems: "center", padding: "10px 20px", backgroundColor: "#f8f9fa", borderBottom: "1px solid #ddd" },
  modeButtons: { display: "flex", gap: "5px" },
  modeBtn: { padding: "6px 12px", borderRadius: "20px", cursor: "pointer", fontSize: "14px", fontWeight: "500", transition: "all 0.2s" },
  sliderContainer: { display: "flex", alignItems: "center", gap: "10px" },
  sliderLabel: { fontSize: "12px", color: "#555" },
  sliderRow: { display: "flex", alignItems: "center", gap: "5px" },
  sliderIcon: { fontSize: "12px", color: "#888" },
  slider: { width: "80px", accentColor: "#28a745" },
  contentContainer: { display: "flex", flex: 1, padding: "20px", gap: "20px", alignItems: "stretch", position: "relative" },
  textColumn: { flex: 1, display: "flex", flexDirection: "column" },
  textArea: { flex: 1, border: "1px solid #ddd", borderRadius: "8px", padding: "15px", fontSize: "15px", lineHeight: "1.6", boxShadow: "inset 0 1px 3px rgba(0,0,0,0.05)", outline: "none" },
  outputBox: { flex: 1, border: "1px solid #ddd", borderRadius: "8px", backgroundColor: "#fff", padding: "15px", overflowY: "auto", boxShadow: "0 1px 3px rgba(0,0,0,0.05)", position: "relative" },
  loadingText: { color: "#888", fontStyle: "italic", textAlign: "center", margin: 0 },
  placeholder: { color: "#aaa", margin: 0, fontStyle: "italic" },
  rephrasedText: { margin: 0, fontSize: "15px", lineHeight: "1.6", whiteSpace: "pre-wrap", outline: "none" },
  statsBar: { padding: "10px", fontSize: "13px", color: "#666", backgroundColor: "#f8f9fa", borderTop: "1px solid #ddd", display: "flex", justifyContent: "space-between", alignItems: "center" },
  actionIcons: { display: "flex", alignItems: "center" },
  arrowIcon: { display: "flex", alignItems: "center", justifyContent: "center" },
  toolbar: {
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-start",
    backgroundColor: "#fff",
    border: "1px solid #ddd",
    borderRadius: "4px",
    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
    padding: "5px",
    zIndex: 1000,
    maxWidth: "400px",
    maxHeight: "300px",
    overflowY: "auto",
    transition: "opacity 0.2s ease-in-out",
  },
  toolbarButton: {
    backgroundColor: "#1677ff",
    color: "#fff",
    padding: "4px 8px",
    border: "none",
    borderRadius: "4px",
    cursor: "pointer",
    fontSize: "12px",
    fontWeight: "500",
    marginBottom: "5px",
    width: "100%",
    textAlign: "center",
    transition: "background-color 0.2s",
  },
  toolbarOptionButton: {
    backgroundColor: "#f0f0f0",
    color: "#333",
    padding: "4px 8px",
    border: "none",
    borderRadius: "4px",
    cursor: "pointer",
    fontSize: "12px",
    marginBottom: "5px",
    width: "100%",
    textAlign: "left",
    whiteSpace: "normal",
    lineHeight: "1.4",
    transition: "background-color 0.2s",
  },
  toolbarOptionButtonHover: {
    backgroundColor: "#e0e0e0",
  },
  rephraseButton: {
    backgroundColor: "#1677ff",
    color: "#fff",
    padding: "4px 8px",
    border: "none",
    borderRadius: "4px",
    cursor: "pointer",
    fontSize: "12px",
    fontWeight: "500",
    marginLeft: "10px",
  },
  modal: {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    backgroundColor: "#fff",
    border: "1px solid #ddd",
    borderRadius: "8px",
    boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
    padding: "20px",
    zIndex: 2000,
    maxWidth: "600px",
    maxHeight: "80vh",
    overflowY: "auto",
  },
  modalOverlay: {
    position: "fixed",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.5)",
    zIndex: 1500,
  },
  modalHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "15px",
  },
  modalTitle: {
    fontSize: "16px",
    fontWeight: "600",
  },
  modalCloseButton: {
    backgroundColor: "transparent",
    border: "none",
    fontSize: "16px",
    cursor: "pointer",
  },
  modalSentence: {
    marginBottom: "15px",
    padding: "10px",
    border: "1px solid #eee",
    borderRadius: "4px",
  },
  modalOption: {
    padding: "5px 10px",
    margin: "5px 0",
    backgroundColor: "#f9f9f9",
    borderRadius: "4px",
    cursor: "pointer",
    transition: "background-color 0.2s",
  },
  modalOptionHover: {
    backgroundColor: "#e6f7ff",
  },
  added: {
    backgroundColor: "#d4edda",
    color: "#155724",
    padding: "0 2px",
  },
  removed: {
    backgroundColor: "#f8d7da",
    color: "#721c24",
    textDecoration: "line-through",
    padding: "0 2px",
  },
};

export default function ParaphraseEditor() {
  const initialValue: Descendant[] = [{ type: "paragraph", children: [{ text: "" }] } as CustomElement];
  const [isMounted, setIsMounted] = useState(false);
  const [inputValue, setInputValue] = useState<Descendant[]>(initialValue);
  const [rephrasedValue, setRephrasedValue] = useState<Descendant[]>(initialValue);
  const [mode, setMode] = useState<string>("Standard");
  const [synonymLevel, setSynonymLevel] = useState<number>(2);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRephrasingSentence, setIsRephrasingSentence] = useState<boolean>(false);
  const [hoveredSentence, setHoveredSentence] = useState<string | null>(null);
  const [selectedSentencePaths, setSelectedSentencePaths] = useState<number[][]>([]);
  const [toolbarPosition, setToolbarPosition] = useState<{ top: number; left: number; positionAbove?: boolean } | null>(null);
  const [paraphraseOptions, setParaphraseOptions] = useState<string[]>([]);
  const [batchParaphraseData, setBatchParaphraseData] = useState<{
    path: number[];
    original: string;
    options: string[];
    selectedOption?: string;
  }[]>([]);
  const [showBatchModal, setShowBatchModal] = useState(false);
  const [paraphraseHistory, setParaphraseHistory] = useState<{ path: number[]; original: string; newText: string }[]>([]);
  const [paraphraseHistoryIndex, setParaphraseHistoryIndex] = useState(-1);

  const inputEditor = useMemo(() => withHistory(withReact(createEditor())), []);
  const outputEditor = useMemo(() => withHistory(withReact(createEditor())), []);
  const outputRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setIsMounted(true);
    console.log("Component mounted on client-side");
  }, []);

  const serialize = (nodes: Node[]): string =>
    nodes
      .map((n) =>
        "text" in n
          ? (n as CustomText).text
          : (n.children as Node[])
              .filter((child) => "text" in child)
              .map((c) => (c as CustomText).text)
              .join("")
      )
      .join("\n\n");

  const rephraseTextViaAPI = async (text: string, tone: string): Promise<string> => {
    setIsLoading(true);
    console.log("rephraseTextViaAPI called with text:", text, "and tone:", tone);
    try {
      const payload = { text, tone };
      console.log("Sending API request with payload:", payload);
      const response = await fetch("http://localhost:5000/paraphrasing", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      console.log("API response status:", response.status);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("API returned non-OK status:", response.status, errorText);
        throw new Error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
      }
      const data = await response.json();
      console.log("API response data:", data);
      if (data.error) {
        console.error("API returned an error:", data.error);
        throw new Error(data.error);
      }
      if (!data.paraphrases || !Array.isArray(data.paraphrases)) {
        console.error("Invalid API response format:", data);
        throw new Error("Invalid response format: 'paraphrases' missing or not an array");
      }
      const rephrased = data.paraphrases
        .map((item: any) => item.versions[0]?.text || item.sentence)
        .join("\n\n");
      console.log("Paraphrased text extracted:", rephrased);
      return rephrased;
    } catch (error: any) {
      console.error("API call failed:", error.message);
      alert(`Error: ${error.message}`);
      return text;
    } finally {
      setIsLoading(false);
      console.log("API call completed, loading state reset");
    }
  };

  const handleRephraseFullText = async () => {
    console.log("handleRephraseFullText triggered");
    if (typeof window === "undefined") {
      console.log("Skipping due to server-side rendering");
      return;
    }
    setIsLoading(true);
    const fullText = serialize(inputValue as Node[]);
    console.log("Input text to paraphrase:", fullText);
    if (!fullText.trim()) {
      console.log("No text provided, aborting");
      setIsLoading(false);
      return;
    }
    try {
      const rephrasedText = await rephraseTextViaAPI(fullText, mode);
      console.log("Full text paraphrased:", rephrasedText);
      const newNodes: CustomElement[] = rephrasedText
        .split("\n\n")
        .filter((paragraph) => paragraph.trim())
        .map((paragraph) => ({
          type: "paragraph",
          children: [{ text: paragraph, added: false, removed: false } as CustomText] as Descendant[],
        }));
      if (outputEditor.children.length > 0) {
        Transforms.removeNodes(outputEditor, { at: [0] });
      } else {
        console.log("Editor is empty, no nodes to remove");
      }
      Transforms.insertNodes(outputEditor, newNodes as Node[], { at: [0] });
      setRephrasedValue(newNodes);
      console.log("Output editor updated with new nodes");
    } catch (error) {
      console.error("Error in full text paraphrasing:", error);
    } finally {
      setIsLoading(false);
      console.log("Full text paraphrasing completed");
    }
  };

  const handleParaphraseSentence = async () => {
    console.log("Starting sentence paraphrasing");
    if (selectedSentencePaths.length === 0) {
      console.log("No sentences selected for paraphrasing");
      return;
    }
    if (selectedSentencePaths.length === 1) {
      setIsRephrasingSentence(true);
      const path = selectedSentencePaths[0];
      const [paragraphNode] = Editor.node(outputEditor, path);
      if (Element.isElement(paragraphNode)) {
        const originalText = (paragraphNode.children as Node[])
          .filter((child) => "text" in child)
          .map((child) => (child as CustomText).text)
          .join("");
        console.log("Selected paragraph to paraphrase:", originalText);
        try {
          const response = await fetch("http://localhost:5000/paraphrasing", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ text: originalText, tone: mode }),
          });
          if (!response.ok) throw new Error("API request failed");
          const data = await response.json();
          const versions = data.paraphrases[0]?.versions.map((v: any) => v.text) || [originalText];
          const options = versions.length >= 3 ? versions.slice(0, 3) : [...versions, ...Array(3 - versions.length).fill(originalText)];
          setParaphraseOptions(options);
          console.log("Paraphrase options set:", options);
        } catch (error) {
          console.error("Error during sentence paraphrasing:", error);
          setParaphraseOptions([originalText, originalText, originalText]);
        } finally {
          setIsRephrasingSentence(false);
        }
      }
    } else {
      setIsRephrasingSentence(true);
      const batchData: { path: number[]; original: string; options: string[]; selectedOption?: string }[] = [];
      for (const path of selectedSentencePaths) {
        const [paragraphNode] = Editor.node(outputEditor, path);
        if (Element.isElement(paragraphNode)) {
          const originalText = (paragraphNode.children as Node[])
            .filter((child) => "text" in child)
            .map((child) => (child as CustomText).text)
            .join("");
          try {
            const response = await fetch("http://localhost:5000/paraphrasing", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ text: originalText, tone: mode }),
            });
            if (!response.ok) throw new Error("API request failed");
            const data = await response.json();
            const versions = data.paraphrases[0]?.versions.map((v: any) => v.text) || [originalText];
            const options = versions.length >= 3 ? versions.slice(0, 3) : [...versions, ...Array(3 - versions.length).fill(originalText)];
            batchData.push({ path, original: originalText, options });
          } catch (error) {
            console.error("Error during batch paraphrasing:", error);
            batchData.push({ path, original: originalText, options: [originalText, originalText, originalText] });
          }
        }
      }
      setBatchParaphraseData(batchData);
      setShowBatchModal(true);
      setIsRephrasingSentence(false);
    }
  };

  const applyBatchParaphrase = (selections: { path: number[]; option: string }[]) => {
    selections.forEach(({ path, option }) => {
      const [paragraphNode] = Editor.node(outputEditor, path);
      if (Element.isElement(paragraphNode)) {
        const originalText = (paragraphNode.children as Node[])
          .filter((child) => "text" in child)
          .map((child) => (child as CustomText).text)
          .join("");
        Transforms.removeNodes(outputEditor, { at: path });
        Transforms.insertNodes(
          outputEditor,
          { type: "paragraph", children: [{ text: option, added: true, removed: false } as CustomText] } as CustomElement,
          { at: path }
        );
        setParaphraseHistory((prev) => [
          ...prev.slice(0, paraphraseHistoryIndex + 1),
          { path, original: originalText, newText: option },
        ]);
        setParaphraseHistoryIndex((prev) => prev + 1);
      }
    });
    setShowBatchModal(false);
    setSelectedSentencePaths([]);
    setToolbarPosition(null);
  };

  const selectParaphraseOption = (option: string) => {
    if (selectedSentencePaths.length !== 1) return;
    const path = selectedSentencePaths[0];
    const [paragraphNode] = Editor.node(outputEditor, path);
    if (Element.isElement(paragraphNode)) {
      const originalText = (paragraphNode.children as Node[])
        .filter((child) => "text" in child)
        .map((child) => (child as CustomText).text)
        .join("");
      Transforms.removeNodes(outputEditor, { at: path });
      Transforms.insertNodes(
        outputEditor,
        { type: "paragraph", children: [{ text: option, added: true, removed: false } as CustomText] } as CustomElement,
        { at: path }
      );
      setParaphraseHistory((prev) => [
        ...prev.slice(0, paraphraseHistoryIndex + 1),
        { path, original: originalText, newText: option },
      ]);
      setParaphraseHistoryIndex((prev) => prev + 1);
    }
    setSelectedSentencePaths([]);
    setToolbarPosition(null);
    setParaphraseOptions([]);
    console.log("Selected paraphrase option:", option);
  };

  const undoParaphrase = () => {
    if (paraphraseHistoryIndex < 0) return;
    const { path, original } = paraphraseHistory[paraphraseHistoryIndex];
    Transforms.removeNodes(outputEditor, { at: path });
    Transforms.insertNodes(
      outputEditor,
      { type: "paragraph", children: [{ text: original } as CustomText] } as CustomElement,
      { at: path }
    );
    setParaphraseHistoryIndex((prev) => prev - 1);
  };

  const redoParaphrase = () => {
    if (paraphraseHistoryIndex >= paraphraseHistory.length - 1) return;
    const { path, newText } = paraphraseHistory[paraphraseHistoryIndex + 1];
    Transforms.removeNodes(outputEditor, { at: path });
    Transforms.insertNodes(
      outputEditor,
      { type: "paragraph", children: [{ text: newText, added: true, removed: false } as CustomText] } as CustomElement,
      { at: path }
    );
    setParaphraseHistoryIndex((prev) => prev + 1);
  };

  const renderLeaf = useCallback(
    ({ attributes, children, leaf }: RenderLeafProps) => {
      const isHovered = hoveredSentence === leaf.text;
      const isSelected = selectedSentencePaths.some((path) => {
        const [node] = Editor.node(outputEditor, path);
        if (Element.isElement(node)) {
          return (node.children as Node[])
            .filter((child) => "text" in child)
            .some((child) => (child as CustomText).text === leaf.text);
        }
        return false;
      });
      return (
        <span
          {...attributes}
          style={{
            backgroundColor: isSelected ? "#b3d9ff" : isHovered ? "#e6f7ff" : "transparent",
            cursor: "pointer",
            padding: "2px 4px",
            borderRadius: "4px",
            display: "inline-block",
            ...((leaf as CustomText).added ? styles.added : {}),
            ...((leaf as CustomText).removed ? styles.removed : {}),
          }}
          onMouseEnter={() => setHoveredSentence(leaf.text)}
          onMouseLeave={() => setHoveredSentence(null)}
          onClick={(e) => {
            e.preventDefault();
            if (typeof window === "undefined") return;
            try {
              const range = ReactEditor.findEventRange(outputEditor, e.nativeEvent);
              if (!range) {
                console.warn("No range found for click event");
                return;
              }
              const [paragraphNode, paragraphPath] = Editor.above(outputEditor, {
                match: (n): n is CustomElement => Element.isElement(n) && (n as CustomElement).type === "paragraph",
                at: range,
              }) || [];
              if (!paragraphNode || !paragraphPath) {
                console.warn("No paragraph node found for leaf:", leaf);
                return;
              }
              if (e.shiftKey) {
                setSelectedSentencePaths((prev) => {
                  const pathExists = prev.some((p) => JSON.stringify(p) === JSON.stringify(paragraphPath));
                  if (pathExists) {
                    return prev.filter((p) => JSON.stringify(p) !== JSON.stringify(paragraphPath));
                  }
                  return [...prev, paragraphPath];
                });
              } else {
                setSelectedSentencePaths([paragraphPath]);
              }
              const rect = e.currentTarget.getBoundingClientRect();
              if (!outputRef.current) {
                console.warn("Output ref unavailable");
                return;
              }
              const outputRect = outputRef.current!.getBoundingClientRect();
              const spaceBelow = outputRect.bottom - rect.bottom;
              const toolbarHeight = 300;
              const positionAbove = spaceBelow < toolbarHeight;
              setToolbarPosition({
                top: positionAbove ? rect.top - outputRect.top - toolbarHeight - 5 : rect.bottom - outputRect.top + 5,
                left: rect.left - outputRect.left,
                positionAbove,
              });
              setParaphraseOptions([]);
              console.log("Paragraph selected, toolbar positioned at:", {
                top: positionAbove ? rect.top - outputRect.top - toolbarHeight - 5 : rect.bottom - outputRect.top + 5,
                left: rect.left - outputRect.left,
                path: paragraphPath,
                positionAbove,
              });
            } catch (err) {
              console.error("Error finding path for leaf:", leaf, "Error:", err);
            }
          }}
        >
          {children}
        </span>
      );
    },
    [outputEditor, hoveredSentence, selectedSentencePaths]
  );

  const inputStats = useMemo(() => {
    const text = serialize(inputValue as Node[]);
    return {
      sentences: text.split(/[.?!]+/).filter(Boolean).length,
      words: text.trim().split(/\s+/).filter(Boolean).length,
    };
  }, [inputValue]);

  const outputStats = useMemo(() => {
    const text = serialize(rephrasedValue as Node[]);
    return {
      sentences: text.split(/[.?!]+/).filter(Boolean).length,
      words: text.trim().split(/\s+/).filter(Boolean).length,
    };
  }, [rephrasedValue]);

  const handleCopy = () => {
    if (typeof window === "undefined") return;
    const text = serialize(rephrasedValue as Node[]);
    navigator.clipboard.writeText(text).then(() => console.log("Text copied to clipboard:", text));
  };

  const handleDownload = () => {
    if (typeof window === "undefined") return;
    const text = serialize(rephrasedValue as Node[]);
    const blob = new Blob([text], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "rephrased-text.txt";
    a.click();
    URL.revokeObjectURL(url);
    console.log("Text downloaded as rephrased-text.txt");
  };

  const handleExpand = () => {
    if (typeof window === "undefined") return;
    const text = serialize(rephrasedValue as Node[]);
    const newWindow = window.open();
    if (!newWindow) {
      console.warn("Failed to open new window. A popup blocker might be preventing this action.");
      return;
    }
    newWindow.document.write(`<pre>${text}</pre>`);
    newWindow.document.close();
    console.log("Text expanded in new window");
  };

  return (
    <div style={styles.pageContainer}>
      <style jsx global>{`
        div[class*="notion-web-clipper"],
        div[class*="notion"],
        [style*="position: fixed"][style*="bottom"][style*="left"],
        div[id="extwaiokist"] {
          display: none !important;
        }
      `}</style>

      <div style={styles.topToolbar}>
        <div style={styles.modeButtons}>
          {["Standard", "Fluency", "Formal", "Creative", "Expand", "Shorten"].map((m) => (
            <button
              key={m}
              style={{
                ...styles.modeBtn,
                backgroundColor: mode === m ? "#1677ff" : "transparent",
                color: mode === m ? "#fff" : "#333",
                border: mode === m ? "none" : "1px solid #ddd",
              }}
              onClick={() => setMode(m)}
            >
              {m}
            </button>
          ))}
        </div>
        <div style={styles.sliderContainer}>
          <label style={styles.sliderLabel}>Synonyms:</label>
          <div style={styles.sliderRow}>
            <span style={styles.sliderIcon}>Low</span>
            <input
              type="range"
              min={0}
              max={4}
              value={synonymLevel}
              onChange={(e) => setSynonymLevel(Number(e.target.value))}
              style={styles.slider}
            />
            <span style={styles.sliderIcon}>High</span>
          </div>
        </div>
      </div>

      <div style={styles.contentContainer}>
        <div style={styles.textColumn}>
          {isMounted ? (
            <Slate editor={inputEditor} initialValue={inputValue} onChange={(value) => setInputValue(value as Descendant[])}>
              <Editable style={styles.textArea} placeholder="Type or paste your text here..." />
            </Slate>
          ) : (
            <div style={styles.textArea} contentEditable={false}>
              Type or paste your text here...
            </div>
          )}
          <div style={styles.statsBar}>
            <span>{inputStats.sentences} Sentences • {inputStats.words} Words</span>
            <button
              style={{
                ...styles.rephraseButton,
                opacity: isLoading || !serialize(inputValue as Node[]).trim() ? 0.5 : 1,
                cursor: isLoading || !serialize(inputValue as Node[]).trim() ? "not-allowed" : "pointer",
              }}
              onClick={handleRephraseFullText}
              disabled={isLoading || !serialize(inputValue as Node[]).trim()}
            >
              {isLoading ? "Rephrasing..." : "Rephrase"}
            </button>
          </div>
        </div>

        <div style={styles.arrowIcon}>
          <BsArrowLeftRight size={24} color="#666" />
        </div>

        <div style={styles.textColumn}>
          <div ref={outputRef} style={styles.outputBox}>
            {isLoading ? (
              <p style={styles.loadingText}>Rephrasing...</p>
            ) : isMounted && serialize(rephrasedValue as Node[]).trim() ? (
              <Slate editor={outputEditor} initialValue={rephrasedValue} onChange={(value) => setRephrasedValue(value as Descendant[])}>
                <Editable renderLeaf={renderLeaf} style={styles.rephrasedText} />
              </Slate>
            ) : (
              <p style={styles.placeholder}>Your rephrased text will appear here...</p>
            )}
            {toolbarPosition && isMounted && (
              <div
                style={{
                  ...styles.toolbar,
                  position: "absolute",
                  top: toolbarPosition.top,
                  left: toolbarPosition.left,
                  opacity: 1,
                }}
              >
                <button
                  style={styles.toolbarButton}
                  onClick={handleParaphraseSentence}
                  onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = "#0056d2" as string)}
                  onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "#1677ff" as string)}
                >
                  {isRephrasingSentence ? "Loading..." : selectedSentencePaths.length > 1 ? "Batch Rephrase" : "Rephrase"}
                </button>
                {selectedSentencePaths.length === 1 && paraphraseOptions.length > 0 && (
                  <>
                    {paraphraseOptions.map((option, index) => (
                      <button
                        key={index}
                        style={styles.toolbarOptionButton}
                        onClick={() => selectParaphraseOption(option)}
                        onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = styles.toolbarOptionButtonHover.backgroundColor as string)}
                        onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "#f0f0f0" as string)}
                      >
                        {option}
                      </button>
                    ))}
                  </>
                )}
                <button
                  style={{
                    ...styles.toolbarButton,
                    marginTop: "5px",
                    backgroundColor: paraphraseHistoryIndex >= 0 ? "#1677ff" : "#ccc",
                  }}
                  onClick={undoParaphrase}
                  disabled={paraphraseHistoryIndex < 0}
                  onMouseEnter={(e) => {
                    if (paraphraseHistoryIndex >= 0) {
                      e.currentTarget.style.backgroundColor = "#0056d2" as string;
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (paraphraseHistoryIndex >= 0) {
                      e.currentTarget.style.backgroundColor = "#1677ff" as string;
                    } else {
                      e.currentTarget.style.backgroundColor = "#ccc" as string;
                    }
                  }}
                >
                  Undo Paraphrase
                </button>
                <button
                  style={{
                    ...styles.toolbarButton,
                    marginTop: "5px",
                    backgroundColor: paraphraseHistoryIndex < paraphraseHistory.length - 1 ? "#1677ff" : "#ccc",
                  }}
                  onClick={redoParaphrase}
                  disabled={paraphraseHistoryIndex >= paraphraseHistory.length - 1}
                  onMouseEnter={(e) => {
                    if (paraphraseHistoryIndex < paraphraseHistory.length - 1) {
                      e.currentTarget.style.backgroundColor = "#0056d2" as string;
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (paraphraseHistoryIndex < paraphraseHistory.length - 1) {
                      e.currentTarget.style.backgroundColor = "#1677ff" as string;
                    } else {
                      e.currentTarget.style.backgroundColor = "#ccc" as string;
                    }
                  }}
                >
                  Redo Paraphrase
                </button>
                <button
                  style={{
                    ...styles.toolbarButton,
                    marginTop: "5px",
                  }}
                  onClick={() => {
                    setSelectedSentencePaths([]);
                    setToolbarPosition(null);
                    setParaphraseOptions([]);
                  }}
                  onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = "#0056d2" as string)}
                  onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "#1677ff" as string)}
                >
                  Close
                </button>
              </div>
            )}
            {showBatchModal && (
              <>
                <div style={styles.modalOverlay} onClick={() => setShowBatchModal(false)} />
                <div style={styles.modal}>
                  <div style={styles.modalHeader}>
                    <h2 style={styles.modalTitle}>Batch Rephrase</h2>
                    <button style={styles.modalCloseButton} onClick={() => setShowBatchModal(false)}>
                      ✕
                    </button>
                  </div>
                  {batchParaphraseData.map((item, index) => (
                    <div key={index} style={styles.modalSentence}>
                      <p>
                        <strong>Original:</strong> {item.original}
                      </p>
                      <p>
                        <strong>Options:</strong>
                      </p>
                      {item.options.map((option, optIndex) => (
                        <div
                          key={optIndex}
                          style={styles.modalOption}
                          onClick={() => {
                            setBatchParaphraseData((prev) =>
                              prev.map((d, i) =>
                                i === index ? { ...d, selectedOption: option } : d
                              )
                            );
                          }}
                          onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = styles.modalOptionHover.backgroundColor as string)}
                          onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "#f9f9f9" as string)}
                        >
                          {option} {item.selectedOption === option && "✔"}
                        </div>
                      ))}
                    </div>
                  ))}
                  <button
                    style={{
                      ...styles.toolbarButton,
                      marginTop: "10px",
                    }}
                    onClick={() => {
                      const selections = batchParaphraseData
                        .filter((item) => item.selectedOption)
                        .map((item) => ({
                          path: item.path,
                          option: item.selectedOption!,
                        }));
                      if (selections.length === batchParaphraseData.length) {
                        applyBatchParaphrase(selections);
                      } else {
                        alert("Please select an option for each sentence.");
                      }
                    }}
                  >
                    Apply Changes
                  </button>
                </div>
              </>
            )}
          </div>
          <div style={styles.statsBar}>
            <span>{outputStats.sentences} Sentences • {outputStats.words} Words</span>
            <div style={styles.actionIcons}>
              <BsClipboard size={16} style={{ cursor: "pointer", marginLeft: "10px" }} onClick={handleCopy} />
              <BsDownload size={16} style={{ cursor: "pointer", marginLeft: "10px" }} onClick={handleDownload} />
              <BsArrowsExpand size={16} style={{ cursor: "pointer", marginLeft: "10px" }} onClick={handleExpand} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}