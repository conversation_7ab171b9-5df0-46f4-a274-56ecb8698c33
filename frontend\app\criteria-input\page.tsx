"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

// Mock data - in a real app, this would be fetched from an API
interface ScholarshipData {
  scholarship_categories: Record<string, string[]>;
  initial_suggestion_prompts: Record<string, Record<string, string>>;
  follow_up_suggestions: Record<string, Record<string, string>>;
  follow_up_examples: Record<string, Record<string, string>>;
}

export default function CriteriaInput() {
  // State for all form elements
  const [scholarshipData, setScholarshipData] = useState<ScholarshipData>({
    scholarship_categories: {},
    initial_suggestion_prompts: {},
    follow_up_suggestions: {},
    follow_up_examples: {}
  });
  const [selectedScholarship, setSelectedScholarship] = useState<string>('');
  const [newScholarship, setNewScholarship] = useState<string>('');
  const [selectedEssayType, setSelectedEssayType] = useState<string>('');
  const [newEssayType, setNewEssayType] = useState<string>('');
  const [promptText, setPromptText] = useState<string>('');
  const [selectedSuggestionCategory, setSelectedSuggestionCategory] = useState<string>('');
  const [newSuggestionCategory, setNewSuggestionCategory] = useState<string>('');
  const [suggestionText, setSuggestionText] = useState<string>('');
  const [selectedExampleCategory, setSelectedExampleCategory] = useState<string>('');
  const [newExampleCategory, setNewExampleCategory] = useState<string>('');
  const [exampleText, setExampleText] = useState<string>('');
  const [saveStatus, setSaveStatus] = useState<{message: string, type: 'success' | 'warning' | 'error' | ''}>({
    message: '',
    type: ''
  });

  // Fetch data on component mount
  useEffect(() => {
    // Mock API fetch - replace with actual API call
    const fetchData = async () => {
      try {
        // Simulating fetched data
        const data: ScholarshipData = {
          scholarship_categories: {
            'Gates Scholarship': ['Personal Statement', 'Leadership Essay'],
            'Fulbright': ['Research Proposal', 'Personal Statement']
          },
          initial_suggestion_prompts: {
            'Gates Scholarship': {
              'Personal Statement': 'Write about your personal background and goals...',
              'Leadership Essay': 'Describe a time when you demonstrated leadership...'
            },
            'Fulbright': {
              'Research Proposal': 'Outline your proposed research project...',
              'Personal Statement': 'Explain why you are interested in this program...'
            }
          },
          follow_up_suggestions: {
            'Personal Statement': {
              'Clarity': 'Make sure your narrative is clear and focused.',
              'Impact': 'Emphasize the impact of your experiences.'
            },
            'Leadership Essay': {
              'Examples': 'Provide concrete examples of your leadership.',
              'Reflection': 'Reflect on what you learned from the experience.'
            }
          },
          follow_up_examples: {
            'Personal Statement': {
              'Strong Opening': 'The alarm clock rang at 5 AM, but I was already awake, thinking about the opportunity ahead.',
              'Clear Goals': 'My goal is to become a researcher in quantum computing to address...'
            },
            'Leadership Essay': {
              'Situation Description': 'As the president of the Science Club, I faced the challenge of declining membership.'
            }
          }
        };
        setScholarshipData(data);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);

  // Get current scholarship
  const getScholarship = () => {
    if (selectedScholarship === '➕ Add New Scholarship') {
      return newScholarship.trim();
    }
    return selectedScholarship;
  };

  // Get current essay type
  const getEssayType = () => {
    if (selectedEssayType === '➕ Add New Essay Type') {
      return newEssayType.trim();
    }
    return selectedEssayType;
  };

  // Get suggestion category
  const getSuggestionCategory = () => {
    if (selectedSuggestionCategory === '➕ Add New Category') {
      return newSuggestionCategory.trim();
    }
    return selectedSuggestionCategory;
  };

  // Get example category
  const getExampleCategory = () => {
    if (selectedExampleCategory === '➕ Add New Category') {
      return newExampleCategory.trim();
    }
    return selectedExampleCategory;
  };

  // Handle scholarship change
  const handleScholarshipChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedScholarship(value);
    
    // Reset essay type if scholarship changes
    setSelectedEssayType('');
    
    // Load prompt text if available
    if (value !== '➕ Add New Scholarship' && 
        scholarshipData.initial_suggestion_prompts[value] && 
        selectedEssayType && 
        scholarshipData.initial_suggestion_prompts[value][selectedEssayType]) {
      setPromptText(scholarshipData.initial_suggestion_prompts[value][selectedEssayType]);
    } else {
      setPromptText('');
    }
  };

  // Handle essay type change
  const handleEssayTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedEssayType(value);
    
    const scholarship = getScholarship();
    
    // Load prompt text if available
    if (scholarship && 
        value !== '➕ Add New Essay Type' && 
        scholarshipData.initial_suggestion_prompts[scholarship] && 
        scholarshipData.initial_suggestion_prompts[scholarship][value]) {
      setPromptText(scholarshipData.initial_suggestion_prompts[scholarship][value]);
    } else {
      setPromptText('');
    }
    
    // Reset suggestion and example categories
    setSelectedSuggestionCategory('');
    setSelectedExampleCategory('');
    setSuggestionText('');
    setExampleText('');
  };

  // Handle suggestion category change
  const handleSuggestionCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedSuggestionCategory(value);
    
    const essayType = getEssayType();
    
    // Load suggestion text if available
    if (essayType && 
        value !== '➕ Add New Category' && 
        scholarshipData.follow_up_suggestions[essayType] && 
        scholarshipData.follow_up_suggestions[essayType][value]) {
      setSuggestionText(scholarshipData.follow_up_suggestions[essayType][value]);
    } else {
      setSuggestionText('');
    }
  };

  // Handle example category change
  const handleExampleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedExampleCategory(value);
    
    const essayType = getEssayType();
    
    // Load example text if available
    if (essayType && 
        value !== '➕ Add New Category' && 
        scholarshipData.follow_up_examples[essayType] && 
        scholarshipData.follow_up_examples[essayType][value]) {
      setExampleText(scholarshipData.follow_up_examples[essayType][value]);
    } else {
      setExampleText('');
    }
  };

  // Save all data
  const handleSave = async () => {
    const scholarship = getScholarship();
    const essayType = getEssayType();
    
    if (!scholarship || !essayType || !promptText.trim()) {
      setSaveStatus({
        message: '⚠️ Please fill out Scholarship, Essay Type, and Initial Prompt Text before saving.',
        type: 'warning'
      });
      return;
    }

    // Create a copy of the current data to update
    const updatedData = { ...scholarshipData };
    
    // Update scholarship categories
    if (!updatedData.scholarship_categories[scholarship]) {
      updatedData.scholarship_categories[scholarship] = [];
    }
    if (!updatedData.scholarship_categories[scholarship].includes(essayType)) {
      updatedData.scholarship_categories[scholarship].push(essayType);
    }
    
    // Update initial prompts
    if (!updatedData.initial_suggestion_prompts[scholarship]) {
      updatedData.initial_suggestion_prompts[scholarship] = {};
    }
    updatedData.initial_suggestion_prompts[scholarship][essayType] = promptText.trim();
    
    // Update suggestion if provided
    const suggestionCategory = getSuggestionCategory();
    if (suggestionCategory && suggestionText.trim()) {
      if (!updatedData.follow_up_suggestions[essayType]) {
        updatedData.follow_up_suggestions[essayType] = {};
      }
      updatedData.follow_up_suggestions[essayType][suggestionCategory] = suggestionText.trim();
    }
    
    // Update example if provided
    const exampleCategory = getExampleCategory();
    if (exampleCategory && exampleText.trim()) {
      if (!updatedData.follow_up_examples[essayType]) {
        updatedData.follow_up_examples[essayType] = {};
      }
      updatedData.follow_up_examples[essayType][exampleCategory] = exampleText.trim();
    }
    
    // In a real app, send to API here
    // const response = await fetch('/api/update-prompts', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(updatedData)
    // });
    
    // For demo purposes, just update the state
    setScholarshipData(updatedData);
    setSaveStatus({
      message: 'Prompts and suggestions saved successfully! 🎉',
      type: 'success'
    });
    
    // Reset form if adding new scholarship or essay type
    if (selectedScholarship === '➕ Add New Scholarship') {
      setSelectedScholarship(newScholarship);
      setNewScholarship('');
    }
    
    if (selectedEssayType === '➕ Add New Essay Type') {
      setSelectedEssayType(newEssayType);
      setNewEssayType('');
    }
    
    if (selectedSuggestionCategory === '➕ Add New Category') {
      setSelectedSuggestionCategory(newSuggestionCategory);
      setNewSuggestionCategory('');
    }
    
    if (selectedExampleCategory === '➕ Add New Category') {
      setSelectedExampleCategory(newExampleCategory);
      setNewExampleCategory('');
    }
    
    // Clear status message after 3 seconds
    setTimeout(() => {
      setSaveStatus({ message: '', type: '' });
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-gray-100 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm p-4 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5s3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18s-3.332.477-4.5 1.253" />
            </svg>
            <h1 className="text-2xl font-bold text-gray-800">Scholarship Prompt Editor</h1>
          </div>
          <Link href="/" className="text-blue-600 hover:text-blue-800 font-medium transition-colors">
            Return to Home
          </Link>
        </div>
      </header>
  
      {/* Main Content */}
      <main className="flex-grow p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Form Card */}
          <div className="bg-white rounded-xl shadow-md p-6">
            <div className="space-y-6">
              {/* Selection Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Scholarship Selection */}
                <div className="space-y-2">
                  <label htmlFor="scholarship" className="block text-sm font-medium text-gray-700">
                    Scholarship Program
                  </label>
                  <select
                    id="scholarship"
                    value={selectedScholarship}
                    onChange={handleScholarshipChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                  >
                    <option value="">Select Scholarship</option>
                    {Object.keys(scholarshipData.scholarship_categories).map(scholarship => (
                      <option key={scholarship} value={scholarship}>
                        {scholarship}
                      </option>
                    ))}
                    <option value="➕ Add New Scholarship">+ New Scholarship</option>
                  </select>
                  {selectedScholarship === '➕ Add New Scholarship' && (
                    <input
                      type="text"
                      value={newScholarship}
                      onChange={(e) => setNewScholarship(e.target.value)}
                      placeholder="Enter scholarship name"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all mt-2"
                    />
                  )}
                </div>
  
                {/* Essay Type Selection */}
                <div className="space-y-2">
                  <label htmlFor="essayType" className="block text-sm font-medium text-gray-700">
                    Essay Type
                  </label>
                  <select
                    id="essayType"
                    value={selectedEssayType}
                    onChange={handleEssayTypeChange}
                    disabled={!getScholarship()}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all disabled:bg-gray-100 disabled:cursor-not-allowed"
                  >
                    <option value="">Select Essay Type</option>
                    {getScholarship() && scholarshipData.scholarship_categories[getScholarship()]?.map(essayType => (
                      <option key={essayType} value={essayType}>
                        {essayType}
                      </option>
                    ))}
                    <option value="➕ Add New Essay Type">+ New Essay Type</option>
                  </select>
                  {selectedEssayType === '➕ Add New Essay Type' && (
                    <input
                      type="text"
                      value={newEssayType}
                      onChange={(e) => setNewEssayType(e.target.value)}
                      placeholder="Enter essay type"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all mt-2"
                    />
                  )}
                </div>
              </div>
  
              {/* Prompt Input */}
              <div className="space-y-2">
                <label htmlFor="promptText" className="block text-sm font-medium text-gray-700">
                  Initial Prompt
                </label>
                <textarea
                  id="promptText"
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  disabled={!getScholarship() || !getEssayType()}
                  placeholder="Enter the prompt text..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all disabled:bg-gray-100 min-h-[120px] resize-y"
                />
              </div>
  
              {/* Suggestions and Examples Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Suggestions Section */}
                <div className="space-y-4">
                  <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    Suggestions
                  </h2>
                  <select
                    value={selectedSuggestionCategory}
                    onChange={handleSuggestionCategoryChange}
                    disabled={!getEssayType()}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all disabled:bg-gray-100 disabled:cursor-not-allowed"
                  >
                    <option value="">Select Category</option>
                    {getEssayType() && scholarshipData.follow_up_suggestions[getEssayType()] && 
                      Object.keys(scholarshipData.follow_up_suggestions[getEssayType()]).map((category: string) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    <option value="➕ Add New Category">+ New Category</option>
                  </select>
                  {selectedSuggestionCategory === '➕ Add New Category' && (
                    <input
                      type="text"
                      value={newSuggestionCategory}
                      onChange={(e) => setNewSuggestionCategory(e.target.value)}
                      placeholder="New category name"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                    />
                  )}
                  <textarea
                    value={suggestionText}
                    onChange={(e) => setSuggestionText(e.target.value)}
                    disabled={!getEssayType() || !getSuggestionCategory()}
                    placeholder={`Suggestion for '${getSuggestionCategory() || "category"}'...`}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all disabled:bg-gray-100 min-h-[100px] resize-y"
                  />
                </div>
  
                {/* Examples Section */}
                <div className="space-y-4">
                  <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Examples
                  </h2>
                  <select
                    value={selectedExampleCategory}
                    onChange={handleExampleCategoryChange}
                    disabled={!getEssayType()}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all disabled:bg-gray-100 disabled:cursor-not-allowed"
                  >
                    <option value="">Select Category</option>
                    {getEssayType() && scholarshipData.follow_up_examples[getEssayType()] && 
                      Object.keys(scholarshipData.follow_up_examples[getEssayType()]).map((category: string) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    <option value="➕ Add New Category">+ New Category</option>
                  </select>
                  {selectedExampleCategory === '➕ Add New Category' && (
                    <input
                      type="text"
                      value={newExampleCategory}
                      onChange={(e) => setNewExampleCategory(e.target.value)}
                      placeholder="New category name"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                    />
                  )}
                  <textarea
                    value={exampleText}
                    onChange={(e) => setExampleText(e.target.value)}
                    disabled={!getEssayType() || !getExampleCategory()}
                    placeholder={`Example for '${getExampleCategory() || "category"}'...`}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all disabled:bg-gray-100 min-h-[100px] resize-y"
                  />
                </div>
              </div>
  
              {/* Save Button and Status */}
              <div className="flex items-center justify-between pt-4">
                <div className="flex-1">
                  {saveStatus.message && (
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      saveStatus.type === 'success' ? 'bg-green-100 text-green-800' :
                      saveStatus.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                      saveStatus.type === 'error' ? 'bg-red-100 text-red-800' : ''
                    }`}>
                      {saveStatus.type === 'success' && (
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                      {saveStatus.message}
                    </div>
                  )}
                </div>
                <button
                  onClick={handleSave}
                  disabled={!getScholarship() || !getEssayType() || !promptText.trim()}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all flex items-center gap-2"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                  </svg>
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
  
      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 p-4 text-center text-gray-600 text-sm">
        <p>Scholarship Prompt Editor © {new Date().getFullYear()}</p>
      </footer>
    </div>
  );
}