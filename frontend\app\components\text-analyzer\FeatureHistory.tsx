'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiFileText, FiEdit3, FiCheck, FiSearch, FiClock } from 'react-icons/fi';
import { format } from 'date-fns';
import { HistoryEntry } from './HistoryEntry';

interface FeatureHistoryProps {
  entries: HistoryEntry[];
  activeFeature: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser';
  onFeatureSelect: (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => void;
}

const FeatureHistory: React.FC<FeatureHistoryProps> = ({
  entries,
  activeFeature,
  onFeatureSelect
}) => {
  // Filter entries for the current input text
  const sortedEntries = [...entries].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

  // Get feature icon
  const getFeatureIcon = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {
    switch (featureType) {
      case 'ai-detector':
        return <FiSearch className="text-purple-500" />;
      case 'grammar-check':
        return <FiCheck className="text-green-500" />;
      case 'criteria-check':
        return <FiFileText className="text-blue-500" />;
      case 'paraphraser':
        return <FiEdit3 className="text-orange-500" />;
      default:
        return <FiFileText className="text-gray-500" />;
    }
  };

  // Get feature name
  const getFeatureName = (featureType: 'ai-detector' | 'grammar-check' | 'criteria-check' | 'paraphraser') => {
    switch (featureType) {
      case 'ai-detector':
        return 'AI Detector';
      case 'grammar-check':
        return 'Grammar Check';
      case 'criteria-check':
        return 'Criteria Check';
      case 'paraphraser':
        return 'Paraphraser';
      default:
        return 'Unknown Feature';
    }
  };

  return (
    <div className="mb-4 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="p-3 bg-gray-50 border-b border-gray-200">
        <h3 className="font-medium text-gray-700">Feature History</h3>
      </div>
      
      <div className="p-2">
        <div className="flex flex-wrap gap-2">
          <AnimatePresence>
            {sortedEntries.map((entry) => (
              <motion.div
                key={entry.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => onFeatureSelect(entry.featureType)}
                className={`flex items-center p-2 rounded-md cursor-pointer ${
                  activeFeature === entry.featureType
                    ? 'bg-blue-100 border border-blue-300'
                    : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'
                }`}
              >
                <div className="mr-2">
                  {getFeatureIcon(entry.featureType)}
                </div>
                <div>
                  <div className="text-sm font-medium">
                    {getFeatureName(entry.featureType)}
                  </div>
                  <div className="text-xs text-gray-500 flex items-center">
                    <FiClock className="mr-1" size={10} />
                    {format(entry.timestamp, 'h:mm a')}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default FeatureHistory;
