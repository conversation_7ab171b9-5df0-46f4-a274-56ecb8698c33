'use client';

import React, { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { FiSend } from 'react-icons/fi';
import FeatureSelector from './components/FeatureSelector';

export default function Home() {
  const [inputText, setInputText] = useState('');
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const router = useRouter();

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputText(e.target.value);
    setIsTyping(true);

    // Clear typing indicator after a delay
    setTimeout(() => {
      setIsTyping(false);
    }, 1000);
  };

  // Auto-resize textarea as user types
  const autoResizeTextarea = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  // Handle feature selection
  const handleFeatureSelect = (feature: string) => {
    setSelectedFeature(feature);
  };

  // Handle form submission with animation
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!inputText.trim() || !selectedFeature) return;

    // Start navigation animation
    setIsNavigating(true);

    // Store the input text in localStorage
    localStorage.setItem('scholarar_input_text', inputText);

    // Delay navigation to allow for exit animations
    setTimeout(() => {
      // Navigate to the selected feature page
      router.push(`/text-analyzer?tab=${selectedFeature}`);
    }, 500); // Adjust timing to match your animation duration
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key="home-page"
        initial={{ opacity: 1 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className="flex flex-col h-screen bg-gradient-to-b from-blue-50 to-white"
      >
        <main className="flex-1 flex flex-col items-center justify-center p-6">
          <motion.div
            className="w-full max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-800 mb-3">Enhance Your Scholarship Essays</h2>
              <p className="text-lg text-gray-600">
                Enter your text below and select a feature to analyze, check, or improve your writing.
              </p>
            </div>

            {/* ChatGPT-like input form */}
            <form onSubmit={handleSubmit} className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="p-6">
                <textarea
                  ref={textareaRef}
                  className="w-full p-4 text-gray-700 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none min-h-[200px]"
                  placeholder="Enter your essay or text here..."
                  value={inputText}
                  onChange={handleInputChange}
                  onInput={autoResizeTextarea}
                  disabled={isNavigating}
                />

                {/* Typing indicator */}
                <AnimatePresence>
                  {isTyping && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="text-xs text-gray-500 mt-2"
                    >
                      <span className="inline-block w-1 h-1 bg-gray-500 rounded-full animate-pulse mr-1"></span>
                      <span className="inline-block w-1 h-1 bg-gray-500 rounded-full animate-pulse mr-1" style={{ animationDelay: '0.2s' }}></span>
                      <span className="inline-block w-1 h-1 bg-gray-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></span>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Feature selector and submit button */}
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-500 mb-3">Select a feature:</h3>
                  <FeatureSelector
                    onSelect={handleFeatureSelect}
                    selectedFeature={selectedFeature}
                  />
                </div>

                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-gray-500">
                    {inputText.split(/\s+/).filter(Boolean).length} words
                  </div>
                  <motion.button
                    type="submit"
                    disabled={!inputText.trim() || !selectedFeature || isNavigating}
                    whileHover={!inputText.trim() || !selectedFeature || isNavigating ? {} : { scale: 1.05 }}
                    whileTap={!inputText.trim() || !selectedFeature || isNavigating ? {} : { scale: 0.95 }}
                    className={`px-4 py-2 rounded-lg flex items-center ${
                      !inputText.trim() || !selectedFeature || isNavigating
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                  >
                    <FiSend className="mr-2" />
                    {isNavigating ? 'Navigating...' : 'Analyze'}
                  </motion.button>
                </div>
              </div>
            </form>
          </motion.div>
        </main>
      </motion.div>
    </AnimatePresence>
  );
}
