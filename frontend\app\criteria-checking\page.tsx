
"use client";
import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface Message {
  role: string;
  content: string;
}

interface ChatSession {
  chat_id: string;
  title: string;
  scholarship?: string;
  essay_type?: string;
}

interface EssayType {
  essay_type_id: string;
  essay_type_name: string;
  category_id?: string;
}

interface FollowUpSuggestion {
  followup_id: number;
  category_id: number;
  essay_type_id: number;
  suggestion_key: string;
  suggestion_text: string;
}

interface FollowUpExample {
  essay_type_id: number;
  example_id: number;
  example_key: string;
  example_text: string;
}

interface PromptData {
  scholarship_categories: { category_id: string; category_name: string }[];
  essay_types: EssayType[];
  initial_suggestion_prompts: { essay_type_id: string; prompt_text: string }[];
  follow_up_suggestions: FollowUpSuggestion[];
  follow_up_examples: FollowUpExample[];
}

const Sidebar: React.FC<{
  chatSessions: ChatSession[];
  currentChatId: string | null;
  setCurrentChatId: (id: string) => void;
  handleNewChat: () => void;
  handleRenameChat: (chatId: string, newTitle: string) => void;
  handleDeleteChat: (chatId: string) => void;
  isSidebarVisible: boolean;
  toggleSidebar: () => void;
}> = ({
  chatSessions,
  currentChatId,
  setCurrentChatId,
  handleNewChat,
  handleRenameChat,
  handleDeleteChat,
  isSidebarVisible,
  toggleSidebar,
}) => {
  return (
    <aside
      className={`bg-gray-50 border-r border-gray-200 h-screen p-6 flex flex-col transition-all duration-300 ${
        isSidebarVisible ? 'w-80' : 'w-0 hidden'
      }`}
    >
      <button
        onClick={toggleSidebar}
        className="w-full bg-gray-600 text-white py-2 rounded-lg mb-6 hover:bg-gray-700 transition-colors"
      >
        {isSidebarVisible ? 'Hide Sidebar' : 'Show Sidebar'}
      </button>
      {isSidebarVisible && (
        <>
          <button
            onClick={handleNewChat}
            className="w-full bg-blue-600 text-white py-2 rounded-lg mb-6 hover:bg-blue-700 transition-colors"
          >
            + New Chat
          </button>
          <div className="flex-1 overflow-y-auto space-y-3">
            {chatSessions.map(chat => (
              <div
                key={chat.chat_id}
                className={`p-4 bg-white rounded-lg shadow-sm cursor-pointer hover:bg-gray-100 transition-colors ${
                  currentChatId === chat.chat_id ? 'border-l-4 border-blue-600' : ''
                }`}
                onClick={() => setCurrentChatId(chat.chat_id)}
              >
                <div className="flex justify-between items-center">
                  <input
                    type="text"
                    value={chat.title}
                    onChange={e => handleRenameChat(chat.chat_id, e.target.value)}
                    className="flex-1 bg-transparent border-none focus:outline-none text-sm font-medium text-gray-800"
                  />
                  <button
                    onClick={e => {
                      e.stopPropagation();
                      handleDeleteChat(chat.chat_id);
                    }}
                    className="text-red-500 hover:text-red-600"
                  >
                    🗑️
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {chat.scholarship ? `${chat.scholarship} - ${chat.essay_type}` : 'New Chat'}
                </p>
              </div>
            ))}
          </div>
        </>
      )}
    </aside>
  );
};

const ChatArea: React.FC<{
  currentChatId: string | null;
  messages: Message[];
  promptData: PromptData;
  selectedScholarship: string | null;
  setSelectedScholarship: (value: string | null) => void;
  selectedEssayType: string | null;
  setSelectedEssayType: (value: string | null) => void;
  userInput: string;
  setUserInput: (value: string) => void;
  hasSubmittedInitial: boolean;
  setHasSubmittedInitial: (value: boolean) => void;
  handleSubmitInitial: () => void;
  selectedFollowUp: string;
  setSelectedFollowUp: (value: string) => void;
  followUpInput: string;
  setFollowUpInput: (value: string) => void;
  handleFollowUpSubmit: () => void;
  isLoading: boolean;
  error: string | null;
  isSidebarVisible: boolean;
  toggleSidebar: () => void;
}> = ({
  currentChatId,
  messages,
  promptData,
  selectedScholarship,
  setSelectedScholarship,
  selectedEssayType,
  setSelectedEssayType,
  userInput,
  setUserInput,
  hasSubmittedInitial,
  setHasSubmittedInitial,
  handleSubmitInitial,
  selectedFollowUp,
  setSelectedFollowUp,
  followUpInput,
  setFollowUpInput,
  handleFollowUpSubmit,
  isLoading,
  error,
  isSidebarVisible,
  toggleSidebar,
}) => {
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const availableEssayTypes = promptData.essay_types.filter(e => e.category_id === selectedScholarship);

  // Debug: Log available essay types to ensure they match database essay_type_id values
  console.log('availableEssayTypes:', availableEssayTypes);

  const essayTypeNum = Number(selectedEssayType);
  const followUpOptions = promptData.follow_up_suggestions.filter(s => s.essay_type_id === essayTypeNum);
  const selectedExample = promptData.follow_up_examples.find(
    ex =>
      ex.essay_type_id === essayTypeNum &&
      ex.example_key === followUpOptions.find(f => f.followup_id === Number(selectedFollowUp))?.suggestion_key
  );

  useEffect(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, [messages]);

  return (
    <main className="flex-1 p-8 bg-white flex flex-col h-screen">
      <div className="max-w-3xl mx-auto w-full flex-1 flex flex-col">
        {!isSidebarVisible && (
          <button
            onClick={toggleSidebar}
            className="mb-4 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
          >
            Show Sidebar
          </button>
        )}
        <h1 className="text-2xl font-semibold text-gray-800 mb-4">Scholarship Writing Mentor</h1>
        {currentChatId && (
          <p className="text-sm text-gray-500 mb-6">
            {selectedScholarship && selectedEssayType
              ? `Working on ${promptData.scholarship_categories.find(c => c.category_id === selectedScholarship)?.category_name} - ${promptData.essay_types.find(e => e.essay_type_id === selectedEssayType)?.essay_type_name}`
              : 'Select a scholarship and essay type to begin'}
          </p>
        )}
        <div className="flex space-x-4 mb-6">
          <select
            value={selectedScholarship || ''}
            onChange={e => setSelectedScholarship(e.target.value)}
            className="flex-1 p-3 border border-gray-200 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
            disabled={hasSubmittedInitial}
          >
            <option value="">Select Scholarship</option>
            {promptData.scholarship_categories.map(cat => (
              <option key={cat.category_id} value={cat.category_id}>
                {cat.category_name}
              </option>
            ))}
          </select>
          <select
            value={selectedEssayType || ''}
            onChange={e => setSelectedEssayType(e.target.value)}
            className="flex-1 p-3 border border-gray-200 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
            disabled={!selectedScholarship || hasSubmittedInitial}
          >
            <option value="">Select Essay Type</option>
            {availableEssayTypes.map(et => (
              <option key={et.essay_type_id} value={et.essay_type_id}>
                {et.essay_type_name}
              </option>
            ))}
          </select>
        </div>
        <div
          ref={messagesContainerRef}
          className="flex-1 bg-gray-50 border border-gray-200 rounded-lg p-6 overflow-y-auto mb-6"
        >
          {messages.map((msg, idx) => (
            <div key={idx} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'} mb-4`}>
              <div
                className={`max-w-md p-4 rounded-lg shadow-sm ${
                  msg.role === 'user' ? 'bg-blue-100 text-gray-800' : 'bg-white text-gray-800'
                }`}
              >
                <ReactMarkdown remarkPlugins={[remarkGfm]}>{msg.content}</ReactMarkdown>
              </div>
            </div>
          ))}
          {isLoading && <div className="text-center text-gray-500 text-sm">Loading...</div>}
        </div>
        {!hasSubmittedInitial && selectedEssayType ? (
          <div className="space-y-4">
            <textarea
              value={userInput}
              onChange={e => setUserInput(e.target.value)}
              rows={5}
              placeholder="Paste your essay or statement..."
              className="w-full p-4 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            />
            <div className="text-xs text-gray-500">{userInput.length}/20 characters</div>
            <button
              onClick={handleSubmitInitial}
              className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400"
              disabled={isLoading || userInput.length < 20}
            >
              Submit Initial Essay
            </button>
          </div>
        ) : hasSubmittedInitial ? (
          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-gray-800">Follow-Up Suggestions</h2>
            <div className="grid grid-cols-2 gap-4">
              {followUpOptions.map(s => (
                <button
                  key={s.followup_id}
                  onClick={() => setSelectedFollowUp(s.followup_id.toString())}
                  className={`p-4 border border-gray-200 rounded-lg text-left hover:bg-gray-100 transition-colors ${
                    selectedFollowUp === s.followup_id.toString() ? 'bg-blue-50 border-blue-500' : ''
                  }`}
                >
                  <h3 className="text-sm font-medium text-gray-800">{s.suggestion_key}</h3>
                  <p className="text-xs text-gray-500 mt-1">{s.suggestion_text}</p>
                </button>
              ))}
            </div>
            {selectedFollowUp && (
              <div className="space-y-4">
                {selectedExample && (
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-700">
                    <strong>Example:</strong> {selectedExample.example_text}
                  </div>
                )}
                <textarea
                  value={followUpInput}
                  onChange={e => setFollowUpInput(e.target.value)}
                  rows={4}
                  placeholder="Enter your follow-up details..."
                  className="w-full p-4 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
                <button
                  onClick={handleFollowUpSubmit}
                  className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400"
                  disabled={isLoading || followUpInput.length < 10}
                >
                  Submit Follow-Up
                </button>
              </div>
            )}
          </div>
        ) : null}
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">{error}</div>
        )}
      </div>
    </main>
  );
};

export default function ChatPage() {
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [promptData, setPromptData] = useState<PromptData | null>(null);
  const [selectedScholarship, setSelectedScholarship] = useState<string | null>(null);
  const [selectedEssayType, setSelectedEssayType] = useState<string | null>(null);
  const [userInput, setUserInput] = useState('');
  const [hasSubmittedInitial, setHasSubmittedInitial] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [selectedFollowUp, setSelectedFollowUp] = useState('');
  const [followUpInput, setFollowUpInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSidebarVisible, setIsSidebarVisible] = useState(true);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const toggleSidebar = () => {
    setIsSidebarVisible(prev => !prev);
  };

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    const initialize = async () => {
      setChatSessions([]);
      setCurrentChatId(null);
      setMessages([]);
      setError(null);
      await checkAuthStatus();
    };
    initialize();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const res = await axios.get<{ authenticated: boolean }>(`${process.env.NEXT_PUBLIC_API_URL}/check-auth`, { withCredentials: true });
      if (res.data.authenticated) {
        setIsAuthenticated(true);
        fetchPromptData();
        fetchChatList();
      } else {
        setIsAuthenticated(false);
      }
    } catch (err) {
      setError('Authentication check failed');
      setIsAuthenticated(false);
    }
  };

  const fetchPromptData = async () => {
    try {
      const res = await axios.get<PromptData>(`${process.env.NEXT_PUBLIC_API_URL}/prompts`, { withCredentials: true });
      setPromptData(res.data);
      // Debug: Log the entire promptData to verify API response
      console.log('Fetched promptData:', res.data);
    } catch (err) {
      setError('Failed to load prompt data');
    }
  };

  const fetchChatList = async () => {
    try {
      const res = await axios.get<{ chat_sessions: ChatSession[] }>(`${process.env.NEXT_PUBLIC_API_URL}/chat/list`, { withCredentials: true });
      const uniqueSessions = Array.from(
        new Map(res.data.chat_sessions.map((chat: ChatSession) => [chat.chat_id, chat])).values()
      ) as ChatSession[];
      setChatSessions(uniqueSessions);
      if (!currentChatId && uniqueSessions.length > 0) {
        const latest = uniqueSessions.find(chat => /^\d+$/.test(chat.chat_id))?.chat_id;
        if (latest) {
          setCurrentChatId(latest);
        } else {
          setError('No valid chat sessions found');
        }
      } else if (uniqueSessions.length === 0) {
        setError('No chat sessions available');
        setCurrentChatId(null);
      }
    } catch (err) {
      setError('Failed to load chat list');
    }
  };

  const fetchChatHistory = async (chatId: string) => {
    if (!chatId || !/^\d+$/.test(chatId)) {
      setError('Invalid chat ID');
      setMessages([]);
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    try {
      const res = await axios.get<{ messages: Message[] }>(`${process.env.NEXT_PUBLIC_API_URL}/chat/${chatId}`, { withCredentials: true });
      setMessages(res.data.messages);
      const chat = chatSessions.find(c => c.chat_id === chatId);
      if (chat?.scholarship && chat?.essay_type) {
        setSelectedScholarship(chat.scholarship);
        setSelectedEssayType(chat.essay_type);
        setHasSubmittedInitial(res.data.messages.length > 0);
      } else {
        setSelectedScholarship(null);
        setSelectedEssayType(null);
        setHasSubmittedInitial(false);
      }
      setError(null);
    } catch (err) {
      setError('Failed to load chat history');
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewChat = async () => {
    try {
      const res = await axios.post<{ chat_id: string }>(
        `${process.env.NEXT_PUBLIC_API_URL}/chat/new`,
        { title: `Chat ${chatSessions.length + 1}` },
        { withCredentials: true }
      );
      const newChatId = res.data.chat_id;
      const newChat = { chat_id: newChatId, title: `Chat ${chatSessions.length + 1}` };
      const updatedSessions = chatSessions.filter(chat => chat.chat_id !== newChatId);
      setChatSessions([newChat, ...updatedSessions]);
      setCurrentChatId(newChatId);
      setMessages([]);
      setSelectedScholarship(null);
      setSelectedEssayType(null);
      setHasSubmittedInitial(false);
      setError(null);
    } catch (err) {
      setError('Failed to create new chat');
    }
  };

  const handleRenameChat = (chatId: string, newTitle: string) => {
    const updatedSessions = chatSessions.map(chat =>
      chat.chat_id === chatId ? { ...chat, title: newTitle } : chat
    );
    setChatSessions(updatedSessions);
  };

  const handleDeleteChat = async (chatId: string) => {
    if (!window.confirm('Are you sure you want to delete this chat?')) return;
    try {
      await axios.delete(`${process.env.NEXT_PUBLIC_API_URL}/chat/${chatId}`, { withCredentials: true });
      const updatedSessions = chatSessions.filter(chat => chat.chat_id !== chatId);
      setChatSessions(updatedSessions);
      if (currentChatId === chatId) {
        setCurrentChatId(null);
        setMessages([]);
        setSelectedScholarship(null);
        setSelectedEssayType(null);
        setHasSubmittedInitial(false);
        setError(updatedSessions.length > 0 ? null : 'No chat sessions available');
      }
    } catch (err) {
      setError('Failed to delete chat');
    }
  };

  const handleSubmitInitial = async () => {
    if (!userInput || userInput.length < 20 || !currentChatId || !selectedScholarship || !selectedEssayType) {
      setError('Please fill all fields with at least 20 characters');
      return;
    }

    // Debug: Log promptData and selectedEssayType to diagnose undefined issue
    console.log('promptData:', promptData);
    console.log('selectedEssayType:', selectedEssayType);

    // Retrieve the prompt from promptData or use a fallback
    let promptText = `Please review the attached personal statement and evaluate it based on the following criteria specified in the application instructions:

        1) Motivations for Applying 
        2) Educational Background 
        3) Significant Experiences 
        7) Person or Event that had a significant influence on you
        4) Extracurricular Activities 
        5) Awards, publications you have made, or skill you have acquired
        6) Other(e.g, extracurricular activities, community service, or work experiences)

    Task for AI:
    - Highlight which sections meet each requirement, and show that text below.
    - Explain why each section meets the requirement, by using and showing examples from the text.
    - Point out any missing elements that the instructions require but are not addressed.
    - Create a table to show which requirements are met (✓ for met, ✗ for not met) and the percentage of how well the statement meets the requirements on the button of the chat.
    `;
    if (promptData?.initial_suggestion_prompts) {
      const foundPrompt = promptData.initial_suggestion_prompts.find(p => p.essay_type_id === selectedEssayType);
      if (foundPrompt) {
        promptText = foundPrompt.prompt_text;
      } else {
        console.warn(`No prompt found for essay_type_id: ${selectedEssayType}. Using fallback prompt.`);
      }
    } else {
      console.warn('promptData.initial_suggestion_prompts is not available. Using fallback prompt.');
    }

    // Validate promptText to ensure it's not undefined or placeholder text
    if (!promptText || promptText.includes('ajsdhfkhajslf')) {
      promptText = 'Provide feedback and suggestions to improve the submitted essay for clarity, structure, and impact.';
      console.warn('Prompt was invalid or placeholder text. Using fallback prompt.');
    }

    // Prepend the prompt to the user's input
    const combinedInput = `Prompt: ${promptText}\n\nUser Essay:\n${userInput}`;

    // Debug: Log the combined input to verify
    console.log('combinedInput:', combinedInput);

    setMessages(prev => [...prev, { role: 'user', content: userInput }, { role: 'assistant', content: '' }]);
    setIsLoading(true);
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/chat/${currentChatId}/submit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          input: combinedInput,
          scholarship: selectedScholarship,
          category: selectedEssayType,
        }),
      });
      const reader = res.body?.getReader();
      const decoder = new TextDecoder('utf-8');
      let partial = '';
      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          partial += decoder.decode(value);
          setMessages(prev => {
            const updated = [...prev];
            updated[updated.length - 1] = { role: 'assistant', content: partial };
            return updated;
          });
          scrollToBottom();
        }
      }
      setHasSubmittedInitial(true);
      setUserInput('');
      const updatedSessions = chatSessions.map(chat =>
        chat.chat_id === currentChatId
          ? { ...chat, scholarship: selectedScholarship, essay_type: selectedEssayType }
          : chat
      );
      setChatSessions(updatedSessions);
      setError(null);
    } catch (err) {
      setError('Failed to submit initial essay');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFollowUpSubmit = async () => {
    if (!followUpInput || followUpInput.length < 10 || !selectedFollowUp) {
      setError('Please select a suggestion and enter at least 10 characters');
      return;
    }
    const essayTypeNum = Number(selectedEssayType);
    const selectedSuggestion = promptData?.follow_up_suggestions.find(opt =>
      opt.followup_id === Number(selectedFollowUp) && opt.essay_type_id === essayTypeNum
    );
    const promptText = selectedSuggestion?.suggestion_text || '';
    setMessages(prev => [...prev, { role: 'user', content: followUpInput }, { role: 'assistant', content: '' }]);
    setIsLoading(true);
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/chat/${currentChatId}/followup`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          input: followUpInput,
          prompt_text: promptText,
          scholarship: selectedScholarship,
          category: selectedEssayType,
        }),
      });
      const reader = res.body?.getReader();
      const decoder = new TextDecoder('utf-8');
      let partial = '';
      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          partial += decoder.decode(value);
          setMessages(prev => {
            const updated = [...prev];
            updated[updated.length - 1] = { role: 'assistant', content: partial };
            return updated;
          });
          scrollToBottom();
        }
      }
      setFollowUpInput('');
      setSelectedFollowUp('');
      setError(null);
    } catch (err) {
      setError('Failed to submit follow-up');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (currentChatId && /^\d+$/.test(currentChatId)) {
      fetchChatHistory(currentChatId);
    } else if (currentChatId) {
      setError('Invalid chat ID');
      setMessages([]);
    }
  }, [currentChatId]);

  if (isAuthenticated === null) return <div className="p-8 text-gray-500">Checking authentication...</div>;
  if (!isAuthenticated)
    return (
      <div className="p-8 text-red-500">
        You must <a href="/login" className="underline text-blue-500">log in</a>.
      </div>
    );
  if (!promptData) return <div className="p-8 text-gray-500">Loading prompt data...</div>;

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar
        chatSessions={chatSessions}
        currentChatId={currentChatId}
        setCurrentChatId={setCurrentChatId}
        handleNewChat={handleNewChat}
        handleRenameChat={handleRenameChat}
        handleDeleteChat={handleDeleteChat}
        isSidebarVisible={isSidebarVisible}
        toggleSidebar={toggleSidebar}
      />
      <ChatArea
        currentChatId={currentChatId}
        messages={messages}
        promptData={promptData}
        selectedScholarship={selectedScholarship}
        setSelectedScholarship={setSelectedScholarship}
        selectedEssayType={selectedEssayType}
        setSelectedEssayType={setSelectedEssayType}
        userInput={userInput}
        setUserInput={setUserInput}
        hasSubmittedInitial={hasSubmittedInitial}
        setHasSubmittedInitial={setHasSubmittedInitial}
        handleSubmitInitial={handleSubmitInitial}
        selectedFollowUp={selectedFollowUp}
        setSelectedFollowUp={setSelectedFollowUp}
        followUpInput={followUpInput}
        setFollowUpInput={setFollowUpInput}
        handleFollowUpSubmit={handleFollowUpSubmit}
        isLoading={isLoading}
        error={error}
        isSidebarVisible={isSidebarVisible}
        toggleSidebar={toggleSidebar}
      />
    </div>
  );
}
