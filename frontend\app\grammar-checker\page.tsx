"use client";

import React, { useMemo, useCallback, useState, useEffect, JSX } from "react";
import { createEditor, Descendant, Transforms, BaseEditor } from "slate";
import { Slate, Editable, withReact, ReactEditor, RenderLeafProps } from "slate-react";
import { withH<PERSON><PERSON>, HistoryEditor } from "slate-history";

// Extend Slate types
declare module "slate" {
  interface CustomTypes {
    Editor: BaseEditor & ReactEditor & HistoryEditor;
    Element: CustomElement;
    Text: CustomText;
  }
}

interface CustomElement {
  type: string;
  children: Descendant[];
}

interface CustomText {
  added: any;
  removed: any;
  text: string;
}

interface CorrectionAPI {
  incorrect: string;
  suggestion: string;
  start: number;
  end: number;
  priority: "high" | "medium" | "low";
  category: "misspelling" | "correctness" | "clarity" | "engagement" | "delivery";
}

interface GrammarCheckResponse {
  corrections: CorrectionAPI[];
  error?: string;
}

interface Correction extends CorrectionAPI {
  isPro?: boolean;
  id?: string;
}

// Load and save handled corrections from/to localStorage (client-side only)
const loadHandledCorrections = (): Set<string> => {
  if (typeof window !== "undefined" && window.localStorage) {
    const stored = localStorage.getItem("handledCorrections");
    return stored ? new Set(JSON.parse(stored)) : new Set();
  }
  return new Set(); // Default to empty set on server-side
};

const saveHandledCorrections = (handledCorrections: Set<string>) => {
  if (typeof window !== "undefined" && window.localStorage) {
    localStorage.setItem("handledCorrections", JSON.stringify([...handledCorrections]));
  }
};

const initialValue: Descendant[] = [
  {
    type: "paragraph",
    children: [{ text: "", added: null, removed: null }],
  },
];

const GrammarCheckerPage = () => {
  const editor = useMemo(() => withHistory(withReact(createEditor())), []);
  const [value, setValue] = useState<Descendant[]>(initialValue);
  const [corrections, setCorrections] = useState<Correction[]>([]);
  const [selectedCorrection, setSelectedCorrection] = useState<Correction | null>(null);
  const [isFetching, setIsFetching] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [handledCorrections, setHandledCorrections] = useState<Set<string>>(new Set()); // Initialize empty
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Load handledCorrections from localStorage only on client-side mount
  useEffect(() => {
    setHandledCorrections(loadHandledCorrections());
  }, []);

  // Simulate loading progress
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isFetching) {
      setLoadingProgress(0);
      interval = setInterval(() => {
        setLoadingProgress((prev) => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 200);
    }
    return () => clearInterval(interval);
  }, [isFetching]);

  const checkGrammar = async (text: string) => {
    if (!text.trim() || isFetching) return;

    // Check cache first (client-side only)
    if (typeof window !== "undefined" && window.localStorage) {
      const cacheKey = `grammarCache_${text}`;
      const cachedResult = localStorage.getItem(cacheKey);
      if (cachedResult) {
        const data: GrammarCheckResponse = JSON.parse(cachedResult);
        const mappedCorrections: Correction[] = data.corrections
          .map((corr) => ({
            ...corr,
            id: `${corr.incorrect}-${corr.start}-${corr.end}`,
            isPro: Math.random() > 0.5,
          }))
          .filter((corr) => !handledCorrections.has(corr.id!));
        setCorrections(mappedCorrections);
        setErrorMessage(null);
        return;
      }
    }

    setIsFetching(true);
    setErrorMessage(null);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/grammar-check`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ text, tone: "Professional" }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
      }

      const data: GrammarCheckResponse = await response.json();

      if (data.error) {
        console.error("API returned an error:", data.error);
        setErrorMessage("Failed to fetch suggestions: " + data.error);
        return;
      }

      const uniqueCorrections = new Map<string, CorrectionAPI>();
      for (const corr of data.corrections) {
        const key = `${corr.incorrect}-${corr.start}-${corr.end}`;
        if (!uniqueCorrections.has(key)) {
          corr.suggestion = corr.suggestion?.trim() || corr.incorrect;
          uniqueCorrections.set(key, corr);
        }
      }

      const mappedCorrections: Correction[] = Array.from(uniqueCorrections.values())
        .map((corr) => ({
          ...corr,
          id: `${corr.incorrect}-${corr.start}-${corr.end}`,
          isPro: Math.random() > 0.5,
        }))
        .filter((corr) => !handledCorrections.has(corr.id!));

      setCorrections(mappedCorrections);
      if (typeof window !== "undefined" && window.localStorage) {
        localStorage.setItem(`grammarCache_${text}`, JSON.stringify(data));
      }
    } catch (error) {
      console.error("Grammar check failed:", error);
      setErrorMessage("Failed to fetch suggestions. Please try again later.");
    } finally {
      setIsFetching(false);
      setLoadingProgress(100);
    }
  };

  const handleCheckGrammar = () => {
    const fullText = value
      .flatMap((node) => ("children" in node ? node.children : []))
      .map((node) => ("text" in node ? node.text : ""))
      .join("\n");
    checkGrammar(fullText);
  };

  const escapeRegExp = (string: string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  };

  const renderLeaf = useCallback(
    (props: RenderLeafProps) => {
      const { attributes, children, leaf } = props;
      const text = leaf.text as string;

      if (!corrections.length) return <span {...attributes}>{children}</span>;

      const sortedCorrections = [...corrections].sort((a, b) => a.start - b.start);
      let elements: JSX.Element[] = [];
      let lastIndex = 0;

      for (const correction of sortedCorrections) {
        const regex = new RegExp(`\\b${escapeRegExp(correction.incorrect)}\\b`, "gi");
        const match = regex.exec(text);
        if (match) {
          const start = match.index;
          const end = start + correction.incorrect.length;

          if (start >= lastIndex) {
            if (start > lastIndex) {
              elements.push(<span key={lastIndex}>{text.slice(lastIndex, start)}</span>);
            }

            const underlineStyle =
              correction.category === "misspelling" || correction.category === "correctness"
                ? { borderBottom: "2px solid red", cursor: "pointer" }
                : correction.category === "clarity"
                ? { borderBottom: "2px solid blue", cursor: "pointer" }
                : correction.category === "engagement"
                ? { borderBottom: "2px solid purple", cursor: "pointer" }
                : { borderBottom: "2px solid green", cursor: "pointer" };

            elements.push(
              <span
                key={start}
                style={underlineStyle}
                onClick={() => setSelectedCorrection(correction)}
              >
                {text.slice(start, end)}
              </span>
            );
            lastIndex = end;
          }
        }
      }

      if (lastIndex < text.length) {
        elements.push(<span key={lastIndex}>{text.slice(lastIndex)}</span>);
      }

      return <span {...attributes}>{elements}</span>;
    },
    [corrections, setSelectedCorrection]
  );

  const applyCorrection = (correction: Correction) => {
    const { incorrect, suggestion, id } = correction;

    for (const [nodeIndex, node] of value.entries()) {
      if ("children" in node) {
        for (const [childIndex, child] of node.children.entries()) {
          if ("text" in child && child.text.includes(incorrect)) {
            const text = child.text;
            const regex = new RegExp(`\\b${escapeRegExp(incorrect)}\\b`, "gi");
            const match = regex.exec(text);
            if (match) {
              const start = match.index;
              const end = start + incorrect.length;

              Transforms.select(editor, {
                anchor: { path: [nodeIndex, childIndex], offset: start },
                focus: { path: [nodeIndex, childIndex], offset: end },
              });

              Transforms.insertText(editor, suggestion);

              setHandledCorrections((prev) => {
                const newSet = new Set(prev);
                newSet.add(id!);
                saveHandledCorrections(newSet);
                return newSet;
              });

              setCorrections((prev) => prev.filter((c) => c.id !== id));
              setSelectedCorrection(null);
              return;
            }
          }
        }
      }
    }
  };

  const dismissCorrection = (correction: Correction) => {
    const { id } = correction;

    setHandledCorrections((prev) => {
      const newSet = new Set(prev);
      newSet.add(id!);
      saveHandledCorrections(newSet);
      return newSet;
    });

    setCorrections((prev) => prev.filter((c) => c.id !== id));
    setSelectedCorrection(null);
  };

  const resetHandledCorrections = () => {
    setHandledCorrections(new Set());
    if (typeof window !== "undefined" && window.localStorage) {
      localStorage.removeItem("handledCorrections");
    }
    const fullText = value
      .flatMap((node) => ("children" in node ? node.children : []))
      .map((node) => ("text" in node ? node.text : ""))
      .join("\n");
    if (fullText.trim()) {
      checkGrammar(fullText);
    }
  };

  const toggleBold = () => {
    alert("Bold toggle clicked! Implement Slate mark API for bold.");
  };

  return (
    <div style={styles.container}>
      <div style={styles.editorContainer}>
        <div style={styles.editorHeader}>
          <h2 style={styles.editorTitle}>Grammar</h2>
          <div style={styles.buttonGroup}>
            <button style={styles.button} onClick={toggleBold}>
              <strong>B</strong>
            </button>
            <button style={styles.button}>
              <em>I</em>
            </button>
            <button style={styles.button}>H1</button>
            <button style={styles.button}>H2</button>
            <button style={styles.button}>
              <span style={{ textDecoration: "underline" }}>U</span>
            </button>
            <button style={styles.button}>
              <span style={{ fontFamily: "monospace" }}>C</span>
            </button>
            <button style={styles.button}>
              <span>≡</span>
            </button>
            <button style={styles.button} onClick={handleCheckGrammar}>
              Check Grammar
            </button>
          </div>
        </div>
        <div style={styles.editorBox}>
          <Slate editor={editor} initialValue={value} onChange={setValue}>
            <Editable
              renderLeaf={renderLeaf}
              placeholder="Start typing to check grammar..."
              style={styles.editable}
            />
          </Slate>
        </div>
        <div style={styles.wordCount}>
          {value
            .flatMap((node) => ("children" in node ? node.children : []))
            .reduce(
              (count, node) =>
                count + ("text" in node ? node.text.split(/\s+/).length : 0),
              0
            )}{" "}
          words
        </div>
      </div>

      <div style={styles.sidebar}>
        <div style={styles.sidebarHeader}>
          <h2 style={styles.sidebarTitle}>
            Review suggestions ({corrections.length})
          </h2>
          <div style={styles.scoreCircle}>60</div>
        </div>

        <div style={styles.categoryTabs}>
          <div style={styles.categoryItem}>
            <span style={{ ...styles.categoryDot, backgroundColor: "red" }}></span>
            <span style={styles.categoryText}>Correctness</span>
          </div>
          <div style={styles.categoryItem}>
            <span style={{ ...styles.categoryDot, backgroundColor: "blue" }}></span>
            <span style={styles.categoryText}>Clarity</span>
          </div>
          <div style={styles.categoryItem}>
            <span style={{ ...styles.categoryDot, backgroundColor: "purple" }}></span>
            <span style={styles.categoryText}>Engagement</span>
          </div>
          <div style={styles.categoryItem}>
            <span style={{ ...styles.categoryDot, backgroundColor: "green" }}></span>
            <span style={styles.categoryText}>Delivery</span>
          </div>
        </div>

        {errorMessage ? (
          <div style={{ textAlign: "center", padding: "20px", color: "red" }}>
            {errorMessage}
          </div>
        ) : isFetching ? (
          <div style={{ textAlign: "center", padding: "20px", color: "#666" }}>
            <div style={styles.progressBarContainer}>
              <div
                style={{
                  ...styles.progressBar,
                  width: `${loadingProgress}%`,
                }}
              ></div>
            </div>
            <p>Loading... {Math.round(loadingProgress)}%</p>
          </div>
        ) : corrections.length === 0 ? (
          <div style={{ textAlign: "center", padding: "20px", color: "#666" }}>
            No suggestions available. Click "Check Grammar" to analyze your text.
          </div>
        ) : (
          <div style={styles.suggestionsContainer}>
            {corrections.map((correction) => (
              <div
                key={correction.id}
                style={{
                  ...styles.suggestionItem,
                  ...(selectedCorrection === correction ? styles.suggestionSelected : {}),
                }}
              >
                <div style={styles.suggestionHeader}>
                  <span
                    style={{
                      ...styles.suggestionIcon,
                      backgroundColor:
                        correction.category === "misspelling" ||
                        correction.category === "correctness"
                          ? "#ff4d4f"
                          : correction.category === "clarity"
                          ? "#1890ff"
                          : correction.category === "engagement"
                          ? "#9b59b6"
                          : "#2ecc71",
                    }}
                  >
                    {correction.category === "misspelling"
                      ? "M"
                      : correction.category === "correctness"
                      ? "C"
                      : correction.category === "clarity"
                      ? "CL"
                      : correction.category === "engagement"
                      ? "E"
                      : "D"}
                  </span>
                  <span style={styles.suggestionTitle}>
                    {correction.category === "misspelling"
                      ? "Misspellings"
                      : correction.category === "correctness"
                      ? "Correctness"
                      : correction.category === "clarity"
                      ? "Remove wordiness"
                      : correction.category === "engagement"
                      ? "Unsplit the infinitive"
                      : "Delivery"}
                  </span>
                </div>
                <p style={styles.suggestionText}>
                  {correction.incorrect} →{" "}
                  <strong>{correction.suggestion || "Remove"}</strong>
                </p>
                <div style={styles.suggestionButtons}>
                  <button
                    style={styles.acceptButton}
                    onClick={() => applyCorrection(correction)}
                  >
                    Accept
                  </button>
                  <button
                    style={styles.dismissButton}
                    onClick={() => dismissCorrection(correction)}
                  >
                    Dismiss
                  </button>
                  <button style={styles.moreButton}>•••</button>
                </div>
              </div>
            ))}
          </div>
        )}

        <div style={styles.checkButtonContainer}>
          <button style={styles.checkButton}>
            <span>📜</span> Check for plagiarism and AI text
          </button>
        </div>
      </div>
    </div>
  );
};

// Styles (unchanged)
const styles: { [key: string]: React.CSSProperties } = {
  container: {
    display: "flex",
    minHeight: "100vh",
    fontFamily: "Arial, sans-serif",
  },
  editorContainer: {
    flex: 1,
    padding: "20px",
    backgroundColor: "#f5f7fa",
  },
  editorHeader: {
    display: "flex",
    justifyContent: "space-between",
    marginBottom: "10px",
  },
  editorTitle: {
    fontSize: "18px",
    color: "#333",
    fontWeight: "bold",
  },
  buttonGroup: {
    display: "flex",
    gap: "5px",
  },
  button: {
    padding: "5px 10px",
    background: "none",
    border: "none",
    fontSize: "14px",
    cursor: "pointer",
    color: "#666",
  },
  editorBox: {
    border: "1px solid #e1e4e8",
    padding: "15px",
    minHeight: "400px",
    backgroundColor: "white",
    borderRadius: "5px",
    boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
  },
  editable: {
    minHeight: "400px",
    fontSize: "16px",
    lineHeight: "1.6",
    outline: "none",
  },
  wordCount: {
    marginTop: "10px",
    fontSize: "14px",
    color: "#666",
  },
  sidebar: {
    width: "370px",
    padding: "20px",
    backgroundColor: "#fff",
    borderLeft: "1px solid #e1e4e8",
    display: "flex",
    flexDirection: "column",
  },
  sidebarHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "20px",
  },
  sidebarTitle: {
    fontSize: "16px",
    color: "#333",
    fontWeight: "bold",
  },
  scoreCircle: {
    width: "50px",
    height: "50px",
    borderRadius: "50%",
    backgroundColor: "#ffeb3b",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    fontSize: "18px",
    fontWeight: "bold",
    color: "#333",
  },
  categoryTabs: {
    display: "flex",
    gap: "10px",
    marginBottom: "20px",
  },
  categoryItem: {
    display: "flex",
    alignItems: "center",
    gap: "5px",
  },
  categoryDot: {
    width: "10px",
    height: "10px",
    borderRadius: "50%",
  },
  categoryText: {
    fontSize: "14px",
    color: "#333",
  },
  suggestionsContainer: {
    flex: 1,
    overflowY: "auto",
    maxHeight: "calc(100vh - 300px)",
    marginBottom: "20px",
  },
  suggestionItem: {
    padding: "10px",
    borderBottom: "1px solid #eee",
  },
  suggestionSelected: {
    backgroundColor: "#e6f7ff",
  },
  suggestionHeader: {
    display: "flex",
    alignItems: "center",
    gap: "10px",
  },
  suggestionIcon: {
    width: "20px",
    height: "20px",
    borderRadius: "50%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    color: "white",
    fontSize: "12px",
  },
  suggestionTitle: {
    fontWeight: "bold",
    color: "#333",
    fontSize: "14px",
  },
  suggestionText: {
    margin: "5px 0",
    fontSize: "14px",
    color: "#666",
  },
  suggestionButtons: {
    display: "flex",
    gap: "10px",
    marginTop: "10px",
  },
  acceptButton: {
    padding: "5px 10px",
    backgroundColor: "#52c41a",
    color: "white",
    border: "none",
    borderRadius: "3px",
    cursor: "pointer",
    fontSize: "12px",
  },
  dismissButton: {
    padding: "5px 10px",
    backgroundColor: "#f0f0f0",
    color: "#333",
    border: "none",
    borderRadius: "3px",
    cursor: "pointer",
    fontSize: "12px",
  },
  moreButton: {
    padding: "5px",
    background: "none",
    border: "none",
    cursor: "pointer",
    color: "#666",
    fontSize: "12px",
  },
  checkButtonContainer: {
    marginTop: "20px",
    textAlign: "center",
  },
  checkButton: {
    padding: "10px 20px",
    backgroundColor: "#fff",
    border: "1px solid #e1e4e8",
    borderRadius: "5px",
    cursor: "pointer",
    fontSize: "14px",
    color: "#666",
    display: "flex",
    alignItems: "center",
    gap: "5px",
    margin: "0 auto",
  },
  progressBarContainer: {
    width: "100%",
    height: "10px",
    backgroundColor: "#e0e0e0",
    borderRadius: "5px",
    marginBottom: "10px",
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    backgroundColor: "#1890ff",
    transition: "width 0.2s ease-in-out",
  },
};

export default GrammarCheckerPage;