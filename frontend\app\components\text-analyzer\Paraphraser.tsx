'use client';

import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { FiRefreshCw, FiCopy, FiX, FiMaximize2, FiMinimize2 } from 'react-icons/fi';

interface ParaphrasingComponentProps {
  text: string;
  onParaphrased?: (paraphrasedText: string) => void;
}

const API_BASE_URL = 'http://localhost:5000'; // Update this to your Flask API URL

const Paraphraser: React.FC<ParaphrasingComponentProps> = ({ text, onParaphrased }) => {
  // State for paraphrasing options
  const [paraphraseStyle, setParaphraseStyle] = useState<string>('standard');
  const [synonymLevel, setSynonymLevel] = useState<number>(2); // 0-4 scale
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [hasOverflow, setHasOverflow] = useState(false);

  // State for sentence selection and options
  const [selectedSentence, setSelectedSentence] = useState<string | null>(null);
  const [showSentenceOptions, setShowSentenceOptions] = useState<boolean>(false);
  const [sentenceOptions, setSentenceOptions] = useState<string[]>([]);
  const [sentenceSelectionCoords, setSentenceSelectionCoords] = useState<{top: number, left: number} | null>(null);

  // Ref for the options popup
  const optionsPopupRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Close options popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (optionsPopupRef.current && !optionsPopupRef.current.contains(event.target as Node)) {
        setShowSentenceOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // State for tracking the original text and modified text
  const [modifiedText, setModifiedText] = useState<string>(text);
  const textContainerRef = useRef<HTMLDivElement>(null);

  // Update modifiedText when text prop changes
  useEffect(() => {
    setModifiedText(text);
  }, [text]);

  // Check for overflow when text changes
  useEffect(() => {
    const checkForOverflow = () => {
      if (textContainerRef.current) {
        const hasVerticalOverflow = textContainerRef.current.scrollHeight > textContainerRef.current.clientHeight;
        setHasOverflow(hasVerticalOverflow);
      }
    };

    checkForOverflow();
    // Add resize listener to recheck on window resize
    window.addEventListener('resize', checkForOverflow);
    return () => window.removeEventListener('resize', checkForOverflow);
  }, [modifiedText]);

  // Function to generate paraphrase options for a sentence

  const generateParaphraseOptions = async (sentence: string) => {
    try {
      setIsLoading(true);
      setShowSentenceOptions(true);
      const response = await axios.post(`${API_BASE_URL}/paraphrasing`, {
        
        text: sentence,
        tone: paraphraseStyle,
        synonym_level: synonymLevel,
      });
      console.log('Generating paraphrase options for sentence:', response.data);

      if (response.status === 200) {
        const { paraphrases } = response.data;
        if (paraphrases && paraphrases.length > 0) {
          const options = paraphrases[0].versions.map((version: any) => version.text);
          setSentenceOptions(options);
        } else {
          throw new Error('No paraphrases returned');
        }
      } else {
        throw new Error('API request failed');
      }
    } catch (error: any) {
      console.error('Error generating paraphrase options:', error);
      setError(error.response?.data?.error || 'Failed to generate paraphrase options');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to apply a paraphrase option to the text
  const applyParaphraseOption = (originalSentence: string, newSentence: string) => {
    try {
      // Use regex to find the exact sentence with its punctuation
      const escapedSentence = originalSentence.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const sentenceRegex = new RegExp(`(${escapedSentence})`, 'g');
      const updatedText = modifiedText.replace(sentenceRegex, newSentence);

      if (updatedText === modifiedText) {
        console.warn('No changes were made to the text. The sentence might not have been found exactly.');
        console.log('Original sentence:', originalSentence);
        console.log('New sentence:', newSentence);
        console.log('Text:', modifiedText);
      } else {
        setModifiedText(updatedText);
        if (onParaphrased) {
          onParaphrased(updatedText);
        }
      }
    } catch (error) {
      console.error('Error applying paraphrase option:', error);
      setError('Failed to apply paraphrase option');
    }
  };

  // Function to copy text to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(modifiedText); // Changed to copy modifiedText
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div ref={containerRef} className="h-full overflow-hidden flex flex-col">
      {/* Top mode tabs */}
      <div className="border-b border-gray-200 mb-2">
        <div className="flex flex-wrap">
          {['Standard', 'Fluency', 'Creative', 'Expand', 'Shorten', 'Custom'].map((mode) => (
            <button
              key={mode}
              onClick={() => setParaphraseStyle(mode.toLowerCase())}
              className={`px-4 py-2 text-sm transition-all ${
                paraphraseStyle === mode.toLowerCase()
                  ? 'text-blue-600 font-medium border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              {mode}
            </button>
          ))}

          <div className="ml-auto flex items-center px-4">
            <span className="text-sm text-gray-700 mr-2">Synonyms:</span>
            <div className="relative w-32 h-4 bg-gray-200 rounded-full">
              <div
                className="absolute top-0 left-0 h-full bg-blue-600 rounded-full"
                style={{ width: `${(synonymLevel / 4) * 100}%` }}
              ></div>
              <div
                className="absolute top-0 right-0 w-4 h-4 bg-white border-2 border-blue-600 rounded-full shadow-md cursor-pointer"
                style={{ right: `${100 - (synonymLevel / 4) * 100}%`, transform: 'translateX(50%)' }}
                onMouseDown={(e) => {
                  const slider = e.currentTarget.parentElement;
                  if (!slider) return;

                  const handleMouseMove = (e: MouseEvent) => {
                    if (!slider) return;
                    const rect = slider.getBoundingClientRect();
                    const x = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
                    setSynonymLevel(Math.round(x * 4));
                  };

                  const handleMouseUp = () => {
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                  };

                  document.addEventListener('mousemove', handleMouseMove);
                  document.addEventListener('mouseup', handleMouseUp);
                }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Rephrase button and controls */}
      <div className="mb-2 flex items-center">
        <button
          onClick={async () => {
            if (!text.trim()) return;

            setIsLoading(true);
            setError(null);
            try {
              const response = await axios.post(`${API_BASE_URL}/paraphrasing`, {
                text,
                tone: paraphraseStyle,
                synonym_level: synonymLevel,
              });

              if (response.status === 200) {
                const { paraphrases } = response.data;
                if (paraphrases && paraphrases.length > 0) {
                  // Combine the first version of each paraphrased sentence
                  const paraphrasedText = paraphrases
                    .map((para: any) => para.versions[0]?.text || para.sentence)
                    .join(' ');
                  setModifiedText(paraphrasedText);
                  if (onParaphrased) {
                    onParaphrased(paraphrasedText);
                  }
                } else {
                  throw new Error('No paraphrases returned');
                }
              } else {
                throw new Error('API request failed');
              }
            } catch (error: any) {
              console.error('Error paraphrasing text:', error);
              setError(error.response?.data?.error || 'Failed to paraphrase text. Please try again.');
            } finally {
              setIsLoading(false);
            }
          }}
          disabled={isLoading || !text.trim()}
          className={`px-4 py-1.5 rounded-md text-sm font-medium ${
            isLoading || !text.trim()
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700 transition-colors'
          }`}
        >
          {isLoading ? (
            <span className="flex items-center">
              <FiRefreshCw className="animate-spin mr-2" size={14} />
              Rephrasing...
            </span>
          ) : (
            'Rephrase'
          )}
        </button>

        <div className="flex ml-4 space-x-2">
          <button className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded">
            <FiRefreshCw size={16} />
          </button>
          <button className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded" onClick={copyToClipboard}>
            <FiCopy size={16} />
          </button>
        </div>

        <button
          onClick={() => setIsFullScreen(!isFullScreen)}
          className="ml-auto p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
          title={isFullScreen ? "Exit full screen" : "Full screen"}
        >
          {isFullScreen ? <FiMinimize2 size={16} /> : <FiMaximize2 size={16} />}
        </button>
      </div>

      {/* Error message display */}
      {error && (
        <div className="mb-2 p-3 bg-red-50 text-red-700 rounded-md">
          <p className="text-sm">{error}</p>
        </div>
      )}

      {/* Main content area */}
      <div className={`${isFullScreen ? 'fixed inset-0 z-50 bg-white p-6' : 'flex-1 w-full'} font-sans overflow-auto`}>
        {/* Text display with sentence selection */}
        <div className="bg-white rounded-lg border border-gray-200 p-4 relative flex-1">
          {hasOverflow && (
            <div className="absolute top-2 right-2 z-10 text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded-md flex items-center">
              <span className="mr-1">Scroll for more</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 5v14M5 12l7 7 7-7"/>
              </svg>
            </div>
          )}
          <div
            ref={textContainerRef}
            className="text-gray-900 text-base h-full max-h-[calc(100vh-400px)] min-h-auto relative"
            style={{ height: 'calc(100vh - 180px)' }}
          >
            {hasOverflow && (
              <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none" />
            )}

            {/* Warning for very long text */}
            {(() => {
              const sentenceCount = modifiedText.split(/[.!?]+/).filter(Boolean).length;
              if (sentenceCount > 100) {
                return (
                  <div className="absolute top-0 left-0 right-0 bg-yellow-50 text-yellow-800 text-xs p-2 border-b border-yellow-200">
                    This text is very long ({sentenceCount} sentences). Some sentences may not be displayed for performance reasons.
                  </div>
                );
              }
              return null;
            })()}
            {/* Split text into sentences for selection */}
            <div className={`${modifiedText.split(/[.!?]+/).filter(Boolean).length > 100 ? 'pt-8' : ''}`}>
              {(() => {
                const sentences: {text: string, startIndex: number, endIndex: number}[] = [];
                const regex = /[^.!?]+[.!?]+/g;
                let match;

                while ((match = regex.exec(modifiedText)) !== null) {
                  sentences.push({
                    text: match[0].trim(),
                    startIndex: match.index,
                    endIndex: match.index + match[0].length
                  });
                }

                const maxSentences = 1000;
                const hasTooManySentences = sentences.length > maxSentences;

                if (hasTooManySentences) {
                  console.warn(`Text has ${sentences.length} sentences, limiting to ${maxSentences} for performance.`);
                }

                const displaySentences = hasTooManySentences
                  ? sentences.slice(0, maxSentences)
                  : sentences;

                return displaySentences.map((sentence, sentenceIndex) => {
                  const isSelected = selectedSentence === sentence.text;

                  return (
                    <span
                      key={sentenceIndex}
                      className={`cursor-pointer py-1 px-0.5 rounded ${isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'}`}
                      onClick={() => {
                        setSentenceSelectionCoords({
                          top: 0,
                          left: 0
                        });
                        setSelectedSentence(sentence.text);
                        generateParaphraseOptions(sentence.text);
                      }}
                    >
                      {sentence.text.split(' ').map((word, wordIndex) => {
                        const isChanged = (word.length > 3 && wordIndex % 3 === 0) ||
                                         (word.length > 5 && wordIndex % 5 === 0);
                        return (
                          <span
                            key={wordIndex}
                            className={isChanged ? 'text-red-500 font-medium' : ''}
                          >
                            {word}{' '}
                          </span>
                        );
                      })}
                    </span>
                  );
                });
              })()}
            </div>
          </div>

          {/* Paraphrase options popup */}
          {selectedSentence && sentenceSelectionCoords && showSentenceOptions && (
            <div
              ref={optionsPopupRef}
              className="fixed bg-white shadow-xl rounded-md border border-gray-200 z-50 w-[90%] max-w-[500px]"
              style={{
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)'
              }}
            >
              <button
                onClick={() => setShowSentenceOptions(false)}
                className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 z-20"
              >
                <FiX />
              </button>

              <div className="p-4 space-y-3 max-h-[300px] overflow-y-auto">
                {isLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <FiRefreshCw className="animate-spin mr-2" size={20} />
                    <span>Generating options...</span>
                  </div>
                ) : (
                  sentenceOptions.map((option, index) => (
                    <div
                      key={index}
                      className="p-3 text-sm border border-gray-200 rounded-md cursor-pointer hover:bg-blue-50"
                      onClick={() => {
                        if (selectedSentence) {
                          applyParaphraseOption(selectedSentence, option);
                        }
                        setShowSentenceOptions(false);
                        setSelectedSentence(null);
                      }}
                    >
                      {option.split(' ').map((word, wordIndex) => {
                        const normalizedWord = word.replace(/[.,!?;:'"()]/g, '').toLowerCase();
                        const normalizedSentence = selectedSentence?.toLowerCase().replace(/[.,!?;:'"()]/g, '');
                        const isChanged = normalizedWord.length > 2 &&
                                         normalizedSentence &&
                                         !normalizedSentence.includes(normalizedWord);
                        return (
                          <span
                            key={wordIndex}
                            className={isChanged ? 'text-red-500 font-medium' : ''}
                          >
                            {word}{' '}
                          </span>
                        );
                      })}
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Word/sentence count footer */}
      <div className="mt-2 flex justify-between items-center text-sm text-gray-500 pt-2">
        <div className="flex items-center space-x-3">
          <span>
            {modifiedText.split(/\s+/).filter(Boolean).length} Words
          </span>
          <span>
            {modifiedText.length} Characters
          </span>
          <span>
            {modifiedText.split(/[.!?]+/).filter(Boolean).length} Sentences
          </span>
        </div>

        <div className="flex items-center space-x-2">
          <button className="p-1 hover:bg-gray-100 rounded">
            <FiRefreshCw size={16} />
          </button>
          <button className="p-1 hover:bg-gray-100 rounded" onClick={copyToClipboard}>
            <FiCopy size={16} />
          </button>
        </div>
      </div>

      {/* Legend */}
      <div className="mt-1 flex items-center justify-center space-x-6 text-xs text-gray-600">
        <div className="flex items-center">
          <span className="w-2 h-2 bg-red-500 rounded-full mr-1"></span>
          <span>Changed Words</span>
        </div>
        <div className="flex items-center">
          <span className="w-2 h-2 bg-yellow-500 rounded-full mr-1"></span>
          <span>Structural Changes</span>
        </div>
        <div className="flex items-center">
          <span className="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
          <span>Longest Unchanged Words</span>
        </div>
      </div>
    </div>
  );
};

export default Paraphraser;