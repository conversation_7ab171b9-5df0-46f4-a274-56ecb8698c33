'use client';

import React, { useCallback } from 'react';
import { <PERSON><PERSON>heck, FiX, FiAlertTriangle } from 'react-icons/fi';
import { motion, AnimatePresence } from 'framer-motion';

// Types for our analysis results
export type IssueType = 'grammar' | 'spelling' | 'ai-content';

export interface Issue {
  id: string;
  type: IssueType;
  original: string;
  suggestion: string;
  position: { start: number; end: number };
}

interface SuggestionPanelProps {
  issues: Issue[];
  isAnalyzing: boolean;
  onAccept: (issue: Issue) => void;
  onIgnore: (issueId: string) => void;
  onAcceptAll: () => void;
  onHide: () => void;
  width: number;
  onStartResize: (e: React.MouseEvent) => void;
}

const SuggestionPanel: React.FC<SuggestionPanelProps> = ({
  issues,
  isAnalyzing,
  onAccept,
  onIgnore,
  onAcceptAll,
  onHide,
  width,
  onStartResize,
}) => {
  return (
    <div 
      className="relative"
      style={{ width: `${width}px` }}
    >
      <div 
        className="absolute left-0 top-0 bottom-0 w-1 cursor-ew-resize bg-gray-200 hover:bg-blue-400"
        onMouseDown={onStartResize}
      ></div>
      
      <div className="h-full p-4 flex flex-col">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-medium">Suggestion</h2>
          <button 
            onClick={onHide}
            className="text-gray-500 hover:text-gray-700"
          >
            Hide
          </button>
        </div>
        
        <AnimatePresence>
          {issues.length > 0 && (
            <motion.div 
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center mb-4 bg-yellow-50 p-3 rounded-md"
            >
              <div className="flex-shrink-0 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-white mr-3">
                <FiAlertTriangle />
              </div>
              <div>
                <span className="font-medium">Issues Found</span>
              </div>
              <button 
                onClick={onAcceptAll}
                className="ml-auto bg-blue-600 text-white px-3 py-1 rounded text-sm"
              >
                Accept all
              </button>
            </motion.div>
          )}
        </AnimatePresence>
        
        <div className="flex-1 overflow-y-auto">
          <AnimatePresence>
            {issues.map((issue) => (
              <motion.div 
                key={issue.id}
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className="mb-4 bg-white p-3 rounded-md border border-gray-200"
              >
                <div className="mb-2">
                  <span className="text-sm text-gray-600">replace with</span>
                </div>
                <div className="flex items-center mb-2">
                  <span className="line-through text-red-500 mr-2">{issue.original}</span>
                  <span className="text-green-600 font-medium">{issue.suggestion}</span>
                </div>
                <div className="flex space-x-2">
                  <button 
                    onClick={() => onAccept(issue)}
                    className="flex-1 bg-blue-600 text-white px-3 py-1 rounded-md text-sm flex items-center justify-center"
                  >
                    <FiCheck className="mr-1" /> Accept
                  </button>
                  <button 
                    onClick={() => onIgnore(issue.id)}
                    className="flex-1 bg-white border border-gray-300 text-gray-700 px-3 py-1 rounded-md text-sm flex items-center justify-center"
                  >
                    <FiX className="mr-1" /> Ignore
                  </button>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {issues.length === 0 && !isAnalyzing && (
            <div className="text-center text-gray-500 mt-8">
              <p>No issues found or all issues resolved.</p>
              <p className="text-sm mt-2">Click "Analyze Text" to check for issues.</p>
            </div>
          )}
          
          {isAnalyzing && (
            <div className="flex flex-col items-center justify-center h-40">
              <svg className="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="mt-3 text-gray-600">Analyzing your text...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SuggestionPanel;
