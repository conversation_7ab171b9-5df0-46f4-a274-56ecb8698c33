"use client";

import React, { use<PERSON>emo, useCallback, useState } from "react";
import { createEditor } from "slate";
import { Slate, Editable, withReact } from "slate-react";
import { withHistory } from "slate-history";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>bulb } from "react-icons/fa";
import { ThumbsUpIcon, ThumbsDownIcon } from "lucide-react";

import { Descendant } from "slate";

type CustomText = Descendant & {
  text: string;
  added?: boolean;
  removed?: boolean;
};

type CustomElement = Descendant & {
  type: "paragraph";
  children: CustomText[];
};

type AIAnalysis = {
  start: number;
  end: number;
  text: string;
  isAI: boolean;
  aiScore: number;
};

const INITIAL_VALUE: CustomElement[] = [
  {
    type: "paragraph",
    children: [{ text: "", added: false, removed: false }],
  },
];

const EditorSection = ({
  editor,
  value,
  onChange,
  aiAnalysis,
  loading,
  error,
  wordCount,
  onAnalyze,
}: {
  editor: any;
  value: CustomElement[];
  onChange: (value: CustomElement[]) => void;
  aiAnalysis: AIAnalysis[];
  loading: boolean;
  error: string | null;
  wordCount: number;
  onAnalyze: () => void;
}) => {
  const renderLeaf = useCallback(
    ({ attributes, children, leaf }: any) => {
      const aiSection = aiAnalysis.find(
        (section) => leaf.text.includes(section.text) && section.isAI
      );
      return (
        <span
          {...attributes}
          className={`${
            aiSection
              ? "bg-yellow-100 rounded px-1 transition-colors hover:bg-yellow-200"
              : "text-gray-900"
          }`}
          title={aiSection ? `AI Score: ${(aiSection.aiScore * 100).toFixed(2)}%` : ""}
        >
          {children}
        </span>
      );
    },
    [aiAnalysis]
  );

  return (
    <div className="bg-white rounded-xl shadow-md p-6 w-full max-w-3xl">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Text Input</h2>
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
            {wordCount} words
          </span>
          <button
            onClick={onAnalyze}
            disabled={loading}
            className="flex items-center gap-2 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 transition-all"
          >
            <FaRedo className={loading ? "animate-spin" : ""} />
            {loading ? "Analyzing..." : "Analyze"}
          </button>
        </div>
      </div>
      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50 min-h-[400px]">
        {loading && !aiAnalysis.length ? (
          <div className="flex items-center justify-center h-full">
            <FaSpinner className="animate-spin text-3xl text-gray-500" />
          </div>
        ) : error ? (
          <div className="text-red-600 p-4 bg-red-50 rounded">{error}</div>
        ) : (
          <Slate
            editor={editor}
            initialValue={value}
            onChange={(newValue) => {
              try {
                onChange(newValue as CustomElement[]);
              } catch (err) {
                console.error("Error updating Slate value:", err);
              }
            }}
          >
            <Editable
              renderLeaf={renderLeaf}
              placeholder="Paste your text here..."
              className="focus:outline-none text-gray-800 leading-relaxed"
            />
          </Slate>
        )}
      </div>
    </div>
  );
};

const AnalysisSection = ({
  loading,
  error,
  aiAnalysis,
  overallAIScore,
  onFeedback,
  feedbackSubmitted,
}: {
  loading: boolean;
  error: string | null;
  aiAnalysis: AIAnalysis[];
  overallAIScore: number;
  onFeedback: (positive: boolean) => void;
  feedbackSubmitted: boolean;
}) => {
  const aiSegments = aiAnalysis.filter((a) => a.isAI).length;
  const humanSegments = aiAnalysis.length - aiSegments;
  const total = aiAnalysis.length;
  const humanPercentage = total ? (humanSegments / total) * 100 : 0;
  const aiPercentage = total ? (aiSegments / total) * 100 : 0;

  return (
    <div className="bg-white rounded-xl shadow-md p-6 w-full max-w-md h-full overflow-y-auto">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Analysis Results</h2>
      {loading && !aiAnalysis.length ? (
        <div className="flex items-center justify-center h-full">
          <FaSpinner className="animate-spin text-3xl text-gray-500" />
        </div>
      ) : error ? (
        <div className="text-red-600 p-4 bg-red-50 rounded">{error}</div>
      ) : (
        <>
          <div className="text-center mb-6">
            <div className="text-4xl font-bold text-gray-800">
              {overallAIScore.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-500">AI Probability</div>
          </div>
          <div className="space-y-4 mb-6">
            <ProgressBar label="Human Content" percentage={humanPercentage} color="bg-teal-500" />
            <ProgressBar label="AI Content" percentage={aiPercentage} color="bg-yellow-500" />
          </div>
          <div className="border-t pt-4">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Breakdown</h3>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {aiAnalysis.map((segment, index) => (
                <div key={index} className="flex items-center gap-2 text-sm p-2 bg-gray-50 rounded">
                  <FaLightbulb className={segment.isAI ? "text-yellow-500" : "text-teal-500"} />
                  <span className="font-medium">
                    {(segment.aiScore).toFixed(1)}%
                  </span>
                  <span className="text-gray-600 truncate">
                    - {segment.text.substring(0, 50)}{segment.text.length > 50 ? "..." : ""}
                  </span>
                </div>
              ))}
            </div>
          </div>
          <div className="mt-6 border-t pt-4">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Feedback</h3>
            <div className="flex gap-3">
              <button
                onClick={() => onFeedback(true)}
                disabled={feedbackSubmitted}
                className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:bg-gray-400"
              >
                <ThumbsUpIcon className="w-4 h-4" /> Yes
              </button>
              <button
                onClick={() => onFeedback(false)}
                disabled={feedbackSubmitted}
                className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:bg-gray-400"
              >
                <ThumbsDownIcon className="w-4 h-4" /> No
              </button>
            </div>
            {feedbackSubmitted && (
              <p className="text-sm text-gray-600 mt-3">Thanks for your feedback!</p>
            )}
          </div>
        </>
      )}
    </div>
  );
};

const ProgressBar = ({ label, percentage, color }: { label: string; percentage: number; color: string }) => (
  <div>
    <div className="flex justify-between text-sm font-medium text-gray-700 mb-2">
      <span>{label}</span>
      <span>{percentage.toFixed(1)}%</span>
    </div>
    <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
      <div className={`h-full ${color} transition-all duration-500`} style={{ width: `${percentage}%` }} />
    </div>
  </div>
);

const AIDetectorPage = () => {
  const editor = useMemo(() => withHistory(withReact(createEditor())), []);
  const [value, setValue] = useState<CustomElement[]>(INITIAL_VALUE);
  const [aiAnalysis, setAIAnalysis] = useState<AIAnalysis[]>([]);
  const [overallAIScore, setOverallAIScore] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [wordCount, setWordCount] = useState(0);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  const calculatePositions = (items: any[]): AIAnalysis[] => {
    let currentPosition = 0;
    try {
      return items.map((item) => {
        const start = currentPosition;
        const end = start + item.text.length;
        currentPosition = end + 1;
        return {
          start,
          end,
          text: item.text,
          isAI: item.prediction === "ai-generated",
          aiScore: item.ai_score,
        };
      });
    } catch {
      return [];
    }
  };

  const fetchAIAnalysis = async () => {
    setLoading(true);
    setError(null);
    const text = value.map((node) =>
      node.children.map((c) => c.text).join(" ")
    ).join("\n");

    if (!text || text.length <= 20) {
      setError("Please enter at least 20 characters.");
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/detect-ai`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ text }),
      });

      if (!response.ok) throw new Error(await response.text());
      const result = await response.json();
      const analysis = calculatePositions(result.sentences);
      setAIAnalysis(analysis);
      setOverallAIScore(result.ai_score);
      calculateWordCount();
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred.");
      setAIAnalysis([]);
      setOverallAIScore(0);
    } finally {
      setLoading(false);
    }
  };

  const calculateWordCount = () => {
    const text = value
      .map((node) => node.children.map((child) => child.text).join(" "))
      .join(" ");
    setWordCount(text.trim().split(/\s+/).filter(Boolean).length);
  };

  const handleFeedback = (positive: boolean) => {
    setFeedbackSubmitted(true);
    console.log(`Feedback: ${positive ? "positive" : "negative"}`);
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-6">
      <div className="w-full max-w-7xl flex flex-col lg:flex-row justify-center gap-6">
        <div className="w-full lg:w-2/3 flex justify-center">
          <EditorSection
            editor={editor}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
              calculateWordCount();
              setAIAnalysis([]);
              setOverallAIScore(0);
            }}
            aiAnalysis={aiAnalysis}
            loading={loading}
            error={error}
            wordCount={wordCount}
            onAnalyze={fetchAIAnalysis}
          />
        </div>
        <div className="w-full lg:w-1/3 flex justify-center">
          <AnalysisSection
            loading={loading}
            error={error}
            aiAnalysis={aiAnalysis}
            overallAIScore={overallAIScore}
            onFeedback={handleFeedback}
            feedbackSubmitted={feedbackSubmitted}
          />
        </div>
      </div>
    </div>
  );
};

export default AIDetectorPage;
