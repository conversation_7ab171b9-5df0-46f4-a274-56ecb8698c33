'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useChat } from '../../context/ChatContext';
import { FiSend } from 'react-icons/fi';

export default function ChatInput() {
  const { sendMessage, isLoading, mode } = useChat();
  const [input, setInput] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [input]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      sendMessage(input);
      setInput('');
    }
  };

  const getPlaceholder = () => {
    switch (mode) {
      case 'criteria':
        return 'Enter your scholarship essay...';
      case 'detector':
        return 'Paste text to check for AI content...';
      case 'grammar':
        return 'Enter text for grammar checking...';
      case 'paraphraser':
        return 'Enter text to paraphrase...';
      default:
        return 'Type a message...';
    }
  };

  return (
    <div className="border-t border-gray-200 bg-white p-4">
      <form onSubmit={handleSubmit} className="max-w-3xl mx-auto">
        <div className="relative flex items-end bg-white border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
          <textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={getPlaceholder()}
            rows={1}
            className="w-full p-3 pr-12 focus:outline-none resize-none max-h-48"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
            disabled={isLoading}
          />
          <button
            type="submit"
            className={`absolute right-3 bottom-3 p-1 rounded-full ${
              input.trim() && !isLoading
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
            disabled={!input.trim() || isLoading}
          >
            <FiSend size={18} />
          </button>
        </div>
        <p className="text-xs text-gray-500 mt-2 text-center">
          {mode === 'criteria' && 'Analyze your essay against scholarship criteria'}
          {mode === 'detector' && 'Check if text appears to be AI-generated'}
          {mode === 'grammar' && 'Fix grammar, spelling, and punctuation'}
          {mode === 'paraphraser' && 'Rewrite text while preserving meaning'}
        </p>
      </form>
    </div>
  );
}