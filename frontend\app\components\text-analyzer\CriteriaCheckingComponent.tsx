'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';
import { FiSearch, FiCheckCircle, FiAlertCircle, FiXCircle, FiX, FiRefreshCw } from 'react-icons/fi';

interface CriteriaCheckingComponentProps {
  text: string;
  onAnalysisComplete?: (result: CriteriaCheckResult) => void;
}

interface CriteriaCheckResult {
  content: string;
  overall_score: number;
  matches: {
    criteria: string;
    match_level: 'high' | 'medium' | 'low';
    explanation: string;
    score: number;
  }[];
  embedding_available?: boolean;
  analysis_details?: {
    word_count: number;
    max_words?: number;
    scholarship: string;
    essay_type: string;
    criteria_count: number;
    embedding_similarity?: number;
  };
}

interface ScholarshipType {
  id: string;
  name: string;
  logo: string;
  description: string;
}

interface EssayType {
  id: string;
  name: string;
  scholarship_id: string;
  criteria: string[];
  instructions: string;
  max_words?: number;
  criteria_count?: number;
}

const CriteriaCheckingComponent: React.FC<CriteriaCheckingComponentProps> = ({
  text,
  onAnalysisComplete
}) => {
  const [scholarshipType, setScholarshipType] = useState<string>('');
  const [essayType, setEssayType] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<CriteriaCheckResult | null>(null);
  const [showScholarshipModal, setShowScholarshipModal] = useState(true); // Show immediately
  const [showWritingTypeModal, setShowWritingTypeModal] = useState(false);
  const [selectedScholarship, setSelectedScholarship] = useState<ScholarshipType | null>(null);
  const [scholarshipTypes, setScholarshipTypes] = useState<ScholarshipType[]>([]);
  const [loadingScholarships, setLoadingScholarships] = useState(false);

  const [essayTypes, setEssayTypes] = useState<EssayType[]>([]);
  const [loadingEssayTypes, setLoadingEssayTypes] = useState(false);

  // API Functions
  const fetchScholarships = async () => {
    setLoadingScholarships(true);
    try {
      const response = await axios.get('/api/v1/scholarships');
      setScholarshipTypes(response.data.scholarships || []);
    } catch (error) {
      console.error('Failed to fetch scholarships:', error);
      setError('Failed to load scholarships. Please try again.');
    } finally {
      setLoadingScholarships(false);
    }
  };

  const fetchEssayTypes = async (scholarshipId: string) => {
    setLoadingEssayTypes(true);
    try {
      const response = await axios.get(`/api/v1/scholarships/${scholarshipId}/essays`);
      setEssayTypes(response.data.essay_types || []);
    } catch (error) {
      console.error('Failed to fetch essay types:', error);
      setError('Failed to load essay types. Please try again.');
    } finally {
      setLoadingEssayTypes(false);
    }
  };

  // Load scholarships on component mount
  useEffect(() => {
    fetchScholarships();
  }, []);

  // Load essay types when scholarship is selected
  useEffect(() => {
    if (scholarshipType) {
      fetchEssayTypes(scholarshipType);
    }
  }, [scholarshipType]);

  // Filtered essay types based on selected scholarship
  const filteredEssayTypes = essayTypes.filter(
    type => type.scholarship_id === scholarshipType
  );

  // Get selected essay type details
  const selectedEssayType = essayTypes.find(type => type.id === essayType);

  // Function to handle scholarship selection
  const handleScholarshipSelect = (scholarship: ScholarshipType) => {
    setSelectedScholarship(scholarship);
    setScholarshipType(scholarship.id);
    setShowScholarshipModal(false);
    setShowWritingTypeModal(true);
    console.log('Scholarship selected:', scholarship.id); // Debug log
  };

  // Function to handle writing type selection
  const handleWritingTypeSelect = (writingType: EssayType) => {
    setEssayType(writingType.id);
    setShowWritingTypeModal(false);
    console.log('Writing type selected:', writingType.id); // Debug log
    console.log('Current scholarship type:', scholarshipType); // Debug log

    // Automatically start analysis after writing type is selected
    setTimeout(() => {
      // Call analyzeCriteria with the specific values to ensure they're available
      analyzeCriteria(scholarshipType, writingType.id);
    }, 500);
  };

  // Function to refresh and restart the process
  const handleRefresh = () => {
    setResult(null);
    setIsLoading(false);
    setError(null);
    setSelectedScholarship(null);
    setScholarshipType('');
    setEssayType('');
    setShowScholarshipModal(true);
  };

  // Function to analyze text against criteria
  const analyzeCriteria = async (scholarshipTypeParam?: string, essayTypeParam?: string) => {
    const currentScholarshipType = scholarshipTypeParam || scholarshipType;
    const currentEssayType = essayTypeParam || essayType;

    if (!text.trim()) {
      setError('Please enter some text to analyze');
      return;
    }

    if (!currentScholarshipType || !currentEssayType) {
      setError('Please select a scholarship and writing type first');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await axios.post('/api/v1/criteria-check', {
        text: text.trim(),
        scholarship_type: currentScholarshipType,
        essay_type: currentEssayType
      });

      const result: CriteriaCheckResult = response.data;
      setResult(result);

      // Log embedding availability for debugging
      if (result.embedding_available) {
        console.log('✅ Enhanced with embedding similarity analysis');
      }

      if (onAnalysisComplete) {
        onAnalysisComplete(result);
      }
    } catch (error) {
      console.error('Criteria check error:', error);

      if (axios.isAxiosError(error)) {
        const errorCode = error.response?.data?.code;
        switch (errorCode) {
          case 'EMPTY_TEXT':
            setError('Please enter your essay text');
            break;
          case 'MISSING_SCHOLARSHIP_TYPE':
            setError('Please select a scholarship type');
            break;
          case 'MISSING_ESSAY_TYPE':
            setError('Please select an essay type');
            break;
          default:
            setError(error.response?.data?.error || 'Analysis failed');
        }
      } else {
        setError('Network error occurred. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Function to get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  // Function to get background color based on score
  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-50 border-green-200';
    if (score >= 60) return 'bg-yellow-50 border-yellow-200';
    return 'bg-red-50 border-red-200';
  };

  // Function to get progress bar color based on score
  const getProgressColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-md font-medium mb-2">Scholarship Criteria Check</h3>

        {/* Selection Status Display */}
        {!selectedScholarship && !isLoading && (
          <div className="p-4 bg-blue-50 rounded-md border border-blue-200 text-center">
            <FiSearch className="mx-auto mb-2 text-blue-500" size={24} />
            <p className="text-sm text-blue-700 font-medium">Please select a scholarship type</p>
            <p className="text-xs text-blue-600 mt-1">The selection popup will appear automatically</p>
          </div>
        )}

        {/* Selected Scholarship Display */}
        {selectedScholarship && !selectedEssayType && !isLoading && (
          <div className="p-3 bg-blue-50 rounded-md border border-blue-200">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-lg">{selectedScholarship.logo}</span>
              <span className="font-medium text-sm">{selectedScholarship.name}</span>
            </div>
            <div className="text-xs text-gray-600 mb-2">{selectedScholarship.description}</div>
            <p className="text-xs text-blue-600 font-medium">Please select writing type...</p>
          </div>
        )}

        {/* Selected Essay Type Display */}
        {selectedEssayType && !isLoading && !result && (
          <div className="p-3 bg-green-50 rounded-md border border-green-200">
            <div className="font-medium text-sm mb-1">{selectedEssayType.name}</div>
            <div className="text-xs text-gray-600 mb-2">{selectedEssayType.instructions}</div>
            <div className="text-xs text-gray-500">
              <strong>Criteria:</strong> {selectedEssayType.criteria.length} evaluation points
            </div>
            <p className="text-xs text-green-600 font-medium mt-2">Starting analysis...</p>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="p-4 bg-gray-50 rounded-md border border-gray-200 text-center">
            <FiSearch className="animate-spin mx-auto mb-2 text-blue-500" size={24} />
            <p className="text-sm text-gray-700 font-medium">Analyzing your text...</p>
            <div className="text-xs text-gray-600 mt-2">
              <div className="flex justify-center items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                <span>Processing text</span>
              </div>
              <div className="flex justify-center items-center gap-2 mt-1">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                <span>Checking criteria</span>
              </div>
              <div className="flex justify-center items-center gap-2 mt-1">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                <span>AI similarity analysis</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="p-3 bg-red-50 text-red-700 rounded-md mb-4">
          {error}
        </div>
      )}

      {/* Scholarship Selection Modal */}
      <AnimatePresence>
        {showScholarshipModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={(e) => e.stopPropagation()} // Prevent closing by clicking outside
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Select Scholarship</h2>
                <div className="text-xs text-gray-500">Required</div>
              </div>

              {loadingScholarships ? (
                <div className="text-center py-8">
                  <FiSearch className="animate-spin mx-auto mb-2 text-blue-500" size={24} />
                  <p className="text-sm text-gray-600">Loading scholarships...</p>
                </div>
              ) : scholarshipTypes.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-sm text-gray-600">No scholarships available</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {scholarshipTypes.map((scholarship) => (
                  <motion.button
                    key={scholarship.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleScholarshipSelect(scholarship)}
                    className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all text-left"
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-3xl">{scholarship.logo}</span>
                      <div>
                        <div className="font-medium text-sm">{scholarship.name}</div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-600">{scholarship.description}</p>
                  </motion.button>
                ))}
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Writing Type Selection Modal */}
      <AnimatePresence>
        {showWritingTypeModal && selectedScholarship && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={(e) => e.stopPropagation()} // Prevent closing by clicking outside
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{selectedScholarship.logo}</span>
                  <h2 className="text-xl font-semibold">{selectedScholarship.name}</h2>
                </div>
                <div className="text-xs text-gray-500">Required</div>
              </div>

              <p className="text-sm text-gray-600 mb-4">Select the type of writing you want to evaluate:</p>

              {loadingEssayTypes ? (
                <div className="text-center py-8">
                  <FiSearch className="animate-spin mx-auto mb-2 text-blue-500" size={24} />
                  <p className="text-sm text-gray-600">Loading essay types...</p>
                </div>
              ) : filteredEssayTypes.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-sm text-gray-600">No essay types available for this scholarship</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredEssayTypes.map((writingType) => (
                  <motion.button
                    key={writingType.id}
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                    onClick={() => handleWritingTypeSelect(writingType)}
                    className="w-full p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all text-left"
                  >
                    <div className="font-medium text-sm mb-2">{writingType.name}</div>
                    <div className="text-xs text-gray-600 mb-2">{writingType.instructions}</div>
                    <div className="text-xs text-gray-500">
                      <strong>Criteria:</strong> {writingType.criteria.length} evaluation points
                    </div>
                  </motion.button>
                ))}
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Results Display */}
      {result && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          {/* Overall Score Header */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-3">
                <h3 className="text-lg font-semibold">Criteria Check</h3>
                <button
                  onClick={handleRefresh}
                  className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Refresh and select new criteria"
                >
                  <FiRefreshCw className="w-4 h-4" />
                </button>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-red-500">{result.overall_score}/100</div>
                <div className="text-sm text-gray-500">Overall Score</div>
              </div>
            </div>

            {/* Overall Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
              <div
                className={`h-3 rounded-full transition-all duration-500 ${getProgressColor(result.overall_score)}`}
                style={{ width: `${result.overall_score}%` }}
              ></div>
            </div>

            <div className="flex justify-between items-center text-sm text-gray-600">
              <span>{selectedEssayType?.name} Evaluation</span>
              {result.embedding_available && (
                <div className="flex items-center gap-1 text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                  <span>🧠</span>
                  <span>AI Enhanced</span>
                </div>
              )}
            </div>
          </div>

          {/* Individual Criteria Results */}
          {result.matches && result.matches.length > 0 && (
            <div className="space-y-3">
              {result.matches.map((match, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`border rounded-lg p-4 ${getScoreBgColor(match.score)}`}
                >
                  <div className="flex justify-between items-start mb-3">
                    <h4 className="font-medium text-sm flex-1">{match.criteria}</h4>
                    <div className={`text-lg font-bold ${getScoreColor(match.score)}`}>
                      {match.score}/100
                    </div>
                  </div>

                  {/* Progress Bar for Individual Criteria */}
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${getProgressColor(match.score)}`}
                      style={{ width: `${match.score}%` }}
                    ></div>
                  </div>

                  <p className="text-sm text-gray-700">{match.explanation}</p>
                </motion.div>
              ))}
            </div>
          )}

          {/* Analysis Details */}
          {result.analysis_details && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-sm mb-3">Analysis Details</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Word Count:</span>
                  <span className="ml-2 font-medium">{result.analysis_details.word_count}</span>
                </div>
                {result.analysis_details.max_words && (
                  <div>
                    <span className="text-gray-500">Recommended:</span>
                    <span className="ml-2 font-medium">{result.analysis_details.max_words} words max</span>
                  </div>
                )}
                <div>
                  <span className="text-gray-500">Criteria Count:</span>
                  <span className="ml-2 font-medium">{result.analysis_details.criteria_count}</span>
                </div>
                {result.analysis_details.embedding_similarity && (
                  <div>
                    <span className="text-gray-500">Similarity Score:</span>
                    <span className="ml-2 font-medium">{result.analysis_details.embedding_similarity.toFixed(1)}/100</span>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="text-sm text-gray-500 mt-4 p-3 bg-gray-50 rounded-md">
            <p>
              <strong>💡 Tip:</strong> Focus on improving criteria with scores below 60 to enhance your overall application strength.
            </p>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CriteriaCheckingComponent;
