'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';
import { FiSearch, FiCheckCircle, FiAlertCircle, FiXCircle, FiX } from 'react-icons/fi';

interface CriteriaCheckingComponentProps {
  text: string;
  onAnalysisComplete?: (result: CriteriaCheckResult) => void;
}

interface CriteriaCheckResult {
  content: string;
  matches: {
    criteria: string;
    match_level: 'high' | 'medium' | 'low';
    explanation: string;
    score: number;
  }[];
  overall_score: number;
}

interface ScholarshipType {
  id: string;
  name: string;
  logo: string;
  description: string;
}

interface EssayType {
  id: string;
  name: string;
  scholarship_id: string;
  criteria: string[];
  instructions: string;
}

const CriteriaCheckingComponent: React.FC<CriteriaCheckingComponentProps> = ({
  text,
  onAnalysisComplete
}) => {
  const [scholarshipType, setScholarshipType] = useState<string>('');
  const [essayType, setEssayType] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<CriteriaCheckResult | null>(null);
  const [showScholarshipModal, setShowScholarshipModal] = useState(false);
  const [showWritingTypeModal, setShowWritingTypeModal] = useState(false);
  const [selectedScholarship, setSelectedScholarship] = useState<ScholarshipType | null>(null);

  // Enhanced scholarship types with logos and descriptions
  const [scholarshipTypes, setScholarshipTypes] = useState<ScholarshipType[]>([
    {
      id: 'gks',
      name: 'GKS (Global Korea Scholarship)',
      logo: '🇰🇷',
      description: 'Korean Government Scholarship Program for international students'
    },
    {
      id: 'fulbright',
      name: 'Fulbright Scholarship',
      logo: '🇺🇸',
      description: 'US Government educational exchange program'
    },
    {
      id: 'chevening',
      name: 'Chevening Scholarship',
      logo: '🇬🇧',
      description: 'UK Government global scholarship programme'
    },
    {
      id: 'erasmus',
      name: 'Erasmus Mundus',
      logo: '🇪🇺',
      description: 'European Union scholarship for joint master programmes'
    },
    {
      id: 'daad',
      name: 'DAAD Scholarship',
      logo: '🇩🇪',
      description: 'German Academic Exchange Service scholarships'
    },
    {
      id: 'australia_awards',
      name: 'Australia Awards',
      logo: '🇦🇺',
      description: 'Australian Government scholarship program'
    },
    {
      id: 'commonwealth',
      name: 'Commonwealth Scholarship',
      logo: '🇬🇧',
      description: 'UK Commonwealth scholarship for developing countries'
    },
    {
      id: 'gates_cambridge',
      name: 'Gates Cambridge Scholarship',
      logo: '🎓',
      description: 'Full-cost scholarship for outstanding applicants from outside the UK'
    },
    {
      id: 'rhodes',
      name: 'Rhodes Scholarship',
      logo: '🏛️',
      description: 'International postgraduate award for selected foreign students to study at Oxford'
    }
  ]);

  const [essayTypes, setEssayTypes] = useState<EssayType[]>([
    {
      id: 'personal_statement',
      name: 'Personal Statement',
      scholarship_id: 'gks',
      criteria: [
        'Motivations with which you apply for this program',
        'Educational background',
        'Significant experiences you have had; persons or events that have had a significant influence on you',
        'Extracurricular activities such as club activities, community service activities or work experiences',
        'Awards you have received, publications you have made, or skills you have acquired'
      ],
      instructions: 'Please type in Korean or in English. The essay must be single spaced within TWO pages, with the font Times New Roman/바탕체/돋움체, size 11. (11 points) The essay should include the following items. Please remove the instructions after reading it.'
    },
    {
      id: 'research_proposal',
      name: 'Research Proposal',
      scholarship_id: 'gks',
      criteria: [
        'Clear research objectives and questions',
        'Literature review and theoretical framework',
        'Methodology and research design',
        'Expected outcomes and significance',
        'Timeline and feasibility'
      ],
      instructions: 'Describe your research plan in detail including objectives, methodology, and expected outcomes.'
    },
    {
      id: 'study_plan',
      name: 'Study Plan',
      scholarship_id: 'gks',
      criteria: [
        'Academic goals and objectives',
        'Course selection rationale',
        'Career development plan',
        'How the program aligns with your goals',
        'Post-graduation plans'
      ],
      instructions: 'Outline your academic plan and how it relates to your career objectives.'
    },
    {
      id: 'recommendation_letter',
      name: 'Recommendation Letter',
      scholarship_id: 'gks',
      criteria: [
        'Academic performance assessment',
        'Character and personal qualities',
        'Leadership and teamwork abilities',
        'Research potential',
        'Specific examples and achievements'
      ],
      instructions: 'Letter should be written by academic or professional references who know you well.'
    },
    // Fulbright Essays
    {
      id: 'fulbright_personal_statement',
      name: 'Personal Statement',
      scholarship_id: 'fulbright',
      criteria: [
        'Clear articulation of academic and professional goals',
        'Demonstration of leadership potential',
        'Cultural adaptability and ambassadorial qualities',
        'Commitment to mutual understanding',
        'Specific knowledge of host country'
      ],
      instructions: 'Describe your academic and professional background, goals, and why you want to study in the host country.'
    },
    {
      id: 'fulbright_research_statement',
      name: 'Research Statement',
      scholarship_id: 'fulbright',
      criteria: [
        'Clear research objectives and methodology',
        'Significance and innovation of research',
        'Feasibility within the grant period',
        'Relevance to host country context',
        'Expected outcomes and impact'
      ],
      instructions: 'Outline your proposed research project, methodology, and expected outcomes.'
    },
    // Chevening Essays
    {
      id: 'chevening_leadership',
      name: 'Leadership Essay',
      scholarship_id: 'chevening',
      criteria: [
        'Demonstration of leadership experience',
        'Impact of leadership activities',
        'Leadership style and approach',
        'Future leadership potential',
        'Specific examples and outcomes'
      ],
      instructions: 'Describe your leadership experience and how you have influenced others to achieve a common goal.'
    },
    {
      id: 'chevening_networking',
      name: 'Networking Essay',
      scholarship_id: 'chevening',
      criteria: [
        'Understanding of networking importance',
        'Examples of successful networking',
        'Building and maintaining relationships',
        'Professional network development',
        'Future networking plans'
      ],
      instructions: 'Explain how you build and maintain relationships and how you plan to use these skills in the future.'
    }
  ]);

  // Filtered essay types based on selected scholarship
  const filteredEssayTypes = essayTypes.filter(
    type => type.scholarship_id === scholarshipType
  );

  // Get selected essay type details
  const selectedEssayType = essayTypes.find(type => type.id === essayType);

  // Function to handle scholarship selection
  const handleScholarshipSelect = (scholarship: ScholarshipType) => {
    setSelectedScholarship(scholarship);
    setScholarshipType(scholarship.id);
    setShowScholarshipModal(false);
    setShowWritingTypeModal(true);
  };

  // Function to handle writing type selection
  const handleWritingTypeSelect = (writingType: EssayType) => {
    setEssayType(writingType.id);
    setShowWritingTypeModal(false);
  };

  // Function to start criteria check process
  const startCriteriaCheck = () => {
    setShowScholarshipModal(true);
  };

  // Function to analyze text against criteria
  const analyzeCriteria = async () => {
    if (!text.trim()) {
      setError('Please enter some text to analyze');
      return;
    }

    if (!scholarshipType || !essayType) {
      setError('Please select a scholarship and writing type first');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Mock analysis result for demonstration
      const mockResult: CriteriaCheckResult = {
        content: 'Analysis completed successfully',
        overall_score: 33,
        matches: selectedEssayType?.criteria.map((criterion, index) => ({
          criteria: criterion,
          match_level: index === 0 ? 'high' : index === 1 ? 'medium' : 'low',
          explanation: `Excellent ${criterion.toLowerCase()} with a compelling approach that effectively captures the reader's attention and sets up your statement perfectly.`,
          score: index === 0 ? 100 : index === 1 ? 60 : 20
        })) || []
      };

      setResult(mockResult);
      if (onAnalysisComplete) {
        onAnalysisComplete(mockResult);
      }
    } catch (err: any) {
      console.error('Criteria check error:', err);
      setError(err.message || 'An error occurred during criteria analysis');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  // Function to get background color based on score
  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-50 border-green-200';
    if (score >= 60) return 'bg-yellow-50 border-yellow-200';
    return 'bg-red-50 border-red-200';
  };

  // Function to get progress bar color based on score
  const getProgressColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-md font-medium mb-2">Scholarship Criteria Check</h3>

        {/* Selection Summary */}
        {selectedScholarship && selectedEssayType && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl">{selectedScholarship.logo}</span>
              <div>
                <div className="font-medium text-sm">{selectedScholarship.name}</div>
                <div className="text-xs text-gray-600">{selectedEssayType.name}</div>
              </div>
            </div>
            <div className="text-xs text-gray-500 mb-2">
              <strong>Instructions:</strong> {selectedEssayType.instructions}
            </div>
            <div className="text-xs text-gray-500">
              <strong>Criteria:</strong> {selectedEssayType.criteria.length} evaluation points
            </div>
          </div>
        )}

        <button
          onClick={scholarshipType && essayType ? analyzeCriteria : startCriteriaCheck}
          disabled={isLoading || !text.trim()}
          className={`flex items-center px-4 py-2 rounded-md w-full justify-center ${
            isLoading || !text.trim()
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : scholarshipType && essayType
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isLoading ? (
            <>
              <FiSearch className="animate-spin mr-2" /> Analyzing...
            </>
          ) : scholarshipType && essayType ? (
            <>
              <FiSearch className="mr-2" /> Analyze Criteria
            </>
          ) : (
            <>
              <FiSearch className="mr-2" /> Select Scholarship & Writing Type
            </>
          )}
        </button>
      </div>

      {error && (
        <div className="p-3 bg-red-50 text-red-700 rounded-md mb-4">
          {error}
        </div>
      )}

      {/* Scholarship Selection Modal */}
      <AnimatePresence>
        {showScholarshipModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowScholarshipModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Select Scholarship</h2>
                <button
                  onClick={() => setShowScholarshipModal(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <FiX size={24} />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {scholarshipTypes.map((scholarship) => (
                  <motion.button
                    key={scholarship.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleScholarshipSelect(scholarship)}
                    className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all text-left"
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-3xl">{scholarship.logo}</span>
                      <div>
                        <div className="font-medium text-sm">{scholarship.name}</div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-600">{scholarship.description}</p>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Writing Type Selection Modal */}
      <AnimatePresence>
        {showWritingTypeModal && selectedScholarship && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowWritingTypeModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{selectedScholarship.logo}</span>
                  <h2 className="text-xl font-semibold">{selectedScholarship.name}</h2>
                </div>
                <button
                  onClick={() => setShowWritingTypeModal(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <FiX size={24} />
                </button>
              </div>

              <p className="text-sm text-gray-600 mb-4">Select the type of writing you want to evaluate:</p>

              <div className="space-y-3">
                {filteredEssayTypes.map((writingType) => (
                  <motion.button
                    key={writingType.id}
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                    onClick={() => handleWritingTypeSelect(writingType)}
                    className="w-full p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all text-left"
                  >
                    <div className="font-medium text-sm mb-2">{writingType.name}</div>
                    <div className="text-xs text-gray-600 mb-2">{writingType.instructions}</div>
                    <div className="text-xs text-gray-500">
                      <strong>Criteria:</strong> {writingType.criteria.length} evaluation points
                    </div>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Results Display */}
      {result && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          {/* Overall Score Header */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Criteria Check</h3>
              <div className="text-right">
                <div className="text-2xl font-bold text-red-500">{result.overall_score}/100</div>
                <div className="text-sm text-gray-500">Overall Score</div>
              </div>
            </div>

            {/* Overall Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
              <div
                className={`h-3 rounded-full transition-all duration-500 ${getProgressColor(result.overall_score)}`}
                style={{ width: `${result.overall_score}%` }}
              ></div>
            </div>

            <div className="text-sm text-gray-600">
              {selectedEssayType?.name} Evaluation
            </div>
          </div>

          {/* Individual Criteria Results */}
          {result.matches && result.matches.length > 0 && (
            <div className="space-y-3">
              {result.matches.map((match, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`border rounded-lg p-4 ${getScoreBgColor(match.score)}`}
                >
                  <div className="flex justify-between items-start mb-3">
                    <h4 className="font-medium text-sm flex-1">{match.criteria}</h4>
                    <div className={`text-lg font-bold ${getScoreColor(match.score)}`}>
                      {match.score}/100
                    </div>
                  </div>

                  {/* Progress Bar for Individual Criteria */}
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${getProgressColor(match.score)}`}
                      style={{ width: `${match.score}%` }}
                    ></div>
                  </div>

                  <p className="text-sm text-gray-700">{match.explanation}</p>
                </motion.div>
              ))}
            </div>
          )}

          <div className="text-sm text-gray-500 mt-4 p-3 bg-gray-50 rounded-md">
            <p>
              <strong>💡 Tip:</strong> Focus on improving criteria with scores below 60 to enhance your overall application strength.
            </p>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CriteriaCheckingComponent;
