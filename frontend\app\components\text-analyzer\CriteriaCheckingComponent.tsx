'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import axios from 'axios';
import { FiSearch, FiCheckCircle, FiAlertCircle, FiXCircle } from 'react-icons/fi';

interface CriteriaCheckingComponentProps {
  text: string;
  onAnalysisComplete?: (result: CriteriaCheckResult) => void;
}

interface CriteriaCheckResult {
  content: string;
  matches: {
    criteria: string;
    match_level: 'high' | 'medium' | 'low';
    explanation: string;
  }[];
}

interface ScholarshipType {
  id: string;
  name: string;
}

interface EssayType {
  id: string;
  name: string;
  scholarship_id: string;
}

const CriteriaCheckingComponent: React.FC<CriteriaCheckingComponentProps> = ({ 
  text, 
  onAnalysisComplete 
}) => {
  const [scholarshipType, setScholarshipType] = useState<string>('academic');
  const [essayType, setEssayType] = useState<string>('personal_statement');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<CriteriaCheckResult | null>(null);
  
  // Scholarship types and essay types
  const [scholarshipTypes, setScholarshipTypes] = useState<ScholarshipType[]>([
    { id: 'academic', name: 'Academic Merit' },
    { id: 'community', name: 'Community Service' },
    { id: 'leadership', name: 'Leadership' },
    { id: 'diversity', name: 'Diversity & Inclusion' },
    { id: 'financial', name: 'Financial Need' }
  ]);
  
  const [essayTypes, setEssayTypes] = useState<EssayType[]>([
    { id: 'personal_statement', name: 'Personal Statement', scholarship_id: 'academic' },
    { id: 'career_goals', name: 'Career Goals', scholarship_id: 'academic' },
    { id: 'challenges', name: 'Overcoming Challenges', scholarship_id: 'academic' },
    { id: 'community_impact', name: 'Community Impact', scholarship_id: 'community' },
    { id: 'leadership_experience', name: 'Leadership Experience', scholarship_id: 'leadership' },
    { id: 'diversity_statement', name: 'Diversity Statement', scholarship_id: 'diversity' },
    { id: 'financial_need', name: 'Financial Need Statement', scholarship_id: 'financial' }
  ]);
  
  // Filtered essay types based on selected scholarship
  const filteredEssayTypes = essayTypes.filter(
    type => type.scholarship_id === scholarshipType
  );
  
  // Effect to set default essay type when scholarship changes
  useEffect(() => {
    const defaultEssayType = filteredEssayTypes[0]?.id;
    if (defaultEssayType) {
      setEssayType(defaultEssayType);
    }
  }, [scholarshipType]);
  
  // Function to analyze text against criteria
  const analyzeCriteria = async () => {
    if (!text.trim()) {
      setError('Please enter some text to analyze');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
      const response = await axios.post(`${API_URL}/generate`, {
        prompt: text,
        scholarship: scholarshipType,
        category: essayType
      });

      if (response.data) {
        setResult(response.data);
        if (onAnalysisComplete) {
          onAnalysisComplete(response.data);
        }
      }
    } catch (err: any) {
      console.error('Criteria check error:', err);
      setError(err.message || 'An error occurred during criteria analysis');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Function to get color based on match level
  const getMatchLevelColor = (level: 'high' | 'medium' | 'low') => {
    switch (level) {
      case 'high':
        return 'bg-green-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };
  
  // Function to get icon based on match level
  const getMatchLevelIcon = (level: 'high' | 'medium' | 'low') => {
    switch (level) {
      case 'high':
        return <FiCheckCircle className="text-green-500" />;
      case 'medium':
        return <FiAlertCircle className="text-yellow-500" />;
      case 'low':
        return <FiXCircle className="text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-md font-medium mb-2">Scholarship Criteria Check</h3>
        
        <div className="space-y-3 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Scholarship Type:
            </label>
            <select
              value={scholarshipType}
              onChange={(e) => setScholarshipType(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              {scholarshipTypes.map((type) => (
                <option key={type.id} value={type.id}>
                  {type.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Essay Type:
            </label>
            <select
              value={essayType}
              onChange={(e) => setEssayType(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              {filteredEssayTypes.map((type) => (
                <option key={type.id} value={type.id}>
                  {type.name}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        <button
          onClick={analyzeCriteria}
          disabled={isLoading || !text.trim()}
          className={`flex items-center px-4 py-2 rounded-md ${
            isLoading || !text.trim() 
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
              : 'bg-green-600 text-white hover:bg-green-700'
          }`}
        >
          {isLoading ? (
            <>
              <FiSearch className="animate-spin mr-2" /> Analyzing...
            </>
          ) : (
            <>
              <FiSearch className="mr-2" /> Analyze Criteria
            </>
          )}
        </button>
      </div>

      {error && (
        <div className="p-3 bg-red-50 text-red-700 rounded-md mb-4">
          {error}
        </div>
      )}

      {result && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          <div className="bg-green-50 p-4 rounded-md">
            <h4 className="text-sm font-medium mb-2">Analysis:</h4>
            <div className="text-gray-800 whitespace-pre-wrap">{result.content}</div>
          </div>

          {result.matches && result.matches.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Criteria Matches:</h4>
              <div className="space-y-3">
                {result.matches.map((match, index) => (
                  <div key={index} className="border border-gray-200 rounded-md p-3">
                    <div className="flex items-center">
                      <div 
                        className={`w-3 h-3 rounded-full mr-2 ${getMatchLevelColor(match.match_level)}`}
                      ></div>
                      <span className="font-medium">{match.criteria}</span>
                      <span className="ml-auto text-xs text-gray-500 flex items-center">
                        {getMatchLevelIcon(match.match_level)}
                        <span className="ml-1">
                          {match.match_level.charAt(0).toUpperCase() + match.match_level.slice(1)} Match
                        </span>
                      </span>
                    </div>
                    <p className="text-sm mt-2 text-gray-600">{match.explanation}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div className="text-sm text-gray-500 mt-2">
            <p>
              <strong>Tip:</strong> To improve your essay, focus on addressing the criteria with low or medium match levels.
            </p>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CriteriaCheckingComponent;
