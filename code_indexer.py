import os
import json
import re
from pathlib import Path

def index_codebase(root_dir, output_file, ignore_patterns=None):
    """
    Index a codebase and save the information to a JSON file.
    
    Args:
        root_dir: Root directory of the codebase
        output_file: Path to save the index
        ignore_patterns: List of regex patterns to ignore
    """
    if ignore_patterns is None:
        ignore_patterns = [
            r'__pycache__',
            r'\.git',
            r'node_modules',
            r'\.next',
            r'\.env',
            r'\.vscode',
            r'\.DS_Store',
            r'\.pyc$',
            r'\.pyo$',
            r'\.pyd$',
            r'\.log$',
        ]
    
    # Compile ignore patterns
    ignore_regex = [re.compile(pattern) for pattern in ignore_patterns]
    
    codebase_index = {
        "files": [],
        "directories": [],
        "summary": {
            "total_files": 0,
            "total_directories": 0,
            "file_types": {}
        }
    }
    
    for root, dirs, files in os.walk(root_dir):
        # Filter out ignored directories
        dirs[:] = [d for d in dirs if not any(pattern.search(d) for pattern in ignore_regex)]
        
        rel_path = os.path.relpath(root, root_dir)
        if rel_path != '.':
            codebase_index["directories"].append(rel_path)
            codebase_index["summary"]["total_directories"] += 1
        
        for file in files:
            # Skip ignored files
            if any(pattern.search(file) for pattern in ignore_regex):
                continue
                
            file_path = os.path.join(rel_path, file)
            if rel_path == '.':
                file_path = file
                
            # Get file extension
            _, ext = os.path.splitext(file)
            ext = ext.lstrip('.')
            if ext:
                codebase_index["summary"]["file_types"][ext] = codebase_index["summary"]["file_types"].get(ext, 0) + 1
            
            # Read file content if it's a text file
            full_path = os.path.join(root, file)
            try:
                if os.path.getsize(full_path) < 1024 * 1024:  # Skip files larger than 1MB
                    with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    file_info = {
                        "path": file_path,
                        "size": os.path.getsize(full_path),
                        "content": content
                    }
                else:
                    file_info = {
                        "path": file_path,
                        "size": os.path.getsize(full_path),
                        "content": "File too large to index"
                    }
            except Exception as e:
                file_info = {
                    "path": file_path,
                    "size": os.path.getsize(full_path),
                    "content": f"Error reading file: {str(e)}"
                }
            
            codebase_index["files"].append(file_info)
            codebase_index["summary"]["total_files"] += 1
    
    # Save to file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(codebase_index, f, indent=2)
    
    print(f"Codebase indexed successfully. Summary:")
    print(f"Total files: {codebase_index['summary']['total_files']}")
    print(f"Total directories: {codebase_index['summary']['total_directories']}")
    print(f"File types: {codebase_index['summary']['file_types']}")
    
    return codebase_index

if __name__ == "__main__":
    # Get current directory
    current_dir = os.getcwd()
    
    # Define output file
    output_file = os.path.join(current_dir, "codebase_index.json")
    
    # Index the codebase
    index_codebase(current_dir, output_file)