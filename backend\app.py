# app.py
import json
from flask import (
    Flask, request, jsonify, render_template, redirect, url_for, session, Response
)
from flask_cors import CORS
from waitress import serve
import logging
import os
import bcrypt
import datetime
from dotenv import load_dotenv
from http import HTTPStatus
from modules.paraphrasing import paraphrase_handler
from modules.grammar import grammar_check_handler
from modules.detect_ai import detect_ai_text
from modules.chat_mgt import (
    list_chat_histories,
    load_chat_history,
    create_new_chat,
    save_chat_history
)
from modules.auth import create_session, get_db_connection, register_user, login_user
from modules.api_calls import call_openrouter_api, call_openrouter_api_stream
from modules.scholarship import (
    get_essay_types,
    get_scholarship_categories,
    get_initial_suggestion_prompts,
    get_follow_up_suggestions,
    get_follow_up_examples,
    get_prompts
)
from modules.profile import get_user_profile, update_user_profile

load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv("SECRET_KEY", "your_secret_key_here")

CORS(
    app,
    origins=os.getenv("ALLOWED_ORIGINS", "http://localhost:3000"),
    supports_credentials=True
)

# Ensure logs directory exists before any imports that use logging
LOG_DIR = os.path.join(os.path.dirname(__file__), "logs")
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)
LOG_FILE = os.path.join(LOG_DIR, "app.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Utility functions from chat.py
def get_api_key():
    return os.getenv("OPENROUTER_API_KEY")

def require_login():
    session_id = request.cookies.get('session_id')
    if not session_id:
        logger.warning("🔒 Missing session_id cookie")
        return jsonify({"error": "You must log in to access this endpoint"}), 401

    conn = get_db_connection()
    with conn.cursor() as cur:
        cur.execute("""
            SELECT user_id, data FROM sessions 
            WHERE session_id = %s AND expires_at > %s;
        """, (session_id, datetime.datetime.now(datetime.UTC)))
        result = cur.fetchone()
        conn.close()

    if result:
        user_id, data = result
        try:
            parsed_data = json.loads(data) if isinstance(data, str) else data
        except Exception as e:
            logger.error("❌ Failed to parse session data: %s", str(e))
            return jsonify({"error": "Invalid session"}), 500

        request.user_id = user_id
        request.user_data = parsed_data
        return None

    logger.warning("🔐 Session invalid or expired: %s", session_id)
    return jsonify({"error": "You must log in to access this endpoint"}), 401

@app.route("/register", methods=["POST"])
def register():
    try:
        data = request.get_json()
        username = data.get("username")
        password = data.get("password")
        first_name = data.get("firstName")
        last_name = data.get("lastName")

        logger.info(f"📨 Register received for: {username}")

        # Validate input
        errors = {}
        if not username:
            errors["username"] = ["Email address is required"]
        elif not username.endswith("@gmail.com"):
            errors["username"] = ["Please use a valid Gmail address (@gmail.com)"]
        
        if not password:
            errors["password"] = ["Password is required"]
        elif len(password) < 8:
            errors["password"] = ["Password must be at least 8 characters long"]
        elif not any(c.isupper() for c in password):
            errors["password"] = ["Password must contain at least one uppercase letter"]
        elif not any(c.islower() for c in password):
            errors["password"] = ["Password must contain at least one lowercase letter"]
        elif not any(c.isdigit() for c in password):
            errors["password"] = ["Password must contain at least one number"]
        
        if not first_name:
            errors["firstName"] = ["First name is required"]
        
        if not last_name:
            errors["lastName"] = ["Last name is required"]
        
        if errors:
            return jsonify({
                "error": "Validation failed",
                "errors": errors
            }), HTTPStatus.BAD_REQUEST

        response, success = register_user(username, password, first_name, last_name)

        if success:
            session_id = create_session(response["user_id"], response["username"], response["role"])
            resp = jsonify({"message": "Registered successfully", "user_id": response["user_id"]})
            resp.set_cookie('session_id', session_id, httponly=True, samesite='Lax', max_age=604800)
            logger.info(f"✅ Registered user {username}, session ID: {session_id}")
            return resp, HTTPStatus.CREATED
        else:
            logger.warning(f"❌ Registration conflict: {response}")
            return jsonify({"error": response.get("error")}), HTTPStatus.CONFLICT

    except Exception as e:
        import traceback
        logger.error("❌ Exception in /register:")
        logger.error(traceback.format_exc())
        return jsonify({"error": "Internal server error"}), HTTPStatus.INTERNAL_SERVER_ERROR

@app.route("/login", methods=["POST"])
def login():
    try:
        data = request.get_json()
        username = data.get("username")
        password = data.get("password")

        if not username or not password:
            return jsonify({"error": "Username and password are required"}), HTTPStatus.BAD_REQUEST

        user_data, success = login_user(username, password)
        if success:
            session["user_id"] = user_data["user_id"]
            session["logged_in"] = True
            session["username"] = username

            session_id = create_session(user_data["user_id"], username, user_data.get("role", "user"))
            resp = jsonify(user_data)
            resp.set_cookie(
                'session_id',
                session_id,
                httponly=True,
                samesite='Lax',
                max_age=604800
            )

            logger.info("User %s logged in successfully via API.", username)
            return resp, HTTPStatus.OK
        else:
            return jsonify({"error": user_data.get("error", "Invalid credentials")}), HTTPStatus.UNAUTHORIZED
    except Exception as e:
        logger.error("Error in api_login endpoint: %s", str(e))
        return jsonify({"error": "Internal server error"}), HTTPStatus.INTERNAL_SERVER_ERROR

@app.route('/logout', methods=['POST', 'GET'])
def logout():
    try:
        session_id = request.cookies.get('session_id')
        logger.info(f"Logout attempt with session_id: {session_id}")
        
        # Even if no session_id, we'll still return success
        if session_id:
            # Delete the session from database
            conn = get_db_connection()
            with conn.cursor() as cur:
                cur.execute("DELETE FROM sessions WHERE session_id = %s", (session_id,))
                deleted_rows = cur.rowcount
                logger.info(f"Deleted {deleted_rows} session rows")
            conn.commit()
            conn.close()
        else:
            logger.warning("Logout attempt with no session_id cookie")
            
        # Clear Flask session
        session.clear()
        
        # Clear the cookie
        resp = jsonify({"message": "Logged out successfully"})
        resp.set_cookie('session_id', '', expires=0, path='/', domain=None, httponly=True, samesite='Lax')
        
        logger.info("User logged out successfully.")
        return resp, 200
        
    except Exception as e:
        logger.error(f"Error in logout endpoint: {str(e)}", exc_info=True)
        # Still return 200 to client to avoid issues
        resp = jsonify({"message": "Logged out successfully", "note": "Error occurred but proceeding with logout"})
        resp.set_cookie('session_id', '', expires=0, path='/', domain=None, httponly=True, samesite='Lax')
        return resp, 200

@app.route('/check-auth', methods=['GET'])
def check_auth():
    """Check if the user is authenticated and return basic user info"""
    if session.get("logged_in"):
        return jsonify({
            "authenticated": True,
            "username": session.get("username"),
            "user_id": session.get("user_id")
        }), HTTPStatus.OK
    else:
        return jsonify({"authenticated": False}), HTTPStatus.OK

@app.route('/')
def index():
    if not session.get("logged_in"):
        return redirect(url_for("login"))
    return render_template("index.html", username=session.get("username"))

@app.route('/paraphrasing', methods=['POST'])
def paraphrase():
    """Paraphrase endpoint to handle text paraphrasing requests."""
    try:
        data = request.get_json()
        result, status = paraphrase_handler(data)
        return jsonify(result), status
    except Exception as e:
        logger.error("Error in paraphrase endpoint: %s", str(e))
        return jsonify({"error": "Internal server error"}), HTTPStatus.INTERNAL_SERVER_ERROR

@app.route('/grammar-check', methods=['POST'])
def grammar_check():
    try:
        data = request.get_json()
        result, status = grammar_check_handler(data)
        return jsonify(result), status
    except Exception as e:
        logger.error("Error in grammar-check endpoint: %s", str(e))
        return jsonify({"error": "Internal server error"}), HTTPStatus.INTERNAL_SERVER_ERROR

@app.route('/detect-ai', methods=['POST'])
def detect_ai():
    try:
        data = request.get_json()
        text = data.get('text', '')
        if len(text.strip()) <= 20:
            return jsonify({"error": "Text must be at least 20 characters"}), HTTPStatus.BAD_REQUEST
        
        result, status = detect_ai_text(text)
        return jsonify(result), status
    except Exception as e:
        logger.error("Error in detect-ai endpoint: %s", str(e))
        return jsonify({"error": "Internal server error"}), HTTPStatus.INTERNAL_SERVER_ERROR

@app.route('/chats', methods=['GET', 'POST'])
def chats():
    if request.method == 'GET':
        user_id = request.args.get('user_id')
        if not user_id:
            return jsonify({"error": "user_id is required"}), HTTPStatus.BAD_REQUEST
        sessions = list_chat_histories(user_id)
        result = [{"id": cid, "title": title} for cid, title in sessions]
        return jsonify(result), HTTPStatus.OK
    elif request.method == 'POST':
        data = request.get_json()
        if not data or "user_id" not in data:
            return jsonify({"error": "user_id is required"}), HTTPStatus.BAD_REQUEST
        user_id = data.get('user_id')
        title = data.get('title', 'Untitled Chat')
        new_chat_id = create_new_chat(user_id, title)
        logger.info("New chat created with ID %s for user %s", new_chat_id, user_id)
        return jsonify({"id": new_chat_id, "title": title}), HTTPStatus.CREATED

@app.route('/chat/<chat_id>', methods=['GET'])
def get_chat(chat_id):
    logger.info("Received chat_id: %s", chat_id)
    auth_check = require_login()
    if auth_check:
        return auth_check
    try:
        chat_id = int(chat_id)
        messages = load_chat_history(chat_id, user_id=request.user_id)
        return jsonify({"messages": messages}), HTTPStatus.OK
    except ValueError:
        logger.error("Invalid chat_id: %s", chat_id)
        return jsonify({"error": "Chat ID must be a valid integer"}), HTTPStatus.BAD_REQUEST
    except Exception as e:
        logger.error("Error loading chat %s: %s", chat_id, str(e))
        return jsonify({"error": "Failed to load chat"}), HTTPStatus.INTERNAL_SERVER_ERROR

@app.route('/chats/save', methods=['POST'])
def save_chat():
    auth_check = require_login()
    if auth_check:
        return auth_check
    try:
        data = request.get_json()
        chat_id = data.get('chatId')
        messages = data.get('messages')
        if not chat_id or messages is None:
            return jsonify({"error": "chatId and messages are required"}), HTTPStatus.BAD_REQUEST
        save_chat_history(chat_id, messages, request.user_id)
        logger.info("Chat %s saved successfully", chat_id)
        return jsonify({"status": "success"}), HTTPStatus.OK
    except Exception as e:
        logger.error("Error saving chat %s: %s", chat_id, str(e))
        return jsonify({"error": "Failed to save chat"}), HTTPStatus.INTERNAL_SERVER_ERROR

# --- OpenRouter Chat Generation ---
def get_chat_instruction(prompt):
    system_instruction = (
        "You are a helpful assistant designed to assist with scholarship essay writing. "
        "Respond to the user's prompt in a clear, concise, and supportive manner. "
        "Return the response in this strict JSON format:\n"
        "```json\n"
        "{\n"
        "  \"reply\": \"your response here\"\n"
        "}\n"
        "```"
    )
    return prompt, system_instruction

@app.route('/generate', methods=['POST'])
def generate():
    try:
        data = request.get_json()
        prompt = data.get('prompt')
        if not prompt:
            return jsonify({"error": "prompt is required"}), HTTPStatus.BAD_REQUEST
        
        prompt, system_instruction = get_chat_instruction(prompt)
        response, status = call_openrouter_api(prompt, system_instruction)
        return jsonify(response), status
    except Exception as e:
        logger.error("Error in generate endpoint: %s", str(e))
        return jsonify({"error": "Internal server error"}), HTTPStatus.INTERNAL_SERVER_ERROR

@app.route('/generate/stream', methods=['POST'])
def generate_stream():
    try:
        data = request.get_json()
        prompt = data.get('prompt')
        if not prompt:
            return jsonify({"error": "prompt is required"}), HTTPStatus.BAD_REQUEST
        
        prompt, system_instruction = get_chat_instruction(prompt)

        def stream():
            for chunk in call_openrouter_api_stream(prompt, system_instruction):
                yield f"data: {chunk}\n\n"

        return Response(stream(), mimetype="text/event-stream")
    except Exception as e:
        logger.error("Error in generate/stream endpoint: %s", str(e))
        return jsonify({"error": "Internal server error"}), HTTPStatus.INTERNAL_SERVER_ERROR

# Scholarship-specific endpoints from chat.py
@app.route("/chat/list", methods=["GET"])
def get_chat_list():
    auth_check = require_login()
    if auth_check:
        return auth_check
    user_id = request.user_id
    chat_sessions = list_chat_histories(user_id)
    return jsonify({"chat_sessions": chat_sessions}), 200

@app.route("/chat/new", methods=["POST"])
def create_chat_endpoint():
    auth_check = require_login()
    if auth_check:
        return auth_check
    user_id = request.user_id
    data = request.get_json() or {}
    title = data.get("title", f"Chat {len(list_chat_histories(user_id)) + 1}")
    new_chat_id = create_new_chat(user_id, title)
    return jsonify({"chat_id": new_chat_id}), 201

@app.route("/chat/<chat_id>/submit", methods=["POST"])
def submit_message(chat_id):
    auth_check = require_login()
    if auth_check:
        return auth_check
    
    user_id = request.user_id
    data = request.get_json() or {}
    user_input = data.get("input")
    selected_scholarship = data.get("scholarship")
    selected_category = data.get("category")
    prompt_text = data.get("prompt_text")

    if not user_input or len(user_input.strip()) < 20:
        return jsonify({"error": "Input must be at least 20 characters"}), 400

    api_key = get_api_key()
    if not api_key:
        return jsonify({"error": "Missing OpenRouter API key"}), 500

    try:
        messages = load_chat_history(int(chat_id), user_id)
        messages.append({"role": "user", "content": user_input})
        if prompt_text:
            messages.insert(0, {"role": "system", "content": prompt_text})

        def generate():
            full_response = ""
            for chunk in call_openrouter_api_stream(user_input, "You are a helpful writing assistant for scholarship essays."):
                full_response += chunk
                yield chunk
            messages.append({"role": "assistant", "content": full_response})
            save_chat_history(int(chat_id), messages, user_id, selected_scholarship, selected_category)

        return Response(generate(), mimetype='text/plain')
    except Exception as e:
        logger.error("Error submitting message for chat %s: %s", chat_id, str(e))
        return jsonify({"error": "Failed to submit message"}), HTTPStatus.INTERNAL_SERVER_ERROR

@app.route("/chat/<chat_id>/followup", methods=["POST"])
def submit_followup(chat_id):
    auth_check = require_login()
    if auth_check:
        return auth_check
    
    user_id = request.user_id
    data = request.get_json() or {}
    follow_up_input = data.get("input")
    follow_up_prompt = data.get("prompt_text", "Please elaborate further.")
    selected_scholarship = data.get("scholarship")
    selected_category = data.get("category")

    if not follow_up_input or len(follow_up_input.strip()) < 10:
        return jsonify({"error": "Input must be at least 10 characters"}), 400

    api_key = get_api_key()
    if not api_key:
        return jsonify({"error": "Missing OpenRouter API key"}), 500

    try:
        messages = load_chat_history(int(chat_id), user_id)
        messages.append({"role": "user", "content": follow_up_input})
        if follow_up_prompt:
            messages.insert(0, {"role": "system", "content": follow_up_prompt})

        def generate():
            full_response = ""
            for chunk in call_openrouter_api_stream(follow_up_input, "You are a helpful writing assistant for scholarship essays."):
                full_response += chunk
                yield chunk
            messages.append({"role": "assistant", "content": full_response})
            save_chat_history(int(chat_id), messages, user_id)

        return Response(generate(), mimetype='text/plain')
    except Exception as e:
        logger.error("Error submitting followup for chat %s: %s", chat_id, str(e))
        return jsonify({"error": "Failed to submit followup"}), HTTPStatus.INTERNAL_SERVER_ERROR

@app.route('/prompts', methods=['GET'])
def get_prompts_data():
    categories, cat_success = get_scholarship_categories()
    initial_prompts, init_success = get_initial_suggestion_prompts()
    follow_up_sugs, sug_success = get_follow_up_suggestions()
    follow_up_exs, ex_success = get_follow_up_examples()
    essay_types, essay_success = get_essay_types()

    if not all([cat_success, init_success, sug_success, ex_success, essay_success]):
        return jsonify({"error": "Failed to load prompt data"}), 500

    return jsonify({
        "scholarship_categories": categories,
        "initial_suggestion_prompts": initial_prompts,
        "essay_types": essay_types,
        "follow_up_suggestions": follow_up_sugs,
        "follow_up_examples": follow_up_exs
    }), 200

@app.route('/profile', methods=['GET'])
def profile():
    """Get the current user's profile information"""
    if not session.get("logged_in"):
        return jsonify({"error": "Not authenticated"}), HTTPStatus.UNAUTHORIZED
    
    user_id = session.get("user_id")
    if not user_id:
        return jsonify({"error": "User ID not found in session"}), HTTPStatus.UNAUTHORIZED
    
    user_data, status = get_user_profile(user_id)
    return jsonify(user_data), status

@app.route('/profile', methods=['PUT'])
def update_profile():
    """Update the current user's profile information"""
    if not session.get("logged_in"):
        return jsonify({"error": "Not authenticated"}), HTTPStatus.UNAUTHORIZED
    
    user_id = session.get("user_id")
    if not user_id:
        return jsonify({"error": "User ID not found in session"}), HTTPStatus.UNAUTHORIZED
    
    try:
        update_data = request.get_json()
        if not update_data:
            return jsonify({"error": "No data provided"}), HTTPStatus.BAD_REQUEST
        
        result, status = update_user_profile(user_id, update_data)
        return jsonify(result), status
    except Exception as e:
        logger.error(f"Error in update_profile endpoint: {str(e)}")
        return jsonify({"error": "Internal server error"}), HTTPStatus.INTERNAL_SERVER_ERROR

if __name__ == '__main__':
    logger.info("Starting server on port %s", os.getenv("PORT", 5000))
    debug_mode = os.getenv("FLASK_DEBUG", "False").lower() in ("true", "1", "t")
    if debug_mode:
        app.run(host='0.0.0.0', port=int(os.getenv("PORT", 5000)), debug=True)
    else:
        serve(app, host='0.0.0.0', port=int(os.getenv("PORT", 5000)), threads=4)
