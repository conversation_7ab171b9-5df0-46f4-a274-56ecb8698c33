# modules/detect-ai.py
import requests
from http import HTTPStatus
import logging
from modules.config import Config

logger = logging.getLogger(__name__)

EDENAI_URL = "https://api.edenai.run/v2/text/ai_detection"
api_eden = Config.EDENAI_API_KEY

def detect_ai_text(text):
    """Calls EdenAI's AI detection API and returns AI probability score with sentence analysis."""
    headers = {"Authorization": f"Bearer {api_eden}"}
    payload = {"providers": "sapling", "text": text}

    try:
        response = requests.post(EDENAI_URL, json=payload, headers=headers, timeout=Config.API_TIMEOUT)
        response.raise_for_status()
        
        result = response.json()
        logger.debug("Raw API Response from EdenAI: %s", result)

        if 'sapling' not in result or 'items' not in result['sapling']:
            return {"error": "Unexpected API response format"}, HTTPStatus.BAD_REQUEST

        ai_score = result['sapling'].get('ai_score', 0) * 100
        human_score = 100 - ai_score

        sentence_results = [
            {
                "text": item['text'],
                "ai_score": item['ai_score'] * 100,
                "prediction": item['prediction']
            }
            for item in result['sapling']['items']
        ]

        if ai_score > 75:
            classification = "AI-Generated"
        elif ai_score < 40:
            classification = "Human-Written"
        else:
            classification = "Paraphrased"

        return {
            "ai_score": ai_score,
            "human_score": human_score,
            "classification": classification,
            "sentences": sentence_results
        }, HTTPStatus.OK

    except requests.exceptions.RequestException as e:
        logger.error("EdenAI API Error: %s", str(e))
        return {"error": f"API Error: {str(e)}"}, HTTPStatus.SERVICE_UNAVAILABLE