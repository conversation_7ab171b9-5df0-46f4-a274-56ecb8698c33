# modules/profile.py
import logging
import bcrypt
from http import HTTPStatus
from modules.auth import get_db_connection
import psycopg2.extras

logger = logging.getLogger(__name__)

def get_user_profile(user_id):
    """
    Retrieve user profile information from the database.
    """
    try:
        conn = get_db_connection()
        # Use DictCursor instead of dictionary=True
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        query = """
        SELECT user_id, username, first_name, last_name, role, created_at
        FROM users
        WHERE user_id = %s
        """
        
        cursor.execute(query, (user_id,))
        user_data = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        if not user_data:
            return {"error": "User not found"}, HTTPStatus.NOT_FOUND
        
        # Convert DictRow to regular dict for JSON serialization
        user_dict = dict(user_data)
        return user_dict, HTTPStatus.OK
    
    except Exception as e:
        logger.error(f"Error retrieving user profile: {str(e)}")
        return {"error": "Failed to retrieve user profile"}, HTTPStatus.INTERNAL_SERVER_ERROR

def update_user_profile(user_id, update_data):
    """
    Update user profile information in the database.
    
    Args:
        user_id: The ID of the user to update
        update_data: Dictionary containing fields to update, which may include:
            - first_name: User's first name
            - last_name: User's last name
            - current_password: Current password (required for password change)
            - new_password: New password (if changing password)
    
    Returns:
        Tuple of (response_data, status_code)
    """
    try:
        conn = get_db_connection()
        # Use DictCursor instead of dictionary=True
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # First, verify the user exists
        cursor.execute("SELECT * FROM users WHERE user_id = %s", (user_id,))
        user = cursor.fetchone()
        
        if not user:
            cursor.close()
            conn.close()
            return {"error": "User not found"}, HTTPStatus.NOT_FOUND
        
        # Handle password change if requested
        if 'new_password' in update_data and 'current_password' in update_data:
            # Verify current password
            if not bcrypt.checkpw(update_data['current_password'].encode('utf-8'), 
                                 user['password'].encode('utf-8')):
                cursor.close()
                conn.close()
                return {"error": "Current password is incorrect"}, HTTPStatus.UNAUTHORIZED
            
            # Hash the new password
            hashed_password = bcrypt.hashpw(
                update_data['new_password'].encode('utf-8'), 
                bcrypt.gensalt()
            ).decode('utf-8')
            
            # Update password
            cursor.execute(
                "UPDATE users SET password = %s WHERE user_id = %s",
                (hashed_password, user_id)
            )
        
        # Update profile information
        update_fields = []
        update_values = []
        
        if 'first_name' in update_data:
            update_fields.append("first_name = %s")
            update_values.append(update_data['first_name'])
        
        if 'last_name' in update_data:
            update_fields.append("last_name = %s")
            update_values.append(update_data['last_name'])
        
        if update_fields:
            query = f"UPDATE users SET {', '.join(update_fields)} WHERE user_id = %s"
            update_values.append(user_id)
            
            cursor.execute(query, tuple(update_values))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return {"message": "Profile updated successfully"}, HTTPStatus.OK
    
    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        return {"error": "Failed to update user profile"}, HTTPStatus.INTERNAL_SERVER_ERROR



