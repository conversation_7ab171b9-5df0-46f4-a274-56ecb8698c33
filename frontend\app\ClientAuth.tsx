'use client';

import React, { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from './context/AuthContext';

export default function ClientAuth({ children }: { children: React.ReactNode }) {
  const { user, isLoading, view, openAuth } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Skip auth check for public routes
    const publicRoutes = ['/login', '/register', '/about'];
    if (publicRoutes.includes(pathname)) {
      return;
    }

    // If not loading and no user and no auth modal open, redirect to login
    if (!isLoading && !user && !view) {
      // For a better UX, open the auth modal instead of redirecting
      openAuth('login');
    }
  }, [user, isLoading, pathname, router, view, openAuth]);

  return <>{children}</>;
}
